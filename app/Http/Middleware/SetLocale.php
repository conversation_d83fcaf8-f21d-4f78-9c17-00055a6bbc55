<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
	/**
	 * Handle an incoming request.
	 */
	public function handle(Request $request, Closure $next): Response
	{
        $locale = session('locale') ?? $request->cookie('locale');
        $fallback = config('app.locale');

        if (!is_string($locale) || !in_array($locale, ['en', 'ar'], true)) {
            $locale = $fallback;
        }

        app()->setLocale($locale);

		return $next($request);
	}
}
