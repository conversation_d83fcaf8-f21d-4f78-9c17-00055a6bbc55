<?php

namespace App\Http\Requests\Frontend;

use Illuminate\Foundation\Http\FormRequest;

class ContactRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255'],
            'phone' => ['nullable', 'string', 'max:20'],
            'subject' => ['required', 'string', 'max:255'],
            'inquiry_type' => ['required', 'string', 'in:general,submission,services,events,partnership'],
            'message' => ['required', 'string', 'min:10', 'max:2000'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => __('frontend.contact.name'),
            'email' => __('frontend.contact.email'),
            'phone' => __('frontend.contact.phone'),
            'subject' => __('frontend.contact.subject'),
            'inquiry_type' => __('frontend.contact.inquiry_type'),
            'message' => __('frontend.contact.message'),
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'inquiry_type.in' => __('frontend.contact.inquiry_type_invalid'),
            'message.min' => __('frontend.contact.message_too_short'),
        ];
    }
}
