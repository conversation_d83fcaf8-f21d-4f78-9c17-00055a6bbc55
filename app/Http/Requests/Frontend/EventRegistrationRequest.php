<?php

namespace App\Http\Requests\Frontend;

use Illuminate\Foundation\Http\FormRequest;

class EventRegistrationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // Personal Information
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255'],
            'phone' => ['required', 'string', 'max:20'],

            // Professional Information
            'profession' => ['required', 'string', 'max:255'],
            'organization' => ['nullable', 'string', 'max:255'],

            // Special Requirements
            'dietary_restrictions' => ['nullable', 'string', 'max:500'],
            'special_requirements' => ['nullable', 'string', 'max:500'],

            // Emergency Contact
            'emergency_contact' => ['required', 'string', 'max:255'],
            'emergency_phone' => ['required', 'string', 'max:20'],

            // Additional Information
            'how_did_you_hear' => ['nullable', 'string', 'in:website,social_media,email,friend,advertisement,other'],
            'comments' => ['nullable', 'string', 'max:1000'],

            // Terms and Conditions
            'agree_terms' => ['required', 'accepted'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => __('frontend.events.name'),
            'email' => __('frontend.events.email'),
            'phone' => __('frontend.events.phone'),
            'profession' => __('frontend.events.profession'),
            'organization' => __('frontend.events.organization'),
            'dietary_restrictions' => __('frontend.events.dietary_restrictions'),
            'special_requirements' => __('frontend.events.special_requirements'),
            'emergency_contact' => __('frontend.events.emergency_contact'),
            'emergency_phone' => __('frontend.events.emergency_phone'),
            'how_did_you_hear' => __('frontend.events.how_did_you_hear'),
            'comments' => __('frontend.events.comments'),
            'agree_terms' => __('frontend.events.agree_terms'),
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'how_did_you_hear.in' => __('frontend.events.how_did_you_hear_invalid'),
        ];
    }
}
