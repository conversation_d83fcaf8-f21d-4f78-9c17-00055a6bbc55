<?php

namespace App\Http\Requests\Frontend;

use Illuminate\Foundation\Http\FormRequest;

class SubmissionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // Author Information
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'email', 'max:255'],
            'phone' => ['nullable', 'string', 'max:20'],
            'biography' => ['required', 'string', 'min:50', 'max:1000'],
            'previous_publications' => ['nullable', 'string', 'max:1000'],

            // Manuscript Information
            'title' => ['required', 'string', 'max:255'],
            'genre' => ['required', 'string', 'in:fiction,non-fiction,poetry,children,young-adult,mystery,romance,sci-fi,fantasy,biography,memoir,self-help,business,history,other'],
            'word_count' => ['required', 'integer', 'min:10000', 'max:200000'],
            'description' => ['required', 'string', 'min:100', 'max:2000'],
            'target_audience' => ['required', 'string', 'max:500'],
            'marketing_plan' => ['nullable', 'string', 'max:1000'],

            // File Uploads
            'manuscript' => ['required', 'file', 'mimes:pdf,doc,docx', 'max:10240'], // 10MB max
            'synopsis' => ['nullable', 'file', 'mimes:pdf,doc,docx', 'max:2048'], // 2MB max
            'cover_letter' => ['nullable', 'file', 'mimes:pdf,doc,docx', 'max:2048'], // 2MB max

            // Terms and Conditions
            'agree_terms' => ['required', 'accepted'],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name' => __('frontend.submissions.name'),
            'email' => __('frontend.submissions.email'),
            'phone' => __('frontend.submissions.phone'),
            'biography' => __('frontend.submissions.biography'),
            'previous_publications' => __('frontend.submissions.previous_publications'),
            'title' => __('frontend.submissions.title'),
            'genre' => __('frontend.submissions.genre'),
            'word_count' => __('frontend.submissions.word_count'),
            'description' => __('frontend.submissions.description'),
            'target_audience' => __('frontend.submissions.target_audience'),
            'marketing_plan' => __('frontend.submissions.marketing_plan'),
            'manuscript' => __('frontend.submissions.manuscript'),
            'synopsis' => __('frontend.submissions.synopsis'),
            'cover_letter' => __('frontend.submissions.cover_letter'),
            'agree_terms' => __('frontend.submissions.agree_terms'),
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'word_count.min' => __('frontend.submissions.word_count_too_low'),
            'word_count.max' => __('frontend.submissions.word_count_too_high'),
            'manuscript.max' => __('frontend.submissions.manuscript_too_large'),
            'synopsis.max' => __('frontend.submissions.synopsis_too_large'),
            'cover_letter.max' => __('frontend.submissions.cover_letter_too_large'),
            'biography.min' => __('frontend.submissions.biography_too_short'),
            'description.min' => __('frontend.submissions.description_too_short'),
        ];
    }
}
