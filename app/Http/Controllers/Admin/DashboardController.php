<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Repositories\Admin\AdminRepository;
use App\Repositories\Admin\RoleRepository;
use App\Repositories\Admin\ModuleRepository;
use App\Repositories\Admin\UserRepository;
use Illuminate\Contracts\View\View;

class DashboardController extends Controller
{

    public function __construct(
        protected RoleRepository $roleRepository,
        protected AdminRepository $adminRepository,
        protected ModuleRepository $moduleRepository,
        protected UserRepository $userRepository
    ) {
        $this->middleware('auth:admin');
    }

    public function index():View
    {
        $users = $this->userRepository->getModel()->paginate(10);
        $totalUsers = $this->userRepository->getModel()->count();
        $admins = $this->adminRepository->all();
        $roles = $this->roleRepository->getModel()->withCount('admins')->get();
        $modules = $this->moduleRepository->getModel()->count();

        return view('admin.dashboard', compact('users', 'totalUsers', 'admins', 'roles', 'modules'));
    }
}
