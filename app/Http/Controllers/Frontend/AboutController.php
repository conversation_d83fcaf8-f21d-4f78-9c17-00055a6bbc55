<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\View\View;
use Modules\Page\app\Repositories\PageRepository;

class AboutController extends Controller
{
    protected $pageRepository;

    public function __construct(
        PageRepository $pageRepository,
    ) {
        $this->pageRepository = $pageRepository;
    }
    /**
     * Display the about page.
     */
    public function index(): View
    {
        $aboutPage = $this->pageRepository->getPageWithActiveSectionsBySlug('about-us');
        $aboutSections = $aboutPage ? $aboutPage->sections : collect();

        return view('frontend.about.index', [
            'aboutSections' => $aboutSections,
        ]);
    }
}
