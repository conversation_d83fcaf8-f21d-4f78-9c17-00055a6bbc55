<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\View\View;

class TestimonialController extends Controller
{
    /**
     * Display all testimonials.
     */
    public function index(): View
    {
        // Get all published testimonials
        // $testimonials = Testimonial::where('status', 'published')->latest()->paginate(12);
        // $featuredTestimonials = Testimonial::where('status', 'published')->where('is_featured', true)->latest()->limit(6)->get();

        return view('frontend.testimonials.index', [
            // 'testimonials' => $testimonials,
            // 'featuredTestimonials' => $featuredTestimonials,
        ]);
    }
}
