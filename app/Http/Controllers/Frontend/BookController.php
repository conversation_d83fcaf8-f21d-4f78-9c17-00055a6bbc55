<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;

class BookController extends Controller
{
    /**
     * Display all books with pagination and filtering.
     */
    public function index(Request $request): View
    {
        // $query = Book::with('author')->where('status', 'published');

        // Apply search filter if provided
        // if ($request->filled('search')) {
        //     $query->where(function ($q) use ($request) {
        //         $q->where('title', 'like', '%' . $request->search . '%')
        //           ->orWhere('description', 'like', '%' . $request->search . '%')
        //           ->orWhereHas('author', function ($authorQuery) use ($request) {
        //               $authorQuery->where('name', 'like', '%' . $request->search . '%');
        //           });
        //     });
        // }

        // Apply genre filter if provided
        // if ($request->filled('genre')) {
        //     $query->where('genre', $request->genre);
        // }

        // Apply sorting
        // $sortBy = $request->get('sort', 'latest');
        // match ($sortBy) {
        //     'title' => $query->orderBy('title'),
        //     'author' => $query->join('authors', 'books.author_id', '=', 'authors.id')->orderBy('authors.name'),
        //     'publication_date' => $query->orderBy('publication_date', 'desc'),
        //     default => $query->latest(),
        // };

        // $books = $query->paginate(12);
        // $genres = Book::distinct()->pluck('genre')->filter();

        return view('frontend.books.index', [
            // 'books' => $books,
            // 'genres' => $genres,
            // 'currentSearch' => $request->search,
            // 'currentGenre' => $request->genre,
            // 'currentSort' => $sortBy,
        ]);
    }

    /**
     * Display a specific book details.
     */
    public function show(string $bookSlug): View
    {
        // Get book with author and related books
        // $book = Book::with(['author', 'author.books' => function ($query) use ($bookSlug) {
        //     $query->where('status', 'published')
        //           ->where('slug', '!=', $bookSlug)
        //           ->limit(4);
        // }])->where('slug', $bookSlug)->where('status', 'published')->firstOrFail();

        return view('frontend.books.show', [
            // 'book' => $book,
        ]);
    }
}
