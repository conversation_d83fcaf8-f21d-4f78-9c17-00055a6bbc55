<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\SubmissionRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class SubmissionController extends Controller
{
    /**
     * Display the submission guidelines page.
     */
    public function index(): View
    {
        return view('frontend.submissions.index');
    }

    /**
     * Store the manuscript submission.
     */
    public function store(SubmissionRequest $request): RedirectResponse
    {
        // Handle file upload
        // $manuscriptPath = $request->file('manuscript')->store('submissions/manuscripts', 'private');
        // $synopsisPath = $request->hasFile('synopsis') ? $request->file('synopsis')->store('submissions/synopsis', 'private') : null;
        // $coverLetterPath = $request->hasFile('cover_letter') ? $request->file('cover_letter')->store('submissions/cover-letters', 'private') : null;

        // Create the submission
        // Submission::create([
        //     'name' => $request->name,
        //     'email' => $request->email,
        //     'phone' => $request->phone,
        //     'title' => $request->title,
        //     'genre' => $request->genre,
        //     'word_count' => $request->word_count,
        //     'description' => $request->description,
        //     'biography' => $request->biography,
        //     'previous_publications' => $request->previous_publications,
        //     'manuscript_path' => $manuscriptPath,
        //     'synopsis_path' => $synopsisPath,
        //     'cover_letter_path' => $coverLetterPath,
        //     'target_audience' => $request->target_audience,
        //     'marketing_plan' => $request->marketing_plan,
        //     'status' => 'submitted',
        // ]);

        // Send confirmation email
        // Mail::to($request->email)->send(new SubmissionReceived($request->validated()));

        // Send notification to admin
        // Mail::to(config('mail.admin_email'))->send(new NewSubmissionNotification($request->validated()));

        return redirect()->route('submissions.success')
                        ->with('success', __('messages.submission_success'));
    }

    /**
     * Show the submission success page.
     */
    public function success(): View
    {
        return view('frontend.submissions.success');
    }
}
