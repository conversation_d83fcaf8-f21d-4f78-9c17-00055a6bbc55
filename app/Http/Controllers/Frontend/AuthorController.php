<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;

class AuthorController extends Controller
{
    /**
     * Display all authors with pagination and filtering.
     */
    public function index(Request $request): View
    {
        // $query = Author::where('status', 'active');

        // Apply search filter if provided
        // if ($request->filled('search')) {
        //     $query->where(function ($q) use ($request) {
        //         $q->where('name', 'like', '%' . $request->search . '%')
        //           ->orWhere('bio', 'like', '%' . $request->search . '%');
        //     });
        // }

        // Apply genre filter if provided
        // if ($request->filled('genre')) {
        //     $query->whereHas('books', function ($q) use ($request) {
        //         $q->where('genre', $request->genre);
        //     });
        // }

        // $authors = $query->latest()->paginate(12);
        // $genres = Book::distinct()->pluck('genre')->filter();

        return view('frontend.authors.index', [
            // 'authors' => $authors,
            // 'genres' => $genres,
            // 'currentSearch' => $request->search,
            // 'currentGenre' => $request->genre,
        ]);
    }

    /**
     * Display a specific author profile.
     */
    public function show(string $authorSlug): View
    {
        // Get author with their books
        // $author = Author::with(['books' => function ($query) {
        //     $query->where('status', 'published')->latest();
        // }])->where('slug', $authorSlug)->where('status', 'active')->firstOrFail();

        return view('frontend.authors.show', [
            // 'author' => $author,
        ]);
    }
}
