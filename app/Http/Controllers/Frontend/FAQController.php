<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Repositories\Frontend\FAQRepository;
use Illuminate\Http\Request;
use Illuminate\View\View;

class FAQController extends Controller
{
    protected $faqRepository;

    public function __construct(FAQRepository $faqRepository)
    {
        $this->faqRepository = $faqRepository;
    }

    /**
     * Display the FAQs page.
     */
    public function index(Request $request): View
    {
        // Get all active categories
        $categories = $this->faqRepository->getActiveCategories();

        // Get category slug mapping for frontend tabs
        $categoryMapping = $this->faqRepository->getCategorySlugMapping();

        // Get FAQs grouped by category
        $faqsGroupedByCategory = $this->faqRepository->getFAQsGroupedByCategory();

        // Handle search if provided
        $searchTerm = $request->get('search');
        $categoryFilter = $request->get('category');

        $searchResults = null;
        if ($searchTerm) {
            $categoryId = $categoryFilter && isset($categoryMapping[$categoryFilter])
                ? $categoryMapping[$categoryFilter]
                : null;
            $searchResults = $this->faqRepository->searchFAQs($searchTerm, $categoryId);
        }

        return view('frontend.faq.index', compact(
            'categories',
            'categoryMapping',
            'faqsGroupedByCategory',
            'searchTerm',
            'searchResults'
        ));
    }
}
