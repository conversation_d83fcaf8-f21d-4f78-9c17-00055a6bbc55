<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Modules\News\app\Repositories\NewsRepository;

class NewsController extends Controller
{
    protected $newsRepository;

    public function __construct(
        NewsRepository $newsRepository
    ) {
        $this->newsRepository = $newsRepository;
    }
    /**
     * Display all news articles with pagination and filtering.
     */
    public function index(): View
    {
        $news = $this->newsRepository->getPaginatedPublishedNews(9);
        return view('frontend.news.index', compact('news'));
    }

    /**
     * Display a specific news article.
     */
    public function show(string $newsSlug): View
    {
        $article = $this->newsRepository->getPublishedNewsBySlug($newsSlug);
        abort_unless($article, 404);
        return view('frontend.news.show', compact('article'));
    }
}
