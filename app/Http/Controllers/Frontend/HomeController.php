<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\View\View;
use Modules\Author\app\Repositories\AuthorRepository;
use Modules\Book\app\Repositories\BookRepository;
use Modules\Page\app\Repositories\PageRepository;
use Modules\News\app\Repositories\NewsRepository;

class HomeController extends Controller
{
    protected $bookRepository;
    protected $pageRepository;
    protected $authorRepository;
    protected $newsRepository;

    public function __construct(
        BookRepository $bookRepository,
        PageRepository $pageRepository,
        AuthorRepository $authorRepository,
        NewsRepository $newsRepository
    ) {
        $this->bookRepository = $bookRepository;
        $this->pageRepository = $pageRepository;
        $this->authorRepository = $authorRepository;
        $this->newsRepository = $newsRepository;
    }

    /**
     * Display the homepage with featured content.
     */
    public function index(): View
    {
        $books = $this->bookRepository->getPublishedBooks();
        $homePage = $this->pageRepository->getPageWithActiveSectionsBySlug('home');
        $homeSections = $homePage ? $homePage->sections : collect();
        $authors = $this->authorRepository->getActiveAuthors();
        $latestNews = $this->newsRepository->getLatestNews();

        return view('frontend.home.index', [
            'books' => $books,
            'homeSections' => $homeSections,
            'authors' => $authors,
            'latestNews' => $latestNews
        ]);
    }
}
