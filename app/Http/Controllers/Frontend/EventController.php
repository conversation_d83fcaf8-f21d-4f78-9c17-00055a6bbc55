<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\EventRegistrationRequest;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class EventController extends Controller
{
    /**
     * Display all events with filtering and pagination.
     */
    public function index(Request $request): View
    {
        // $query = Event::where('status', 'published');

        // Apply event type filter if provided
        // if ($request->filled('type')) {
        //     $query->where('event_type', $request->type);
        // }

        // Apply date filter
        // $dateFilter = $request->get('date_filter', 'upcoming');
        // match ($dateFilter) {
        //     'past' => $query->where('event_date', '<', now())->orderBy('event_date', 'desc'),
        //     'today' => $query->whereDate('event_date', today()),
        //     'this_week' => $query->whereBetween('event_date', [now()->startOfWeek(), now()->endOfWeek()]),
        //     'this_month' => $query->whereMonth('event_date', now()->month)->whereYear('event_date', now()->year),
        //     default => $query->where('event_date', '>=', now())->orderBy('event_date'),
        // };

        // $events = $query->paginate(9);
        // $eventTypes = Event::where('status', 'published')->distinct()->pluck('event_type')->filter();
        // $featuredEvents = Event::where('status', 'published')->where('is_featured', true)->where('event_date', '>=', now())->limit(3)->get();

        return view('frontend.events.index', [
            // 'events' => $events,
            // 'eventTypes' => $eventTypes,
            // 'featuredEvents' => $featuredEvents,
            // 'currentType' => $request->type,
            // 'currentDateFilter' => $dateFilter,
        ]);
    }

    /**
     * Display a specific event details.
     */
    public function show(string $eventSlug): View
    {
        // Get event with related events
        // $event = Event::where('slug', $eventSlug)->where('status', 'published')->firstOrFail();
        // $relatedEvents = Event::where('status', 'published')
        //                       ->where('id', '!=', $event->id)
        //                       ->where('event_type', $event->event_type)
        //                       ->where('event_date', '>=', now())
        //                       ->orderBy('event_date')
        //                       ->limit(3)
        //                       ->get();

        return view('frontend.events.show', [
            // 'event' => $event,
            // 'relatedEvents' => $relatedEvents,
        ]);
    }

    /**
     * Show the event registration form.
     */
    public function register(string $eventSlug): View
    {
        // $event = Event::where('slug', $eventSlug)
        //               ->where('status', 'published')
        //               ->where('event_date', '>=', now())
        //               ->where('registration_open', true)
        //               ->firstOrFail();

        return view('frontend.events.register', [
            // 'event' => $event,
        ]);
    }

    /**
     * Store the event registration.
     */
    public function storeRegistration(EventRegistrationRequest $request, string $eventSlug): RedirectResponse
    {
        // $event = Event::where('slug', $eventSlug)
        //               ->where('status', 'published')
        //               ->where('event_date', '>=', now())
        //               ->where('registration_open', true)
        //               ->firstOrFail();

        // Create the registration
        // EventRegistration::create([
        //     'event_id' => $event->id,
        //     'name' => $request->name,
        //     'email' => $request->email,
        //     'phone' => $request->phone,
        //     'profession' => $request->profession,
        //     'organization' => $request->organization,
        //     'dietary_restrictions' => $request->dietary_restrictions,
        //     'special_requirements' => $request->special_requirements,
        //     'emergency_contact' => $request->emergency_contact,
        //     'emergency_phone' => $request->emergency_phone,
        // ]);

        // Send confirmation email
        // Mail::to($request->email)->send(new EventRegistrationConfirmation($event, $request->validated()));

        return redirect()->route('events.registration.success')
                        ->with('success', __('messages.event_registration_success'));
    }

    /**
     * Show the registration success page.
     */
    public function registrationSuccess(): View
    {
        return view('frontend.events.registration-success');
    }
}
