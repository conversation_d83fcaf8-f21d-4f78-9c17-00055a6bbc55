<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\View\View;

class ClientController extends Controller
{
    /**
     * Display all clients.
     */
    public function index(): View
    {
        // Get all active clients
        // $clients = Client::where('status', 'active')->orderBy('sort_order')->get();
        // $featuredClients = Client::where('status', 'active')->where('is_featured', true)->orderBy('sort_order')->limit(8)->get();

        return view('frontend.clients.index', [
            // 'clients' => $clients,
            // 'featuredClients' => $featuredClients,
        ]);
    }
}
