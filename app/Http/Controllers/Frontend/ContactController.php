<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Http\Requests\Frontend\ContactRequest;
use App\Http\Requests\Frontend\NewsletterRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;

class ContactController extends Controller
{
    /**
     * Display the contact page.
     */
    public function index(): View
    {
        return view('frontend.contact.index');
    }

    /**
     * Store the contact inquiry.
     */
    public function store(ContactRequest $request): RedirectResponse
    {
        // Create the contact inquiry
        // Contact::create([
        //     'name' => $request->name,
        //     'email' => $request->email,
        //     'phone' => $request->phone,
        //     'subject' => $request->subject,
        //     'message' => $request->message,
        //     'inquiry_type' => $request->inquiry_type,
        //     'status' => 'new',
        // ]);

        // Send confirmation email to user
        // Mail::to($request->email)->send(new ContactConfirmation($request->validated()));

        // Send notification to admin
        // Mail::to(config('mail.admin_email'))->send(new NewContactInquiry($request->validated()));

        return redirect()->route('contact.success')
                        ->with('success', __('messages.contact_success'));
    }

    /**
     * Show the contact success page.
     */
    public function success(): View
    {
        return view('frontend.contact.success');
    }

    /**
     * Handle newsletter subscription.
     */
    public function newsletter(NewsletterRequest $request): RedirectResponse
    {
        // Check if email already exists
        // $existingSubscriber = NewsletterSubscriber::where('email', $request->email)->first();

        // if ($existingSubscriber) {
        //     if ($existingSubscriber->status === 'unsubscribed') {
        //         $existingSubscriber->update(['status' => 'subscribed', 'subscribed_at' => now()]);
        //     }
        // } else {
        //     NewsletterSubscriber::create([
        //         'email' => $request->email,
        //         'status' => 'subscribed',
        //         'subscribed_at' => now(),
        //     ]);
        // }

        // Send welcome email
        // Mail::to($request->email)->send(new NewsletterWelcome());

        return back()->with('newsletter_success', __('messages.newsletter_success'));
    }
}
