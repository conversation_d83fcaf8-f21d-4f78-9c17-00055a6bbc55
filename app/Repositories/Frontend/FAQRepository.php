<?php

namespace App\Repositories\Frontend;

use Modules\FAQ\app\Models\FAQ;
use Modules\FAQCategory\app\Models\FAQCategory;
use Illuminate\Database\Eloquent\Collection;

class FAQRepository
{
    protected $faqModel;
    protected $categoryModel;

    public function __construct(FAQ $faqModel, FAQCategory $categoryModel)
    {
        $this->faqModel = $faqModel;
        $this->categoryModel = $categoryModel;
    }

    /**
     * Get all active FAQ categories
     *
     * @return Collection
     */
    public function getActiveCategories(): Collection
    {
        return $this->categoryModel->active()
            ->orderBy('category->en')
            ->get();
    }

    /**
     * Get FAQs by category
     *
     * @param int|null $categoryId
     * @return Collection
     */
    public function getFAQsByCategory(?int $categoryId = null): Collection
    {
        $query = $this->faqModel->with('category')
            ->join('faq_categories', 'faq_categories.id', '=', 'faqs.category')
            ->where('faq_categories.status', true)
            ->select('faqs.*')
            ->orderBy('faqs.id');

        if ($categoryId) {
            $query->where('faqs.category', $categoryId);
        }

        return $query->get();
    }

    /**
     * Get FAQs grouped by category
     *
     * @return array
     */
    public function getFAQsGroupedByCategory(): array
    {
        $categories = $this->getActiveCategories();
        $groupedFAQs = [];

        foreach ($categories as $category) {
            $faqs = $this->getFAQsByCategory($category->id);
            if ($faqs->isNotEmpty()) {
                $groupedFAQs[] = [
                    'category' => $category,
                    'faqs' => $faqs
                ];
            }
        }

        return $groupedFAQs;
    }

    /**
     * Search FAQs by question or answer
     *
     * @param string $searchTerm
     * @param int|null $categoryId
     * @return Collection
     */
    public function searchFAQs(string $searchTerm, ?int $categoryId = null): Collection
    {
        $locale = app()->getLocale();
        
        $query = $this->faqModel->with('category')
            ->join('faq_categories', 'faq_categories.id', '=', 'faqs.category')
            ->where('faq_categories.status', true)
            ->where(function ($q) use ($searchTerm, $locale) {
                $q->whereRaw("JSON_UNQUOTE(JSON_EXTRACT(faqs.question, '$.{$locale}')) LIKE ?", ["%{$searchTerm}%"])
                  ->orWhereRaw("JSON_UNQUOTE(JSON_EXTRACT(faqs.answer, '$.{$locale}')) LIKE ?", ["%{$searchTerm}%"]);
            })
            ->select('faqs.*')
            ->orderBy('faqs.id');

        if ($categoryId) {
            $query->where('faqs.category', $categoryId);
        }

        return $query->get();
    }

    /**
     * Get category by ID
     *
     * @param int $categoryId
     * @return FAQCategory|null
     */
    public function getCategoryById(int $categoryId): ?FAQCategory
    {
        return $this->categoryModel->active()->find($categoryId);
    }

    /**
     * Get category slug mapping for frontend tabs
     *
     * @return array
     */
    public function getCategorySlugMapping(): array
    {
        $categories = $this->getActiveCategories();
        $mapping = [];

        foreach ($categories as $category) {
            $categoryName = $category->getTranslation('category', 'en');
            $slug = $this->generateSlugFromCategoryName($categoryName);
            $mapping[$slug] = $category->id;
        }

        return $mapping;
    }

    /**
     * Generate slug from category name
     *
     * @param string $categoryName
     * @return string
     */
    private function generateSlugFromCategoryName(string $categoryName): string
    {
        // Convert common category names to expected slugs
        $slugMap = [
            'Submissions' => 'submissions',
            'Representation' => 'representation', 
            'General Inquiries' => 'general',
            'General' => 'general',
            'Technical' => 'technical',
            'Billing' => 'billing'
        ];

        return $slugMap[$categoryName] ?? strtolower(str_replace(' ', '-', $categoryName));
    }
}
