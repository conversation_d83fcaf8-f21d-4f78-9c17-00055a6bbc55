<?php

namespace App\DataGrids\Admin;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Indianic\LaravelDataGrid\LaravelDataGrid;

class RoleDataGrid extends LaravelDataGrid
{
    public const ROLE_ACTION = "admin.common.action";
    public const ROLE_DESCRIPTION = "admin.roles.description";
    public $guard = 'admin';
    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'roles';

    /**
     * Define how many rows you want to display on a page
     * @var int $rowPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define row per page dropdown options
     * @var array $recordsPerPageOptions
     */
    public $recordsPerPageOptions;

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy;

    /**
     * Define default soring direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection;

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'roles';

    /**
     * Get Resource of Query Builder
     */
    public function resource()
    {
        return DB::table('roles');
    }

    public function columns(): array
    {
        return [
            'display_name' => __('admin.roles.name'),
            'description' => trans(self::ROLE_DESCRIPTION),
            'action' => trans(self::ROLE_ACTION),
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'display_name',
            'description',
        ];
    }

    public function downloadableColumns(): array
    {
        return [
            'display_name' => __('admin.roles.name'),
            'description' => trans(self::ROLE_DESCRIPTION),
        ];
    }

    public function gloablSearchableColumns(): array
    {
        return [
            'display_name' => 'string',
            'description' => 'string',
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'display_name' => 'string',
            'description' => 'string',
        ];
    }

    public function getAdvanceSearchOptions(): array
    {
        return [
            ['id' => 'display_name', 'label' => __('admin.roles.display_name'), 'type' => 'string'],
            ['id' => 'description', 'label' => trans(self::ROLE_DESCRIPTION), 'type' => 'string'],
        ];
    }

    public function getColumnAction(array $data): string
    {
        $return = '';
        $user = Auth::user();
        if (($user->hasPermission('update-roles') || $user->hasRole('super_admin'))
            && $user->id != $data['id'] && $data['id'] != 1) {
            $return = '<a class="me-3" href="' . route('admin.roles.edit', encrypt($data['id'])) . '">
             <span class="inic inic-edit fs-18"  data-bs-toggle="tooltip" title="Edit"></span></a>';
        }
        if (($user->hasPermission('delete-roles') || $user->hasRole('super_admin'))
            && $user->id != $data['id'] && $data['id'] != 1) {
            $return .= '<a class="delete-grid-row" href="javascript:void(0)"
            data-grid="inic_grid_' . $this->uniqueID . '"
             data-url="' . route('admin.roles.destroy', encrypt($data['id'])) . '">
            <span class="inic inic-bin fs-18"  data-bs-toggle="tooltip" title="Delete"></span></a>';

        }
        return $return;
    }
}
