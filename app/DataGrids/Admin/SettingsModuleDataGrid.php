<?php

namespace App\DataGrids\Admin;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Indianic\LaravelDataGrid\LaravelDataGrid;

class SettingsModuleDataGrid extends LaravelDataGrid
{
    public const SETTING_NAME = "admin.modules.display_name";
    public const SETTING_DESCRIPTION = "admin.modules.description";
    public const SETTING_STATUS = "admin.common.status";

    public $guard = 'admin';
    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'modules';

    /**
     * Define how many rows you want to display on a page
     * @var int $rowPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define row per page dropdown options
     * @var array $recordsPerPageOptions
     */
    public $recordsPerPageOptions;

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy;

    /**
     * Define default soring direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection;

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'modules';

    /**
     * Get Resource of Query Builder
     */
    public function resource()
    {
        return DB::table('modules')->where('is_moduled', '1');
    }

    public function columns(): array
    {
        return [
            'display_name' => trans(self::SETTING_NAME),
            'description' => trans(self::SETTING_DESCRIPTION),
            'dependent_modules' => __('admin.modules.dependent_modules'),
            'status' => trans(self::SETTING_STATUS),
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'display_name',
            'description',
            'status',
        ];
    }

    public function downloadableColumns(): array
    {
        return [
            'display_name' => trans(self::SETTING_NAME),
            'description' => trans(self::SETTING_DESCRIPTION),
            'status' => trans(self::SETTING_STATUS),
        ];
    }

    public function gloablSearchableColumns(): array
    {
        return [
            'display_name' => 'string',
            'description' => 'string',
            'dependent_modules' => 'string',
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'display_name' => 'string',
            'description' => 'string',
            'dependent_modules' => 'string',
        ];
    }

    public function getAdvanceSearchOptions(): array
    {
        return [
            ['id' => 'display_name', 'label' => trans(self::SETTING_NAME), 'type' => 'string'],
            ['id' => 'description', 'label' => trans(self::SETTING_DESCRIPTION), 'type' => 'string'],
            ['id' => 'dependent_modules', 'label' => __('admin.modules.dependent_modules'), 'type' => 'string'],
            ['id' => 'status', 'label' => trans(self::SETTING_STATUS), 'type' => 'string'],
        ];
    }

    public function getColumnStatus(array $data): string
    {
        return '<input type="checkbox"
         class="my-switch update-module-status"
         data-name = "' . $data['actual_name'] . '"
         data-dModules = "' . $data['dependent_modules'] . '"
         id="switchCheckbox' . $data['id'] . '" ' . (($data['status'] == 'Active') ? 'checked' : '') . '
          data-bs-toggle="tooltip" title="' . $data['status'] . '">';
    }

    public function getColumnAction(array $data): string
    {
        $user = Auth::user();
        $return = '';
        if ($user->hasPermission('edit-global-setttings')) {
            $return = '<a class="cursor-pointer me-3"
             href="' . route('admin.modules.edit', encrypt($data['id'])) . '">
             <span class="inic inic-edit" data-bs-toggle="tooltip" title="Edit"></span></a>';
        }

        if ($user->hasPermission('delete-global-setttings')) {
            $return .= '<a class="cursor-pointer delete-grid-row"
             href="javascript:void(0)" data-grid="inic_grid_' . $this->uniqueID . '"
              data-url="' . route('admin.modules.destroy', encrypt($data['id'])
            ) . '"><span class="inic inic-bin" data-bs-toggle="tooltip" title="Delete"></span></a>';
        }
        return $return;
    }

    public function getDownloadColumnStatus(array $data): string
    {
        return $data['status'];
    }
}
