<?php

namespace App\DataGrids\Admin;

use Indianic\LaravelDataGrid\LaravelDataGrid;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Vite;

class AdminDataGrid extends LaravelDataGrid
{
    public $guard = 'admin';
    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'admins';

    /**
     * Define how many rows you want to display on a page
     * @var int $rowPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define row per page dropdown options
     * @var array $recordsPerPageOptions
     */
    public $recordsPerPageOptions;

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy;

    /**
     * Define default soring direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection;

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'admins';


    public const FIRST_NAME = "admin.admins.first_name";
    public const LAST_NAME = "admin.admins.last_name";
    public const EMAIL = "admin.admins.email";
    public const MOBILE = "admin.admins.mobile";
    public const STATUS = "admin.common.status";
    public const TRANS_ACTIVE = "admin.common.active";
    /**
     * Get Resource of Query Builder
     */
    public function resource()
    {
        return DB::table('admins');
    }

    public function columns(): array
    {
        return [
            'checkbox' => '<input class="select_all checkbox" type="checkbox" />',
            'first_name' =>  trans(self::FIRST_NAME),
            'last_name' =>  trans(self::LAST_NAME),
            'email' => trans(self::EMAIL),
            'mobile' => trans(self::MOBILE),
            'status' =>  trans(self::STATUS),
            'created_at' => __('admin.admins.created_at'),
            'action' =>  __('admin.common.action')
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'first_name',
            'last_name',
            'email',
            'mobile',
            'status'
        ];
    }

    public function downloadableColumns(): array
    {
        return [
            'first_name' =>  trans(self::FIRST_NAME),
            'last_name' =>  trans(self::LAST_NAME),
            'email' => trans(self::EMAIL),
            'mobile' => trans(self::MOBILE),
            'status' => trans(self::STATUS)
        ];
    }

    public function gloablSearchableColumns(): array
    {
        return [
            'first_name' => 'string',
            'last_name' => 'string',
            'email' => 'string',
            'mobile' => 'string',
            'created_at' => 'date',
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'first_name' => 'string',
            'last_name' => 'string',
            'email' => 'string',
            'mobile' => 'string',
            'created_at' => 'date',
        ];
    }

    public function getAdvanceSearchOptions(): array
    {
        return [
            ['id' => 'first_name', 'label' => trans(self::FIRST_NAME), 'type' => 'string'],
            ['id' => 'last_name', 'label' => trans(self::LAST_NAME), 'type' => 'string'],
            ['id' => 'email', 'label' => __('admin.users.email'), 'type' => 'string'],
            ['id' => 'mobile', 'label' => __('admin.users.mobile_number'), 'type' => 'string'],
            ['id' => 'status', 'label' => __('admin.users.status'), 'type' => 'string'],
            ['id' => 'created_at', 'label' => __('admin.admins.created_at'), 'type' => 'date'],

        ];
    }

    public function filters(): array
    {
        return [
            'status' => [
                'title' => trans(self::STATUS),
                'data' => [
                    '1' => __(self::TRANS_ACTIVE), // true = 1 = Active
                    '0' => __('admin.common.in_active') // false = 0 = Inactive
                ],
                'width' => '250',
                'type' => 'string',
                'auto_filter_val' => __(self::TRANS_ACTIVE)
            ],
            'created_at' => [
                'title' => __('admin.admins.created_at'),
                'width' => '250',
                'type' => 'daterange'
            ],
        ];
    }

    public function getBulkAction(): array
    {
        return [
            'Active'=>[
                'class'=>'success',
                'title'=>__(self::TRANS_ACTIVE),
                'module'=>'Admin',
                'type' => 'active'
            ],
            'Inactive'=>[
                'class'=>'warning',
                'title'=>__('admin.common.in_active'),
                'module'=>'Admin',
                'type' => 'inactive'
            ],
            'Delete'=>[
                'class'=>'danger',
                'title'=>__('admin.common.delete'),
                'module'=>'Admin',
                'type' => 'delete'
            ]
        ];
    }

    public function getColumnFirstName(array $data): string
    {
        $url = ($data['photo']) ?
        getImage($data['photo'],config('constants.folders.admin'),true) :
        Vite::asset('resources/admin/images/default-user-img.png');

        return '<div class="d-flex align-items-center">
                    <img class="rounded-circle me-1"
                    src="' .$url . '" alt="'.$data['first_name'].'" width="30" height="30">
                    <a href="javascript:;" class="text-black">' . $data['first_name'] . '</a>
                </div>';
    }

    public function getColumnStatus(array $data): string
    {
        $isActive = (bool) $data['status'];
        $user = Auth::user();

        if ($data['id'] != 1 &&
         $user->hasPermission('update-admins') &&
         ($user->id != $data['id'])) {
            return '<input type="checkbox" class="my-switch change-status"
                   data-id = "' . $data['id'] . '"
                   id="switchCheckbox' . $data['id'] . '" ' . ($isActive ? 'checked' : '') . '
                    data-bs-toggle="tooltip" title="' . ($isActive ? 'Active' : 'Inactive') . ' Admin">';
        } else {
            return '<input type="checkbox" disabled="disabled"
            class="my-switch change-status" data-id = "' . $data['id'] . '"
            id="switchCheckbox' . $data['id'] . '" ' . ($isActive ? 'checked' : '') . '>';

        }
    }

    public function getColumnAction(array $data): string
    {
        $return = '';
        $user = Auth::user();
        if ($data['id'] != 1 && ($user->id != $data['id'])) {
            if ($user->hasPermission('update-admins')) {
                $return .= '<a class="me-3"
                href="' . route('admin.admins.edit', encrypt($data['id'])) . '">
                <span class="inic inic-edit fs-18"  data-bs-toggle="tooltip" title="Edit"></span></a>';

                $return .= '<a class="me-3"
                href="' . route('admin.admins.change_password', encrypt($data['id'])) . '">
                <span class="inic bx bxs-key fs-18"  data-bs-toggle="tooltip" title="Change Password"></span></a>';
            }
            if ($user->hasPermission('delete-admins') && $user->id != $data['id']) {
                $return .= '<a class="delete-grid-row"
                href="javascript:void(0)" data-grid="inic_grid_'.$this->uniqueID.'"
                data-url="' . route('admin.admins.destroy', encrypt($data['id'])
                ) . '"><span class="inic inic-bin fs-18"  data-bs-toggle="tooltip" title="Delete"></span></a>';
            }
        }
        return $return;
    }

    public function getColumnCreatedAt(array $data): string
    {
        if (isset($data['created_at']) && !empty($data['created_at'])) {
            return adminDateFormatShow($data['created_at']);
        } else {
            return '-';
        }
    }

    public function getDownloadColumnMobile(array $data): string
    {
        return 'MO : ' . $data['mobile'];
    }

    public function getColumnCheckbox($data): string
    {
        $user = Auth::user();
        if($data['id'] != 1 && $user->id != $data['id']){
            return '<input type="checkbox" value="' . $data['id'] . '" class="check_approval" />';
        }
        return '';
    }

    public function getDownloadColumnStatus(array $data): string
    {
        return (bool) $data['status'] ? 'Active' : 'Inactive';
    }

    public function getDownloadColumnFirstName(array $data): string
    {
        return $data['first_name'];
    }
}
