<?php

namespace App\DataGrids\Admin;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Vite;
use Indianic\LaravelDataGrid\LaravelDataGrid;

class UserDataGrid extends LaravelDataGrid
{
    public const FIRST_NAME = "admin.users.first_name";
    public const LAST_NAME = "admin.users.last_name";
    public const EMAIL = "admin.users.email";
    public const STATUS = "admin.common.status";
    public $guard = 'admin';

    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'users';

    /**
     * Define how many rows you want to display on a page
     * @var int $rowPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define row per page dropdown options
     * @var array $recordsPerPageOptions
     */
    public $recordsPerPageOptions;

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy;

    /**
     * Define default soring direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection = 'DESC';

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'users';

    /**
     * Get Resource of Query Builder
     */
    public function resource()
    {
        return User::select();
    }

    public function columns(): array
    {
        return [
            'checkbox' => '<input class="select_all checkbox" type="checkbox" />',
            'first_name' => trans(self::FIRST_NAME),
            'last_name' => trans(self::LAST_NAME),
            'email' => trans(self::EMAIL),
            'status' =>trans(self::STATUS),
            'action' => __('admin.common.action'),
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'first_name',
            'last_name',
            'email',
        ];
    }

    public function downloadableColumns(): array
    {
        return [
            'first_name' => trans(self::FIRST_NAME),
            'last_name' => trans(self::LAST_NAME),
            'email' => trans(self::EMAIL),
            'status' =>trans(self::STATUS),
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'first_name' => 'string',
            'last_name' => 'string',
            'email' => 'string',
        ];
    }

    public function gloablSearchableColumns(): array
    {
        return [
            'first_name' => 'string',
            'last_name' => 'string',
            'email' => 'string',
        ];
    }

    public function getAdvanceSearchOptions(): array
    {
        return [
            ['id' => 'first_name', 'label' => trans(self::FIRST_NAME), 'type' => 'string'],
            ['id' => 'last_name', 'label' => trans(self::LAST_NAME), 'type' => 'string'],
            ['id' => 'email', 'label' => trans(self::EMAIL), 'type' => 'string'],
            ['id' => 'status', 'label' => __('admin.users.status'), 'type' => 'string'],
        ];
    }

    public function filters(): array
    {
        return [
            'status' => [
                'title' => trans(self::STATUS),
                'data' => [
                    '1' => __('admin.common.active'), // true = 1 = Active
                    '0' => __('admin.common.in_active') // false = 0 = Inactive
                ],
                'width' => '250',
                'type' => 'string',
                'auto_filter_val' => 'Active',
            ],
        ];
    }

    /**
     * @return array
     */
    public function getBulkAction(): array
    {
        return [
            'Active' => [
                'class' => 'success',
                'title' => __('admin.common.active'),
                'module' => 'User',
                'type' => 'active'
            ],
            'Inactive' => [
                'class' => 'warning',
                'title' => __('admin.common.in_active'),
                'module' => 'User',
                'type' => 'inactive'
            ],
            'Delete' => [
                'class' => 'danger',
                'title' => __('admin.common.delete'),
                'module' => 'User',
                'type' => 'delete'
            ],
        ];
    }

    public function getColumnFirstName(array $data): string
    {

        $url = ($data['photo']) ?
        getImage($data['photo'],config('constants.folders.user'),true) :
        Vite::asset('resources/admin/images/default-user-img.png');
        return '<div class="d-flex align-items-center">
                <img class="rounded-circle me-1"
                src="' . $url . '" alt="' . $data['first_name'] . '"
                width="30" height="30">
                <a href="javascript:;" class="text-black">' . $data['first_name'] . '</a>
                </div>';
    }

    public function getColumnStatus(array $data): string
    {
        $isActive = (bool) $data['status'];
        return '<input type="checkbox" class="my-switch change-status"
                data-id = "' . $data['id'] . '"
                id="switchCheckbox' . $data['id'] . '" ' . ($isActive ? 'checked' : '') . '
                data-bs-toggle="tooltip" title="' . ($isActive ? 'Active' : 'Inactive') . ' User">';
    }

    public function getColumnAction(array $data): string
    {
        $user = Auth::user();
        $return = '<a class="me-3" href="' . route('admin.users.show', encrypt($data['id'])) . '" >
                      <span class="inic inic-visibility fs-18" data-bs-toggle="tooltip" title="Show"></span>
                    </a>';

        if ($user->hasPermission('update-users')) {
            $return .= '<a class="me-3" href="' . route('admin.users.edit', encrypt($data['id'])) . '">
                          <span class="inic inic-edit fs-18" data-bs-toggle="tooltip" title="Edit"></span>
                        </a>';
        }

        if ($user->hasPermission('delete-users')) {
            $return .= '<a class="delete-grid-row" href="javascript:void(0)"
                       data-grid="inic_grid_' . $this->uniqueID . '"
                       data-url="' . route('admin.users.destroy', encrypt($data['id'])) . '">
                       <span class="inic inic-bin fs-18" data-bs-toggle="tooltip" title="Delete"></span>
                       </a>';
        }

        return $return;
    }

    public function getDownloadColumnStatus(array $data): string
    {
        return (bool) $data['status'] ? 'Active' : 'Inactive';
    }

    public function getColumnCheckbox(array $data): string
    {
        return '<input type="checkbox" value="' . $data['id'] . '" class="check_approval" />';
    }

    public function getDownloadColumnFirstName(array $data): string
    {
        return $data['first_name'];
    }
}
