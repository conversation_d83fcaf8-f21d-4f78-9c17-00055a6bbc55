/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [

        "./*.html",
        "./assets/js/**/*.js"
    ],
    darkMode: 'class', // Use class-based dark mode
    theme: {
        extend: {
            container: {
                center: true,
                padding: '16px',
                screens: {
                  DEFAULT: '1332px', // Always 1300px
                },
              },
            colors: {
                primary: {
                    50: '#FFF9F7',
                    100: '#FFF2ED',
                    200: '#FFE2D4',
                    300: '#FFC0A8',
                    400: '#FF9571',
                    500: '#FF562F',
                    600: '#FE3411',
                    700: '#EF1A07',
                    800: '#C60D08',
                    900: '#9D0F10',
                    950: '#890506'
                },
                gray: {
                    50: '#F6F6F6',
                    100: '#D9D8D6',
                    500: '#494746',
                    700: '#B3ADA6',
                    800: '#040708',
                    900: '#494746',
                },
                danger: {
                    500: '#E24138',
                }
            },
            spacing: {
                '18': '4.5rem',
                '88': '22rem'
            },
            animation: {
                'fade-in': 'fadeIn 0.5s ease-in-out',
                'slide-in': 'slideIn 0.3s ease-out'
            },
            keyframes: {
                fadeIn: {
                    '0%': { opacity: '0', transform: 'translateY(10px)' },
                    '100%': { opacity: '1', transform: 'translateY(0)' }
                },
                slideIn: {
                    '0%': { transform: 'translateX(-100%)' },
                    '100%': { transform: 'translateX(0)' }
                }
            }
        }
    },
    plugins: [
    ]
} 