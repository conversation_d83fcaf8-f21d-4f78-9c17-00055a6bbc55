/**
 * Common Select All Checkbox Functionality
 * Universal solution for all data grids with select all functionality
 *
 * Classes used:
 * - .select_all: Header checkbox for select all
 * - .check_approval: Individual row checkboxes
 */

(function() {
    'use strict';

    // Ensure jQuery is available
    if (typeof jQuery === 'undefined') {
        console.error('Common Select All: jQuery is required');
        return;
    }

    var $ = jQuery;

    // Main initialization function
    function initSelectAllFunctionality() {

        // Update "Select All" checkbox state based on individual checkboxes
        function updateSelectAllState() {
            try {
                var totalCheckboxes = $('.check_approval').length;
                var checkedCheckboxes = $('.check_approval:checked').length;
                var selectAllCheckbox = $('.select_all');

                if (totalCheckboxes === 0) {
                    selectAllCheckbox.prop('checked', false);
                } else if (checkedCheckboxes === totalCheckboxes && checkedCheckboxes > 0) {
                    selectAllCheckbox.prop('checked', true);
                } else {
                    selectAllCheckbox.prop('checked', false);
                }
            } catch (error) {
                console.error('Error updating select all state:', error);
            }
        }

        // Remove any existing handlers to prevent duplicates
        $(document).off('click.selectAll', '.select_all');
        $(document).off('change.selectAll', '.check_approval');

        // Handle "Select All" checkbox click
        $(document).on('click.selectAll', '.select_all', function() {
            try {
                var isChecked = $(this).prop('checked');
                $('.check_approval').prop('checked', isChecked);
            } catch (error) {
                console.error('Error handling select all click:', error);
            }
        });

        // Handle individual checkbox changes
        $(document).on('change.selectAll', '.check_approval', function() {
            updateSelectAllState();
        });

        // Initialize state
        function initState() {
            setTimeout(function() {
                updateSelectAllState();
            }, 100);
        }

        // Hook into grid refresh methods
        function hookGridRefresh() {
            try {
                $('[id^="inic_grid_"]').each(function() {
                    var gridId = $(this).attr('id').replace('inic_grid_', '');
                    var gridInstance = window['inic_grid_' + gridId];

                    if (gridInstance && typeof gridInstance.refresh === 'function' && !gridInstance._selectAllHooked) {
                        var originalRefresh = gridInstance.refresh;
                        gridInstance.refresh = function() {
                            originalRefresh.call(this);
                            setTimeout(initState, 200);
                        };
                        gridInstance._selectAllHooked = true;
                    }
                });
            } catch (error) {
                console.error('Error hooking grid refresh:', error);
            }
        }

        // Initialize everything
        initState();
        setTimeout(hookGridRefresh, 500);

        // Listen for custom events
        $(document).on('grid-refreshed data-loaded', initState);

        // Use MutationObserver for modern browsers, fallback for older ones
        if (typeof MutationObserver !== 'undefined') {
            var observer = new MutationObserver(function(mutations) {
                var needsRehook = false;
                for (var i = 0; i < mutations.length; i++) {
                    var mutation = mutations[i];
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        for (var j = 0; j < mutation.addedNodes.length; j++) {
                            var node = mutation.addedNodes[j];
                            if (node.nodeType === 1 && node.id && node.id.indexOf('inic_grid_') === 0) {
                                needsRehook = true;
                                break;
                            }
                        }
                    }
                    if (needsRehook) break;
                }

                if (needsRehook) {
                    setTimeout(hookGridRefresh, 100);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }

    // Global helper functions
    window.refreshSelectAllState = function() {
        try {
            var totalCheckboxes = $('.check_approval').length;
            var checkedCheckboxes = $('.check_approval:checked').length;
            var selectAllCheckbox = $('.select_all');

            if (checkedCheckboxes === totalCheckboxes && checkedCheckboxes > 0) {
                selectAllCheckbox.prop('checked', true);
            } else {
                selectAllCheckbox.prop('checked', false);
            }
        } catch (error) {
            console.error('Error refreshing select all state:', error);
        }
    };

    window.resetSelectAllCheckboxes = function() {
        try {
            $('.select_all').prop('checked', false);
            $('.check_approval').prop('checked', false);
        } catch (error) {
            console.error('Error resetting checkboxes:', error);
        }
    };

    // Initialize when DOM is ready
    $(document).ready(function() {
        initSelectAllFunctionality();
    });

    // Also initialize if jQuery is loaded after this script
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        setTimeout(initSelectAllFunctionality, 0);
    }

})();
