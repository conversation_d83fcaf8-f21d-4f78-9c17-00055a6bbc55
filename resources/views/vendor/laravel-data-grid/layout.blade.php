<div class="new--grid--style" id="inic_grid_{{$gridID}}">
    <div class="card">
        <div class="card-header card-action-wrap px-0 bg-transparent pt-0 pb-3 mb-2 card-controller filter-card-body">
            <div>
                <div class="row">
                    <div class="col-12 col-lg-6 col-md-12">
                        <div class="d-flex flex-wrap gap-1 justify-content-between justify-content-lg-start mb-2 mb-lg-0">
                            @if (count($gloablSearchableColumns) > 0)
                                <div class="mb-0 search-wrap">
                                    <input name="search" class="form-control search-global-input search-icon-left" type="search" placeholder="{{ __('admin.common.search') }}"/>
                                </div>
                            @endif
                            <div class="action-btns flex-wrap d-flex gap-1">
                                @if(!empty($bulkActionButtons))
                                @foreach($bulkActionButtons as $bulkKey => $bulkActionButton)
                                    <button class="btn btn-sm btn-{{ $bulkActionButton['class'] }}-light grid-bulk-action" id="bulk{{$bulkKey}}" data-status="{{ $bulkActionButton['title'] }}" data-module="{{ $bulkActionButton['module'] }}" data-bs-toggle="tooltip" title="Bulk {{ucwords(str_replace('_', ' ', $gridID))}} {{ $bulkActionButton['title'] }}" data-action-type={{isset($bulkActionButton['type']) ? $bulkActionButton['type'] : ''}}>{{ $bulkActionButton['title'] }}</button>
                                @endforeach
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-lg-6 col-md-12">
                        <div class="btn-toolbar" role="toolbar">
                                <div class="action-btn-wrap w-100 d-flex justify-content-start gap-1 justify-content-lg-end" role="group">

                                    @if(count($advanceSearchOptions) > 0)
                                        <button show-hide="advance-search-options" type="button"
                                            class="btn btn-sm btn-primary"
                                             data-bs-toggle="tooltip" title="{{ __('admin.common.advance_search') }}">
                                            <span class="inic inic-search">
                                            </span>
                                        </button>
                                    @endif

                                    @if(count($filters) > 0)
                                        <button type="button" class="btn btn-sm btn-primary quick-filter-btn"
                                        href="#collapseExample" show-hide="quick-filter"
                                                data-bs-toggle="tooltip" title="Quick Filters"
                                                data-bs-toggle="collapse"   role="button" aria-expanded="false"
                                                aria-controls="collapseExample">
                                                <span class="inic inic-filter-1"></span>
                                                <span class="badge rounded-pill bg-danger quick-filter-count"></span>
                                        </button>
                                     @endif
                                    @if(count($downloadOptions) > 0)
                                            <button show-hide="download-options" type="button"
                                                    class="btn btn-sm btn-primary"
                                                     data-bs-toggle="tooltip" title="{{ __('admin.common.download_options') }}">
                                                    <span class="inic inic-download"></span></button>
                                    @endif
                                    {{-- <button type="button" class="btn btn-sm btn-primary refreshGrid"
                                    data-bs-toggle="tooltip" title="{{ __('admin.common.refresh') }}"><span class="inic inic-rotate-left"></span>
                                    </button> --}}
                                    <div class="dropdown screen-options">
                                        <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-expanded="false" data-bs-toggle="tooltip" title="Table Column Show/Hide">Columns</button>
                                        <div class="dropdown-menu p-3">
                                            <div class="d-flex flex-column gap-3">
                                                @foreach ($columns as $id => $title)
                                                    @if($id != 'checkbox')
                                                    <div class="custom-checkbox save-screen-options d-flex align-items-center m-0">
                                                        <input class="screen_options_ele my-checkbox m-0"
                                                            checked="checked"
                                                            name="screen_options[{{ $id }}]"
                                                            value="{{ $id }}"
                                                            type="checkbox"
                                                            id="screen_options_{{ $id }}">
                                                            <span></span>
                                                        <label class="custom-control-label mb-0 me-0"
                                                            for="screen_options_{{ $id }}">{!! $title !!}</label>
                                                    </div>
                                                    @endif
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        </div>
                    </div>
                    <div class="col-md-12">
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick filter options : Start -->
        @include('vendor/laravel-data-grid/_quick_filter_data')

        <!-- Default panel contents -->
        @if(count($advanceSearchOptions) > 0)
            <div class="card-body advance-search-options custom-dropdown hidden card-controller filter-card-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="filter-option">
                            <div class="flBox-body">
                                <div class="flBox-left">
                                    <h6 class="ftitle text-black fw-bold">{{ __('admin.common.advance_search') }}</h6>
                                    <div class="query_builder"></div>
                                </div>
                                <div class="flBox-right">
                                    <h6 class="fs-14">Saved Advance selections</h6>
                                    <div class="saved-ft d-flex gap-1 flex-wrap flex-column flex-shrink-0 template-list-outer right-col-list filter_result"
                                        id="inic_grid_{{$gridID}}_advance_search_templates" {!! count($advanceSearchTemplates) < 1 ? '' : ' style="display:none;"' !!}>
                                        <div class="d-flex align-items-center gap-1 flex-wrap flex-shrink-0">
                                            @if(count($advanceSearchTemplates) > 0)
                                                @foreach($advanceSearchTemplates as $advanceSearchTemplate)
                                                    <div data-id="{{ $advanceSearchTemplate['id'] }}"  class="saved-item flex-shrink-0 input-group d-flex flex-wrap align-items-center setAdvanceSearchTemplate">
                                                        <label for="search-template1" class="radio-label-btn list-group-item searchTemplate">
                                                        <input type="radio" name="usersDownloads" id="search-template1">
                                                        <span class=""> {{ $advanceSearchTemplate['title'] }}</span>
                                                        </label>
                                                        <button type="button" class="btn btn-sm btn-link text-white p-1 remove">
                                                            <em class="inic inic-bin" data-bs-toggle="tooltip" title="Delete"></em>
                                                        </button>
                                                    </div>
                                                @endforeach
                                            @else
                                                <h6 class="ftitle text-black fw-bold templateNotFoundHere">{{ __('No Template Found') }}</h6>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-12">
                        <form class="flBox-body">
                            <div class="flBox-left">
                                <!-- <h6>Filter Data</h6> -->
                                <ul class="list-unstyled mb-2">
                                </ul>
                                <div class="action-wrap d-flex justify-content-between gap-2 flex-wrap">
                                    <div class="d-flex align-items-center gap-2" id="inic_grid_{{$gridID}}_get_advance_search_value">
                                        <button class="btn btn-primary build_query_btn getAdvancedSearchData" type="button">{{ __('admin.common.search') }}</button>
                                        <button class="btn btn-danger-light ms-12 resetQueryBuilder" type="button">{{ __('admin.common.clear') }}</button>
                                    </div>


                                        <div id="inic_grid_{{$gridID}}_save_advance_search_template" class="input-group advancerdSearch mb-0 ms-12">
                                            <input type="text" name="template_name" class="form-control rule-value-container" value="" autocomplete="off">
                                            <button type="button" class="btn btn-primary saveAdvanceSearchTemplate">{{ __('admin.common.save') }}</button>
                                            <span class="pt-8 help-block error-help-block advancedSearchError"></span>
                                        </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        @endif
        <div class="card-body screen-options custom-dropdown hidden animated card-controller filter-card-body">
            <!-- Screen options Start -->
            <!-- Screen options End -->
        </div>
        @if(count($downloadOptions) > 0)

           <div class="card-body download-options custom-dropdown hidden card-controller filter-card-body">
             <form name="inic_grid_{{$gridID}}_download_form" class="dlBox-body">
                      <div class="dlBox-left">
                        <h6 class="text-black fw-bold fs-14">{{ __('admin.common.select_what_to_download') }}</h6>
                        <ul class="column-selection-listing">
                        @foreach ($downloadOptions as $id => $title)

                          <li class="d-flex align-items-center toggle-vis">
                            <span class="inic inic-drag-handle me-2 mt-1"></span>
                            <div class="custom-checkbox">
                                <input class="download_options_ele custom-control-input my-checkbox"
                                                    checked="checked"
                                                    id="download_{{ $id }}"
                                                    name="download[{{ $id }}]"
                                                    value="{{ $id }}"
                                                    type="checkbox">
                                <label class="text-black fs-14 fw-normal"
                            for="download_{{ $id }}">{!! $title !!}</label>

                            </div>
                          </li>
                          @endforeach

                        </ul>
                      </div>

                      <div class="dlBox-right">
                        <h6 class="fs-14">{{ __('admin.common.saved_download_selections') }}</h6>
                        <div class="saved-ft d-flex align-items-center gap-1 flex-wrap flex-shrink-0 scrollable template-list-outer right-col-list filter_result" id="inic_grid_{{$gridID}}_download_templates" {!! count($downloadTemplates) < 1 ? ' style="display:none;"' : '' !!} >
                            @if(count($downloadTemplates) >= 1)
                                @foreach($downloadTemplates as $downloadTemplate)
                                <div data-id="{{ $downloadTemplate['id'] }}"  class="saved-item flex-shrink-0 input-group d-flex flex-wrap align-items-center setDownloadTemplate">
                                    <label for="saved-template1" class="radio-label-btn list-group-item">
                                        <input type="radio" name="usersDownloads" id="saved-template1">
                                        <span class=""> {{ $downloadTemplate['title'] }}</span>
                                    </label>
                                    <button type="button" class="btn btn-sm btn-link text-white p-1 remove">
                                        <em class="inic inic-bin" data-bs-toggle="tooltip" title="Delete"></em>
                                    </button>
                                </div>
                                @endforeach
                                @else
                                <div class="components-head downloadtemplateNotFoundHere"> <span> No Templates Found </span> </div>
                            @endif
                        </div>


                        <div class="action-wrap d-flex align-items-end justify-content-between flex-wrap w-100 mt-auto gap-2 ">
                          <div class="left-action-wrap d-flex flex-column me-auto flex-shrink-0">
                            <h6 class="fs-14">{{ __('admin.common.select_file_type') }}</h6>
                            <div class="d-flex gap-2">
                                @if(config('laravel-data-grid.download_excel'))
                                <button type="button" data-type="xls" id="downloadExcel" class="dl-file-btn btn btn-sm btn-primary-light border-0 download-grid-data">{{ __('admin.common.excel') }}</button>
                                @endif
                                @if(config('laravel-data-grid.download_csv'))
                                    <button type="button" data-type="csv" id="downloadCsv" class="dl-file-btn btn btn-sm btn-primary-light border-0 download-grid-data">{{ __('admin.common.csv') }}</button>
                                @endif
                            </div>
                          </div>

                          <div id="inic_grid_{{$gridID}}_save_download_template" class="right-action-wrap d-flex align-items-center gap-2" >
                            <div class="input-group input-group-sm mb-0">
                              <input type="text" name="template_name" class="form-control" placeholder="{{ __('admin.common.template_name') }}" aria-describedby="button-addon2">
                              <button id="button-addon2" class="btn btn-primary saveDownloadTemplate">
                                <span>{{ __('admin.common.save_template') }}</span>
                              </button>
                              <span class="pt-8 help-block error-help-block invalid_feedback_template_name"></span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </form>
           </div>
        @endif
        @if(count($filters) > 0)
            <div class="card-body filter custom-dropdown hidden card-controller filter-card-body">
                <!-- Filter Start -->
                <div class="row">
                    <div class="col-sm-12">
                        <h6 class="ftitle text-black fw-bold fs-14">{{ __('admin.common.filters') }}</h6>
                        <form name="inic_grid_{{$gridID}}_filter_form" class="filter-list">
                            <table style="width:auto;" cellspacing="0" cellpadding="0" border="0">
                                <tbody>
                                <tr>
                                    @foreach($filters as $id => $filter)
                                        @if(isset($filter['data']) && is_array($filter['data']) && (!empty($filter['data']) || (isset($filter['type']) && $filter['type'] == 'date')))
                                            <td{!! isset($filter['width']) ? ' width="' . $filter['width'] . '"' : "" !!}>
                                                <h5>{{ isset($filter['title']) ? $filter['title'] : $id }}</h5>

                                                @if(isset($filter['type']) && $filter['type'] === 'date')

                                                    <div class="form-group">
                                                        <span>From Date</span>
                                                        <input name="filters[{{ $id }}][from]"
                                                            placeholder="mm/dd/yyyy" type="text"
                                                            class="form-control date-picker filter_{{ $id }}_from"
                                                            style="width: 150px">
                                                    </div>

                                                    <div class="form-group">
                                                        <span>To Date</span>
                                                        <input name="filters[{{ $id }}][to]"
                                                            placeholder="mm/dd/yyyy" type="text"
                                                            class="form-control date-picker filter_{{ $id }}_to"
                                                            style="width: 150px">
                                                    </div>

                                                @elseif(is_array($filter['data']) && !empty(is_array($filter['data'])))

                                                    <div class="scrolled-item">
                                                    @foreach($filter['data'] as $filterVal => $filterTitle)
                                                            <div class="custom-control custom-checkbox mb-3">
                                                                <input name="filters[{{ $id }}][]" type="checkbox"
                                                                    class="custom-control-input filter_{{ $id }}_{{ str_replace(' ', '', $filterVal) }}"
                                                                    id="filter_{{ $id }}_{{ str_replace(' ', '', $filterVal) }}"
                                                                    value="{{ $filterVal }}">
                                                                <label class="custom-control-label"
                                                                    for="filter_{{ $id }}_{{ str_replace(' ', '', $filterVal) }}">{{ $filterTitle }}</label>
                                                            </div>
                                                        @endforeach
                                                    </div>

                                                @endif

                                            </td>
                                        @endif
                                    @endforeach
                                </tr>
                                </tbody>
                            </table>
                        </form>
                        <div class="filter_result"
                            id="inic_grid_{{$gridID}}_filter_templates" {!! count($filterTemplates) < 1 ? ' style="display:block;"' : '' !!}>
                            <strong>{{ __('admin.common.your_templates') }} </strong>
                            <ul>
                                @foreach($filterTemplates as $filterTemplate)
                                    <li data-id="{{ $filterTemplate['id'] }}"><a href="javascript:;"
                                                                                class="setFilterTemplate">{{ $filterTemplate['title'] }}</a>
                                        <span class="remove"><i class="fa fa-times"></i></span></li>
                                @endforeach
                            </ul>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn btn-primary filter_now_btn">{{ __('admin.common.apply') }}</button>
                            <button type="button" class="btn btn-dark resetGrid">{{ __('admin.common.reset') }}</button>
                            <button type="button" class="btn btn-success blue displaySaveFilterTemplatePopup"><i
                                    class="fa fa-floppy-o"></i> SAVE
                            </button>
                        </div>
                    </div>
                </div>
                <!-- Filter End -->
            </div>
        @endif
        <div class="table-responsive">
            <table class="table user-table grid-data-table dataTable table-hover">
                <thead>
                    <tr>
                        @if($childColumns)
                            <th class="details-control"></th>
                        @endif
                        @if($hasBulkAction)
                            <th style="width: 30px;" class="column-bulk-action">
                                <input type="checkbox" class="bulk_action_checkbox_check_uncheck_all"/>
                            </th>
                        @endif
                        @foreach ($columns as $id => $title)
                            <th data-column-id="{{ $id }}"
                                class="column-{{ $id }}{{ in_array($id, $sortableColumns) ? ' sorting' : '' }}{{ $id == $orderBy['sort'] ? ' ' . $orderBy['sort_direction'] : '' }}"{!! in_array($id, $sortableColumns) ? ('sorting="' . $id . '"') : "" !!}>{!! $title !!}</th>
                        @endforeach
                    </tr>

                    @if (count($searchableColumns) > 0 && gridColumnSearchStatus())
                        <tr class="column_search_row">
                            @if($childColumns)
                                <th class="details-control"></th>
                            @endif
                            @if($hasBulkAction)
                                <th class="column-bulk-action"></th>
                            @endif
                            @foreach ($columns as $id => $title)
                                @if (isset($searchableColumns[$id]))
                                    <th data-column-id="{{ $id }}" class="column-search-{{ $id }}">
                                        @if($searchableColumns[$id] == 'integer')
                                            <input name="column_search[{{ $id }}]" column-search="{{ $id }}"
                                                class="form-control" type="number"/>
                                        @elseif(is_array($searchableColumns[$id]))
                                            <select name="column_search[{{ $id }}]" column-search="{{ $id }}"
                                                    class="form-control input-sm">
                                                <option value="">All</option>
                                                @foreach($searchableColumns[$id] as $val => $title)
                                                    <option value="{{ $val }}">{{ $title }}</option>
                                                @endforeach
                                            </select>
                                        @elseif($searchableColumns[$id] === 'date' )
                                            <input name="column_search[{{ $id }}]" column-search="{{ $id }}"
                                                onkeydown="return false" class="form-control searchAbleDatePicker date-picker"
                                                type="text"/>
                                        @elseif($searchableColumns[$id] === 'daterange' )
                                            <input name="column_search[{{ $id }}]" column-search="{{ $id }}"
                                                onkeydown="return false" class="form-control daterange-picker"
                                                type="text"/>
                                        @else
                                            <input name="column_search[{{ $id }}]" column-search="{{ $id }}"
                                                class="form-control" type="text"/>
                                        @endif
                                    </th>
                                @else
                                    <th data-column-id="{{ $id }}" class="column-search-{{ $id }}"></th>
                                @endif
                            @endforeach
                        </tr>
                    @endif
                </thead>
                <tbody></tbody>
            </table>
        </div>

        <nav class="pagination-wrap">
            <div class="show-per-page"> {{ __('admin.common.record_per_page') }} <select name="row_per_page" class="form-control">
                    @foreach($recordsPerPageDropDown as $recordsPerPageDropDown)
                        <option
                            value="{{ $recordsPerPageDropDown }}"{!! $recordsPerPageDropDown == $recordsPerPage ? ' selected="selected"' : '' !!}>{{ $recordsPerPageDropDown }}</option>
                    @endforeach
                </select>
            </div>

            <div class="view-per-page">Showing 0 to 0 of 0 entries</div>

            <div class="dataTables_paginate paging_simple_numbers" aria-label="pagination">
                <ul class="pagination"></ul>
            </div>
        </nav>
    </div>

    <div class="modal fade" id="jinic_grid_{{$gridID}}_save_download_templates" role="dialog" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">{{ __('admin.common.save_template') }}</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                </div>
                <div class="modal-body">
                    <form class="form">
                        <div class="form-group required">
                            <label class="control-label">{{ __('admin.common.template_name') }}</label>
                            <input type="text" name="template_name" class="form-control" value="" autocomplete="off">
                            <div class="invalid-feedback"></div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('admin.common.cancel') }}</button>
                    <button type="button" class="btn btn-primary saveDownloadTemplate">{{ __('admin.common.save') }}</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="inic_grid_{{$gridID}}_save_filter_template" role="dialog" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">{{ __('admin.common.save_template') }}</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                </div>
                <div class="modal-body">
                    <form class="form">
                        <div class="form-group required">
                            <label class="control-label">{{ __('admin.common.template_name') }}</label>
                            <input type="text" name="template_name" class="form-control" value="" autocomplete="off">
                            <div class="invalid-feedback"></div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('admin.common.cancel') }}</button>
                    <button type="button" class="btn btn-primary saveFilterTemplate">{{ __('admin.common.save') }}</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="inic_grid_{{$gridID}}_template_warning" role="dialog" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title"></h4>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                </div>
                <div class="modal-body"></div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-dismiss="modal">{{ __('admin.common.close') }}</button>
                </div>
            </div>
        </div>
    </div>
</div>
@include('plugins.datepicker')
@include('plugins.daterangepicker')
@push('css')
    {!! Html::style('vendor/laravel-data-grid/css/laravel-data-grid.css') !!}
@endpush
@push('scripts')
<script src="{{ asset('vendor/laravel-data-grid/js/laravel-data-grid.js')}}" defer></script>
<script src="{{ asset('assets/js/common-select-all.js')}}" defer></script>
    <script type="module">
        $(document).ready(function () {
            window.inic_grid_{{$gridID}} = new iNICGrid('inic_grid_{{$gridID}}', {
                id: '{{ $gridID }}',
                baseURI: '{{ URL::to('/') }}',
                ajaxURI: '{{ $ajaxURI }}',
                columns: JSON.parse('{!! json_encode(array_keys($columns)) !!}'),
                childColumns: JSON.parse('{!! json_encode(array_keys($childColumns)) !!}'),
                childColumnHeaders: JSON.parse('{!! json_encode(array_values($childColumns)) !!}'),
                childColumnsPerRow: '{{ $childColumnsPerRow }}',
                recordsPerPage: '{{ $recordsPerPage }}',
                orderBy: JSON.parse('{!! json_encode($orderBy) !!}'),
                bulkAction: '{{$hasBulkAction}}',
                inactiveColumns: JSON.parse('{!! json_encode($inactiveColumns) !!}'),
                downloadTemplates: JSON.parse('{!! json_encode($downloadTemplates) !!}'),
                filterTemplates: JSON.parse('{!! json_encode($filterTemplates) !!}'),
                advanceSearchTemplates: JSON.parse('{!! json_encode($advanceSearchTemplates) !!}'),
                advanceSearchOptions: JSON.parse('{!! json_encode($advanceSearchOptions) !!}'),
                loadingTxt: '{{ config('laravel-data-grid.loading_txt') }}'
            });
        });
    </script>
@endpush

