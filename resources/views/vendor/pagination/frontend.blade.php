@if ($paginator->hasPages())
    <div class="flex justify-center items-center mt-12 gap-4">
        {{-- Previous Page Link --}}
        @if ($paginator->onFirstPage())
            <button
                class="px-6 py-4 rounded-full text-gray-400 border border-gray-300 hover:text-gray-700 transition-colors duration-200 me-4"
                aria-label="Previous page" disabled>
                PREV
            </button>
        @else
            <a href="{{ $paginator->previousPageUrl() }}"
                class="px-6 py-4 rounded-full border border-primary-500 text-primary-500 hover:text-primary-600 transition-colors duration-200 ms-4"
                aria-label="Previous page">
                PREV
            </a>
        @endif

        <div class="flex gap-4">
            @foreach ($elements as $element)
                {{-- "Three Dots" Separator --}}
                @if (is_string($element))
                    <button class="min-w-[58px] py-4 rounded-full border border-gray-100 text-gray-900" disabled>{{ $element }}</button>
                @endif

                {{-- Array Of Links --}}
                @if (is_array($element))
                    @foreach ($element as $page => $url)
                        @if ($page == $paginator->currentPage())
                            <button class="min-w-[58px] py-4 rounded-full border border-primary-500 text-primary-500 font-medium" aria-label="Page {{ $page }}" aria-current="page">{{ str_pad($page, 2, '0', STR_PAD_LEFT) }}</button>
                        @else
                            <a href="{{ $url }}"
                                class="min-w-[58px] py-4 rounded-full border border-gray-100 text-gray-900 hover:border-primary-500 hover:text-primary-500 transition-colors duration-200"
                                aria-label="Page {{ $page }}">
                                {{ str_pad($page, 2, '0', STR_PAD_LEFT) }}
                            </a>
                        @endif
                    @endforeach
                @endif
            @endforeach
        </div>

        {{-- Next Page Link --}}
        @if ($paginator->hasMorePages())
            <a href="{{ $paginator->nextPageUrl() }}"
                class="px-6 py-4 rounded-full border border-primary-500 text-primary-500 hover:text-primary-600 transition-colors duration-200 ms-4"
                aria-label="Next page">
                NEXT
            </a>
        @else
            <button
                class="px-6 py-4 rounded-full text-gray-400 border border-gray-300 hover:text-gray-700 transition-colors duration-200 me-4"
                aria-label="Next page" disabled>
                NEXT
            </button>
        @endif
    </div>
@endif
