<form action="{{ route('contact.store') }}" method="POST" class="contact-form">
    @csrf

    <div class="row">
        <!-- Name -->
        <div class="col-md-6 mb-3">
            <label for="name" class="form-label">
                {{ __('frontend.contact.name') }} <span class="text-danger">*</span>
            </label>
            <input type="text"
                   class="form-control @error('name') is-invalid @enderror"
                   id="name"
                   name="name"
                   value="{{ old('name') }}"
                   required>
            @error('name')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- Email -->
        <div class="col-md-6 mb-3">
            <label for="email" class="form-label">
                {{ __('frontend.contact.email') }} <span class="text-danger">*</span>
            </label>
            <input type="email"
                   class="form-control @error('email') is-invalid @enderror"
                   id="email"
                   name="email"
                   value="{{ old('email') }}"
                   required>
            @error('email')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
    </div>

    <div class="row">
        <!-- Phone -->
        <div class="col-md-6 mb-3">
            <label for="phone" class="form-label">
                {{ __('frontend.contact.phone') }} <span class="text-muted">({{ __('frontend.optional') }})</span>
            </label>
            <input type="tel"
                   class="form-control @error('phone') is-invalid @enderror"
                   id="phone"
                   name="phone"
                   value="{{ old('phone') }}">
            @error('phone')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>

        <!-- Inquiry Type -->
        <div class="col-md-6 mb-3">
            <label for="inquiry_type" class="form-label">
                {{ __('frontend.contact.inquiry_type') }} <span class="text-danger">*</span>
            </label>
            <select class="form-select @error('inquiry_type') is-invalid @enderror"
                    id="inquiry_type"
                    name="inquiry_type"
                    required>
                <option value="">{{ __('frontend.contact.inquiry_type') }}</option>
                @foreach(__('frontend.contact.inquiry_types') as $key => $type)
                    <option value="{{ $key }}" {{ old('inquiry_type') === $key ? 'selected' : '' }}>
                        {{ $type }}
                    </option>
                @endforeach
            </select>
            @error('inquiry_type')
                <div class="invalid-feedback">{{ $message }}</div>
            @enderror
        </div>
    </div>

    <!-- Subject -->
    <div class="mb-3">
        <label for="subject" class="form-label">
            {{ __('frontend.contact.subject') }} <span class="text-danger">*</span>
        </label>
        <input type="text"
               class="form-control @error('subject') is-invalid @enderror"
               id="subject"
               name="subject"
               value="{{ old('subject') }}"
               required>
        @error('subject')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
    </div>

    <!-- Message -->
    <div class="mb-4">
        <label for="message" class="form-label">
            {{ __('frontend.contact.message') }} <span class="text-danger">*</span>
        </label>
        <textarea class="form-control @error('message') is-invalid @enderror"
                  id="message"
                  name="message"
                  rows="5"
                  required
                  minlength="10"
                  maxlength="2000">{{ old('message') }}</textarea>
        @error('message')
            <div class="invalid-feedback">{{ $message }}</div>
        @enderror
        <div class="form-text">{{ __('messages.required') }}</div>
    </div>

    <!-- Submit Button -->
    <div class="text-center">
        <button type="submit" class="btn btn-primary btn-lg">
            <i class="fas fa-paper-plane me-2"></i>
            {{ __('frontend.send') }}
        </button>
    </div>
</form>

@push('scripts')
<script>
$(document).ready(function() {
    // Character counter for message
    $('#message').on('input', function() {
        const maxLength = 2000;
        const currentLength = $(this).val().length;
        const remaining = maxLength - currentLength;

        let counterText = `${currentLength}/${maxLength}`;
        if (remaining < 100) {
            counterText += ` (${remaining} remaining)`;
        }

        $(this).siblings('.form-text').text(counterText);
    });
});
</script>
@endpush
