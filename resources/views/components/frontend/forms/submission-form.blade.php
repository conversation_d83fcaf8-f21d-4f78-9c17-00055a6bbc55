<form action="{{ route('submissions.store') }}" method="POST" enctype="multipart/form-data" class="submission-form">
    @csrf

    <!-- Author Information Section -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-user me-2"></i>Author Information</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="name" class="form-label">
                        {{ __('frontend.submissions.name') }} <span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                           id="name" name="name" value="{{ old('name') }}" required>
                    @error('name')<div class="invalid-feedback">{{ $message }}</div>@enderror
                </div>

                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">
                        {{ __('frontend.submissions.email') }} <span class="text-danger">*</span>
                    </label>
                    <input type="email" class="form-control @error('email') is-invalid @enderror"
                           id="email" name="email" value="{{ old('email') }}" required>
                    @error('email')<div class="invalid-feedback">{{ $message }}</div>@enderror
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="phone" class="form-label">
                        {{ __('frontend.submissions.phone') }}
                    </label>
                    <input type="tel" class="form-control @error('phone') is-invalid @enderror"
                           id="phone" name="phone" value="{{ old('phone') }}">
                    @error('phone')<div class="invalid-feedback">{{ $message }}</div>@enderror
                </div>
            </div>

            <div class="mb-3">
                <label for="biography" class="form-label">
                    {{ __('frontend.submissions.biography') }} <span class="text-danger">*</span>
                </label>
                <textarea class="form-control @error('biography') is-invalid @enderror"
                          id="biography" name="biography" rows="4" required
                          minlength="50" maxlength="1000">{{ old('biography') }}</textarea>
                @error('biography')<div class="invalid-feedback">{{ $message }}</div>@enderror
                <div class="form-text">Minimum 50 characters, maximum 1000 characters</div>
            </div>

            <div class="mb-3">
                <label for="previous_publications" class="form-label">
                    {{ __('frontend.submissions.previous_publications') }}
                </label>
                <textarea class="form-control @error('previous_publications') is-invalid @enderror"
                          id="previous_publications" name="previous_publications" rows="3"
                          maxlength="1000">{{ old('previous_publications') }}</textarea>
                @error('previous_publications')<div class="invalid-feedback">{{ $message }}</div>@enderror
            </div>
        </div>
    </div>

    <!-- Manuscript Information Section -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0"><i class="fas fa-book me-2"></i>Manuscript Information</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8 mb-3">
                    <label for="title" class="form-label">
                        {{ __('frontend.submissions.title') }} <span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control @error('title') is-invalid @enderror"
                           id="title" name="title" value="{{ old('title') }}" required>
                    @error('title')<div class="invalid-feedback">{{ $message }}</div>@enderror
                </div>

                <div class="col-md-4 mb-3">
                    <label for="genre" class="form-label">
                        {{ __('frontend.submissions.genre') }} <span class="text-danger">*</span>
                    </label>
                    <select class="form-select @error('genre') is-invalid @enderror" id="genre" name="genre" required>
                        <option value="">Select Genre</option>
                        <option value="fiction" {{ old('genre') === 'fiction' ? 'selected' : '' }}>Fiction</option>
                        <option value="non-fiction" {{ old('genre') === 'non-fiction' ? 'selected' : '' }}>Non-Fiction</option>
                        <option value="poetry" {{ old('genre') === 'poetry' ? 'selected' : '' }}>Poetry</option>
                        <option value="children" {{ old('genre') === 'children' ? 'selected' : '' }}>Children's Books</option>
                        <option value="young-adult" {{ old('genre') === 'young-adult' ? 'selected' : '' }}>Young Adult</option>
                        <option value="mystery" {{ old('genre') === 'mystery' ? 'selected' : '' }}>Mystery</option>
                        <option value="romance" {{ old('genre') === 'romance' ? 'selected' : '' }}>Romance</option>
                        <option value="sci-fi" {{ old('genre') === 'sci-fi' ? 'selected' : '' }}>Science Fiction</option>
                        <option value="fantasy" {{ old('genre') === 'fantasy' ? 'selected' : '' }}>Fantasy</option>
                        <option value="biography" {{ old('genre') === 'biography' ? 'selected' : '' }}>Biography</option>
                        <option value="memoir" {{ old('genre') === 'memoir' ? 'selected' : '' }}>Memoir</option>
                        <option value="self-help" {{ old('genre') === 'self-help' ? 'selected' : '' }}>Self-Help</option>
                        <option value="business" {{ old('genre') === 'business' ? 'selected' : '' }}>Business</option>
                        <option value="history" {{ old('genre') === 'history' ? 'selected' : '' }}>History</option>
                        <option value="other" {{ old('genre') === 'other' ? 'selected' : '' }}>Other</option>
                    </select>
                    @error('genre')<div class="invalid-feedback">{{ $message }}</div>@enderror
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="word_count" class="form-label">
                        {{ __('frontend.submissions.word_count') }} <span class="text-danger">*</span>
                    </label>
                    <input type="number" class="form-control @error('word_count') is-invalid @enderror"
                           id="word_count" name="word_count" value="{{ old('word_count') }}"
                           min="10000" max="200000" required>
                    @error('word_count')<div class="invalid-feedback">{{ $message }}</div>@enderror
                    <div class="form-text">Between 10,000 and 200,000 words</div>
                </div>
            </div>

            <div class="mb-3">
                <label for="description" class="form-label">
                    {{ __('frontend.submissions.description') }} <span class="text-danger">*</span>
                </label>
                <textarea class="form-control @error('description') is-invalid @enderror"
                          id="description" name="description" rows="5" required
                          minlength="100" maxlength="2000">{{ old('description') }}</textarea>
                @error('description')<div class="invalid-feedback">{{ $message }}</div>@enderror
                <div class="form-text">Minimum 100 characters, maximum 2000 characters</div>
            </div>

            <div class="mb-3">
                <label for="target_audience" class="form-label">
                    {{ __('frontend.submissions.target_audience') }} <span class="text-danger">*</span>
                </label>
                <textarea class="form-control @error('target_audience') is-invalid @enderror"
                          id="target_audience" name="target_audience" rows="3" required
                          maxlength="500">{{ old('target_audience') }}</textarea>
                @error('target_audience')<div class="invalid-feedback">{{ $message }}</div>@enderror
            </div>

            <div class="mb-3">
                <label for="marketing_plan" class="form-label">
                    {{ __('frontend.submissions.marketing_plan') }}
                </label>
                <textarea class="form-control @error('marketing_plan') is-invalid @enderror"
                          id="marketing_plan" name="marketing_plan" rows="3"
                          maxlength="1000">{{ old('marketing_plan') }}</textarea>
                @error('marketing_plan')<div class="invalid-feedback">{{ $message }}</div>@enderror
            </div>
        </div>
    </div>

    <!-- File Uploads Section -->
    <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
            <h5 class="mb-0"><i class="fas fa-upload me-2"></i>File Uploads</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="manuscript" class="form-label">
                        {{ __('frontend.submissions.manuscript') }} <span class="text-danger">*</span>
                    </label>
                    <input type="file" class="form-control @error('manuscript') is-invalid @enderror"
                           id="manuscript" name="manuscript" accept=".pdf,.doc,.docx" required>
                    @error('manuscript')<div class="invalid-feedback">{{ $message }}</div>@enderror
                    <div class="form-text">PDF, DOC, or DOCX (Max 10MB)</div>
                </div>

                <div class="col-md-4 mb-3">
                    <label for="synopsis" class="form-label">
                        {{ __('frontend.submissions.synopsis') }}
                    </label>
                    <input type="file" class="form-control @error('synopsis') is-invalid @enderror"
                           id="synopsis" name="synopsis" accept=".pdf,.doc,.docx">
                    @error('synopsis')<div class="invalid-feedback">{{ $message }}</div>@enderror
                    <div class="form-text">PDF, DOC, or DOCX (Max 2MB)</div>
                </div>

                <div class="col-md-4 mb-3">
                    <label for="cover_letter" class="form-label">
                        {{ __('frontend.submissions.cover_letter') }}
                    </label>
                    <input type="file" class="form-control @error('cover_letter') is-invalid @enderror"
                           id="cover_letter" name="cover_letter" accept=".pdf,.doc,.docx">
                    @error('cover_letter')<div class="invalid-feedback">{{ $message }}</div>@enderror
                    <div class="form-text">PDF, DOC, or DOCX (Max 2MB)</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Terms and Conditions -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="form-check">
                <input class="form-check-input @error('agree_terms') is-invalid @enderror"
                       type="checkbox" id="agree_terms" name="agree_terms" required>
                <label class="form-check-label" for="agree_terms">
                    {{ __('frontend.submissions.agree_terms') }} <span class="text-danger">*</span>
                </label>
                @error('agree_terms')<div class="invalid-feedback">{{ $message }}</div>@enderror
            </div>
        </div>
    </div>

    <!-- Submit Button -->
    <div class="text-center">
        <button type="submit" class="btn btn-primary btn-lg">
            <i class="fas fa-upload me-2"></i>
            {{ __('frontend.submit') }}
        </button>
    </div>
</form>
