@props(['event'])

<form action="{{ route('events.registration.store', $event->slug ?? 'sample-event') }}" method="POST" class="event-registration-form">
    @csrf

    <!-- Personal Information Section -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-user me-2"></i>Personal Information</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="name" class="form-label">
                        {{ __('frontend.events.name') }} <span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                           id="name" name="name" value="{{ old('name') }}" required>
                    @error('name')<div class="invalid-feedback">{{ $message }}</div>@enderror
                </div>

                <div class="col-md-6 mb-3">
                    <label for="email" class="form-label">
                        {{ __('frontend.events.email') }} <span class="text-danger">*</span>
                    </label>
                    <input type="email" class="form-control @error('email') is-invalid @enderror"
                           id="email" name="email" value="{{ old('email') }}" required>
                    @error('email')<div class="invalid-feedback">{{ $message }}</div>@enderror
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="phone" class="form-label">
                        {{ __('frontend.events.phone') }} <span class="text-danger">*</span>
                    </label>
                    <input type="tel" class="form-control @error('phone') is-invalid @enderror"
                           id="phone" name="phone" value="{{ old('phone') }}" required>
                    @error('phone')<div class="invalid-feedback">{{ $message }}</div>@enderror
                </div>
            </div>
        </div>
    </div>

    <!-- Professional Information Section -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>Professional Information</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="profession" class="form-label">
                        {{ __('frontend.events.profession') }} <span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control @error('profession') is-invalid @enderror"
                           id="profession" name="profession" value="{{ old('profession') }}" required>
                    @error('profession')<div class="invalid-feedback">{{ $message }}</div>@enderror
                </div>

                <div class="col-md-6 mb-3">
                    <label for="organization" class="form-label">
                        {{ __('frontend.events.organization') }}
                    </label>
                    <input type="text" class="form-control @error('organization') is-invalid @enderror"
                           id="organization" name="organization" value="{{ old('organization') }}">
                    @error('organization')<div class="invalid-feedback">{{ $message }}</div>@enderror
                </div>
            </div>
        </div>
    </div>

    <!-- Special Requirements Section -->
    <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
            <h5 class="mb-0"><i class="fas fa-utensils me-2"></i>Special Requirements</h5>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <label for="dietary_restrictions" class="form-label">
                    {{ __('frontend.events.dietary_restrictions') }}
                </label>
                <textarea class="form-control @error('dietary_restrictions') is-invalid @enderror"
                          id="dietary_restrictions" name="dietary_restrictions" rows="2"
                          maxlength="500">{{ old('dietary_restrictions') }}</textarea>
                @error('dietary_restrictions')<div class="invalid-feedback">{{ $message }}</div>@enderror
            </div>

            <div class="mb-3">
                <label for="special_requirements" class="form-label">
                    {{ __('frontend.events.special_requirements') }}
                </label>
                <textarea class="form-control @error('special_requirements') is-invalid @enderror"
                          id="special_requirements" name="special_requirements" rows="2"
                          maxlength="500">{{ old('special_requirements') }}</textarea>
                @error('special_requirements')<div class="invalid-feedback">{{ $message }}</div>@enderror
            </div>
        </div>
    </div>

    <!-- Emergency Contact Section -->
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0"><i class="fas fa-phone-alt me-2"></i>Emergency Contact</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="emergency_contact" class="form-label">
                        {{ __('frontend.events.emergency_contact') }} <span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control @error('emergency_contact') is-invalid @enderror"
                           id="emergency_contact" name="emergency_contact" value="{{ old('emergency_contact') }}" required>
                    @error('emergency_contact')<div class="invalid-feedback">{{ $message }}</div>@enderror
                </div>

                <div class="col-md-6 mb-3">
                    <label for="emergency_phone" class="form-label">
                        {{ __('frontend.events.emergency_phone') }} <span class="text-danger">*</span>
                    </label>
                    <input type="tel" class="form-control @error('emergency_phone') is-invalid @enderror"
                           id="emergency_phone" name="emergency_phone" value="{{ old('emergency_phone') }}" required>
                    @error('emergency_phone')<div class="invalid-feedback">{{ $message }}</div>@enderror
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Information Section -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Additional Information</h5>
        </div>
        <div class="card-body">
            <div class="mb-3">
                <label for="how_did_you_hear" class="form-label">
                    {{ __('frontend.events.how_did_you_hear') }}
                </label>
                <select class="form-select @error('how_did_you_hear') is-invalid @enderror"
                        id="how_did_you_hear" name="how_did_you_hear">
                    <option value="">Select an option</option>
                    <option value="website" {{ old('how_did_you_hear') === 'website' ? 'selected' : '' }}>Website</option>
                    <option value="social_media" {{ old('how_did_you_hear') === 'social_media' ? 'selected' : '' }}>Social Media</option>
                    <option value="email" {{ old('how_did_you_hear') === 'email' ? 'selected' : '' }}>Email</option>
                    <option value="friend" {{ old('how_did_you_hear') === 'friend' ? 'selected' : '' }}>Friend/Colleague</option>
                    <option value="advertisement" {{ old('how_did_you_hear') === 'advertisement' ? 'selected' : '' }}>Advertisement</option>
                    <option value="other" {{ old('how_did_you_hear') === 'other' ? 'selected' : '' }}>Other</option>
                </select>
                @error('how_did_you_hear')<div class="invalid-feedback">{{ $message }}</div>@enderror
            </div>

            <div class="mb-3">
                <label for="comments" class="form-label">
                    {{ __('frontend.events.comments') }}
                </label>
                <textarea class="form-control @error('comments') is-invalid @enderror"
                          id="comments" name="comments" rows="3"
                          maxlength="1000">{{ old('comments') }}</textarea>
                @error('comments')<div class="invalid-feedback">{{ $message }}</div>@enderror
            </div>
        </div>
    </div>

    <!-- Terms and Conditions -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="form-check">
                <input class="form-check-input @error('agree_terms') is-invalid @enderror"
                       type="checkbox" id="agree_terms" name="agree_terms" required>
                <label class="form-check-label" for="agree_terms">
                    {{ __('frontend.events.agree_terms') }} <span class="text-danger">*</span>
                </label>
                @error('agree_terms')<div class="invalid-feedback">{{ $message }}</div>@enderror
            </div>
        </div>
    </div>

    <!-- Submit Button -->
    <div class="text-center">
        <button type="submit" class="btn btn-primary btn-lg">
            <i class="fas fa-user-plus me-2"></i>
            {{ __('frontend.events.register_now') }}
        </button>
    </div>
</form>
