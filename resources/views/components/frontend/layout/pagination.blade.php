@props(['paginator'])

@if ($paginator->hasPages())
    <nav aria-label="Pagination Navigation" class="mt-4">
        <ul class="pagination justify-content-center">
            {{-- Previous Page Link --}}
            @if ($paginator->onFirstPage())
                <li class="page-item disabled" aria-disabled="true">
                    <span class="page-link">
                        <i class="fas fa-chevron-left me-1"></i>
                        {{ __('pagination.previous') }}
                    </span>
                </li>
            @else
                <li class="page-item">
                    <a class="page-link" href="{{ $paginator->previousPageUrl() }}" rel="prev">
                        <i class="fas fa-chevron-left me-1"></i>
                        {{ __('pagination.previous') }}
                    </a>
                </li>
            @endif

            {{-- Pagination Elements --}}
            @foreach ($paginator->getUrlRange(1, $paginator->lastPage()) as $page => $url)
                @if ($page == $paginator->currentPage())
                    <li class="page-item active" aria-current="page">
                        <span class="page-link">{{ $page }}</span>
                    </li>
                @else
                    <li class="page-item">
                        <a class="page-link" href="{{ $url }}">{{ $page }}</a>
                    </li>
                @endif
            @endforeach

            {{-- Next Page Link --}}
            @if ($paginator->hasMorePages())
                <li class="page-item">
                    <a class="page-link" href="{{ $paginator->nextPageUrl() }}" rel="next">
                        {{ __('pagination.next') }}
                        <i class="fas fa-chevron-right ms-1"></i>
                    </a>
                </li>
            @else
                <li class="page-item disabled" aria-disabled="true">
                    <span class="page-link">
                        {{ __('pagination.next') }}
                        <i class="fas fa-chevron-right ms-1"></i>
                    </span>
                </li>
            @endif
        </ul>
    </nav>

    {{-- Results Information --}}
    <div class="text-center text-muted mt-3">
        <small>
            {{ __('Showing') }}
            <span class="fw-semibold">{{ $paginator->firstItem() }}</span>
            {{ __('to') }}
            <span class="fw-semibold">{{ $paginator->lastItem() }}</span>
            {{ __('of') }}
            <span class="fw-semibold">{{ $paginator->total() }}</span>
            {{ __('results') }}
        </small>
    </div>
@endif
