<!-- Header -->
<header id="main-header" class="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm transition-all duration-200">
    <div class="container mx-auto">
        <div class="flex justify-between items-center py-4">
            <!-- Logo -->
            <div class="flex items-center flex-shrink-0">
                <a href="{{ route('home') }}"
                    class="flex items-center focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                    <img src="{{ asset('images/sharjah_literary_agency.webp') }}" loading="lazy"
                        alt="Sharjah Literary Agency" width="124px" height="72px" class="hidden md:block">
                    <img src="{{ asset('images/sharjah_literary_agency_small.svg') }}" loading="lazy"
                        alt="Sharjah Literary Agency" width="39px" height="50px" class="md:hidden block">
                </a>
            </div>

            <!-- Unified Navigation - Responsive for both desktop and mobile -->
            <nav role="navigation" aria-label="Main navigation" id="main-navigation" class="main-nav">
                <ul class="nav-list">
                    <li><a href="{{ route('home') }}" class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}">Home</a></li>
                    <li><a href="{{ route('about') }}" class="nav-link {{ request()->routeIs('about') ? 'active' : '' }}">About Us</a></li>
                    <li><a href="{{ route('services') }}" class="nav-link {{ request()->routeIs('services') ? 'active' : '' }}">Services</a></li>
                    <li><a href="{{ route('clients') }}" class="nav-link {{ request()->routeIs('clients') ? 'active' : '' }}">Client</a></li>
                    <li><a href="{{ route('submissions.index') }}" class="nav-link {{ request()->routeIs('submissions.index') ? 'active' : '' }}">Submissions Form</a></li>
                    <li><a href="{{ route('news.index') }}" class="nav-link {{ request()->routeIs('news.index') ? 'active' : '' }}">News</a></li>
                    <li><a href="{{ route('contact.index') }}" class="nav-link {{ request()->routeIs('contact.index') ? 'active' : '' }}">Contact Us</a></li>
                </ul>
            </nav>

            <!-- Right Side Controls -->
            <div class="flex items-center gap-4" id="header-controls">
                <!-- Language Selector -->
                <div class="relative">
                    <div class="custom-selectbox">
                        <button id="language-selector" class="selectbox-button" aria-expanded="false"
                            aria-haspopup="true">
                            <span class="selectbox-text">EN</span>
                            <span class="icon-arrow-down selectbox-arrow ms-2"></span>
                        </button>
                        <div id="language-menu" class="selectbox-dropdown w-[70px]">
                            <div class="selectbox-options">
                                <button type="button" class="selectbox-option active" data-value="en">
                                    <span class="option-text">EN</span>
                                </button>
                                <button type="button" class="selectbox-option" data-value="ar">
                                    <span class="option-text">AR</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mobile Menu Button -->
                <button id="mobile-menu-button" class="lg:hidden p-2 rounded-md text-gray-800" aria-expanded="false"
                    aria-controls="main-navigation">
                    <span class="sr-only">Open main menu</span>
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Mobile Menu Close Button (positioned absolutely) -->
        <button id="mobile-menu-close" class="mobile-menu-close-btn p-2 rounded-md text-gray-800"
            aria-label="Close mobile menu">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                </path>
            </svg>
        </button>
    </div>
</header>
