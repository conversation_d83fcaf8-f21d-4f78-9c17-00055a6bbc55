<div class="dropdown language-switcher">
    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" id="languageDropdown" data-bs-toggle="dropdown" data-bs-display="static" aria-expanded="false">
        <i class="fas fa-globe me-1"></i>
        @if(app()->getLocale() === 'ar')
            {{ __('navigation.arabic') }}
        @else
            {{ __('navigation.english') }}
        @endif
    </button>
    <ul class="dropdown-menu {{ app()->getLocale() === 'ar' ? 'dropdown-menu-start' : 'dropdown-menu-end' }}" aria-labelledby="languageDropdown">
        <li>
            <a class="dropdown-item {{ app()->getLocale() === 'en' ? 'active' : '' }}" href="{{ route('lang.switch', 'en') }}">
                <i class="fas fa-check me-2 {{ app()->getLocale() === 'en' ? '' : 'invisible' }}"></i>
                {{ __('navigation.english') }}
            </a>
        </li>
        <li>
            <a class="dropdown-item {{ app()->getLocale() === 'ar' ? 'active' : '' }}" href="{{ route('lang.switch', 'ar') }}">
                <i class="fas fa-check me-2 {{ app()->getLocale() === 'ar' ? '' : 'invisible' }}"></i>
                {{ __('navigation.arabic') }}
            </a>
        </li>
    </ul>
</div>
