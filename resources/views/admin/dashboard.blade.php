@extends('admin.layouts.page')

@push('css')
<style>
.pagination .page-link {
    border: 1px solid #dee2e6;
    color: #6c757d;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    border-radius: 0;
}

.pagination .page-link:hover {
    z-index: 2;
    color: #0056b3;
    text-decoration: none;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    z-index: 3;
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    cursor: auto;
    background-color: #fff;
    border-color: #dee2e6;
}

.pagination .page-item:first-child .page-link {
    margin-left: 0;
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
}

.pagination .page-item:last-child .page-link {
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
}

.card-footer {
    padding: 0.75rem 1.25rem;
    background-color: rgba(0,0,0,.03);
    border-top: 1px solid rgba(0,0,0,.125);
}
</style>
@endpush

@section('content')
<div class="row">
    <div class="col-md-6 col-xl-3 mb-3">
        <div class="card widget-card bg-night-gradient">
            <div class="widget-wrapper text-white">
                <div class="widget-content-left">
                    <span class="widget-heading mb-0">{{ __('admin.dashboard.user') }}</span>
                    <p class="widget-subheading">{{ __('admin.dashboard.total_users') }}</p>
                    <h5>{{$totalUsers}}</h5>
                </div>
                <div class="widget-content-right">
                    <div class="widget-icon text-white d-flex flex-column align-items-center">
                        <span class="inic inic-person"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 col-xl-3 mb-3">
        <div class="card widget-card bg-skyblue-gradient">
            <div class="widget-wrapper text-white">
                <div class="widget-content-left">
                    <span class="widget-heading mb-0">{{ __('admin.dashboard.admin') }}</span>
                    <p class="widget-subheading">{{ __('admin.dashboard.total_admins') }}</p>
                    <h5>{{$admins->count()}}</h5>
                </div>
                <div class="widget-content-right">
                    <div class="widget-icon text-white d-flex flex-column align-items-center">
                        <span class="inic inic-account-circle"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 col-xl-3 mb-3">
        <div class="card widget-card bg-green-gradient">
            <div class="widget-wrapper text-white">
                <div class="widget-content-left">
                    <span class="widget-heading mb-0">{{ __('admin.dashboard.role') }}</span>
                    <p class="widget-subheading">{{ __('admin.dashboard.total_roles') }}</p>
                    <h5>{{$roles->count()}}</h5>
                </div>
                <div class="widget-content-right">
                    <div class="widget-icon text-white d-flex flex-column align-items-center">
                        <span class="inic inic-flag"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- <div class="col-md-6 col-xl-3 mb-3">
        <div class="card widget-card bg-purple-gradient">
            <div class="widget-wrapper text-white">
                <div class="widget-content-left">
                    <span class="widget-heading mb-0">{{ __('admin.dashboard.module') }}</span>
                    <p class="widget-subheading">{{ __('admin.dashboard.total_modules') }}</p>
                    <h5>{{$modules}}</h5>
                </div>
                <div class="widget-content-right">
                    <div class="widget-icon text-warning d-flex flex-column align-items-center">
                        <span class="inic inic-analysis"></span>
                    </div>
                </div>
            </div>
        </div>
    </div> --}}
    {{-- <div class="col-md-6 col-xl-7 mb-3">
        <div class="card">
            <figure class="highcharts-figure">
                <div id="container"></div>

            </figure>
        </div>
    </div> --}}
    {{-- <div class="col-md-6 col-xl-5 mb-3">
        <div class="card">
            <figure class="highcharts-figure">
                <div id="pie-container"></div>

            </figure>
        </div>
    </div> --}}
    <div class="col-md-6 col-xl-6 mb-3">
        <div class="card">
            <h4>Users</h4>
            <table class="table" aria-describedby="Users">
                <thead>
                    <tr>
                        <th scope="col">{{ __('admin.dashboard.name') }}</th>
                        <th scope="col">{{ __('admin.dashboard.email') }}</th>
                        <th scope="col">{{ __('admin.dashboard.status') }}</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($users as $user)

                    <tr>
                        <td>
                            <a href="{{ route('admin.users.show', encrypt($user->id)) }}"
                                class="avatar-item d-inline-flex align-items-center">
                                <div class="avatar flex-shrink-0 me-2 br-8 overflow-hidden">
                                    <img src="{{getImage($user->photo,config('constants.folders.user')) }}"
                                        alt="{{ $user->first_name .' ' .$user->last_name  }}" width="24" height="24">
                                </div>
                                <div class="avatar-info">
                                    <h6 class="mb-0 text-truncate" data-bs-toggle="tooltip" data-bs-placement="right"
                                        data-bs-title="{{ $user->first_name .' ' .$user->last_name }}">{{
                                        $user->first_name .' ' .$user->last_name }}</h6>
                                </div>
                            </a>
                        </td>
                        <td>
                            {{ $user->email }}
                        </td>
                        <td>{{ $user->getStatusLabelAttribute() }}</td>

                    </tr>
                    @empty
                    <tr>
                        <td colspan="3" class="text-center fw-500">{{ __('admin.dashboard.no_records_found') }}</td>
                    </tr>
                    @endforelse
                </tbody>
            </table>

            <!-- Pagination Links -->
            <div class="card-footer">
                {{ $users->links() }}
            </div>
        </div>
    </div>
    <div class="col-md-6 col-xl-6 mb-3">
        <div class="card">
            <h4>Roles</h4>
            <table class="table" aria-describedby="Roles">
                <thead>
                    <tr>
                        <th scope="col">{{ __('admin.dashboard.display_name') }}</th>
                        <th scope="col">{{ __('admin.dashboard.description') }}</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($roles as $role)

                    <tr>
                        <td>
                            <div class="avatar-info">
                                <h6 class="mb-0 text-truncate" data-bs-toggle="tooltip" data-bs-placement="right"
                                    data-bs-title="{{ $role->display_name }}">{{ $role->display_name }}</h6>
                            </div>
                        </td>
                        <td>
                            {{ $role->description }}
                        </td>

                    </tr>
                    @empty
                    <tr>
                        <td colspan="5" class="text-danger text-center fw-500">{{ __('admin.dashboard.no_records_found') }}</td>
                    </tr>
                    @endif
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection
{{-- @include('plugins.high-chart') --}}
