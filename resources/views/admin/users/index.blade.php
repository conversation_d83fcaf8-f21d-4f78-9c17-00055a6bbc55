@extends('admin.layouts.page')

@section('title', __('admin.users.users'))

@section('breadcrumb')
    <ol class="breadcrumb-item active">{{ __('admin.users.users') }}</ol>
@endsection

@permission('create-users')
    @section('action_buttons')
    {!! addButton(route('admin.users.create'), __('admin.users.user')) !!}
    @endsection
@endpermission

@section('content')
    {!! $dataGridHtml !!}
@endsection

@push('scripts')
    <script type="module">
        $(document).on('change','.change-status', function(){
            var id =  $(this).attr('data-id');
            $.ajax({
                url: "{{ route('admin.users.change_status')}}",
                type: "post",
                data: {"id": id},
                datatype: 'json',
                success: function (response) {
                    toastr.success(response.message);
                    window.inic_grid_users.refresh();
                },
                error: function (response) {
                    toastr.error(response.message);
                }
            });
        });

        $(document).on('click', '.delete-grid-row', function(event) {
            event.preventDefault();
            let url = $(this).attr('data-url');
            SwalConfirm(url, function(url) {
                $.ajax({
                    url: url,
                    type: 'delete',
                    datatype: 'json',
                    success: function(response) {
                        toastr.success(response.message);
                        window.inic_grid_users.refresh();
                    },
                    error: function(response) {
                        toastr.error(response.message);
                    }
                });
            })
        });

        $(document).on('click', '.grid-bulk-action', function(event) {
            event.preventDefault();
            var id = [];var error = 0;
            $('.check_approval').each(function(index){
                if($(this).prop('checked') == true){
                    id.push($(this).val());
                    error = 1;
                }
            });

            var url= "{{ route('admin.users.bulkAction') }}";
            var status=$(this).data('status');
            var type=$(this).data('action-type');

            if(error == 0){
                toastr.error("{{ __('admin.users.select_one_user') }}");
            }else if(error == 1){
                SwalConfirm(url, function(url) {
                    $.ajax({
                        url: url,
                        type: 'post',
                        data: {id: id,status:status,type:type},
                        success: function(response) {
                            if(response.status == 'success'){
                                toastr.success(response.message);
                            }else{
                                toastr.error(response.message);
                            }
                            window.inic_grid_users.refresh();
                        },
                        error: function(response) {
                            toastr.error(response.message);
                        }

                    });
                });
            }
        });

    </script>
@endpush
