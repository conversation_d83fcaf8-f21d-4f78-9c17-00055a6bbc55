<div class="card">
    <div class="card-body content p-0">
        <div class="row">
            <div class="col-md-3 mb-3">
                <div class="align-items-center">
                    <div class="form-group">
                        <label class="form-label">{{ __('admin.global_setting.site_maintenance') }}</label>
                        <input type="checkbox" name="site_maintenance" class="form-check-input siteMaintenance"
                            {{ isset($global_settings) && $global_settings->site_maintenance == 0 ? '' : 'checked' }}>
                    </div>
                </div>
            </div>

            {{-- <div class="col-md-3 mb-3">
                <div class="align-items-center">
                    <label class="form-label">{{ __('admin.global_setting.grid_column_search') }}</label>
                    <input type="checkbox" name="column_search_status"
                        class="form-check-input column-search mr-4 my-switch"
                        {{ isset($global_settings) && $global_settings->column_search_status == 'false' ? '' : 'checked' }}>
                </div>
            </div> --}}

            <div class="col-md-3 mb-3">
                <div class="align-items-center">
                    <label for="date_format_set"
                        class="form-label">{{ __('admin.global_setting.date_formate') }}</label>
                    <select name="date_format_set" id="date_format_set" class="form-control date-format-set">
                        @foreach (trans('admin.date_format') as $key => $value)
                            <option value="{{ $key }}"
                                {{ isset($global_settings) && $global_settings->date_format_set == $key ? 'selected' : '' }}>
                                {{ $value }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="align-items-center">
                    <label for="time_format_set" class="form-label">{{ __('admin.global_setting.time_format') }}</label>
                    <select name="time_format_set" id="time_format_set" class="form-control time-format-set">
                        @foreach (trans('admin.time_format') as $key => $value)
                            <option value="{{ $key }}"
                                {{ isset($global_settings) && $global_settings->time_format_set == $key ? 'selected' : '' }}>
                                {{ $value }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>
        <hr>

        <div class="row">
            <div class="col-md-3 mb-3">
                <div class="align-items-center">
                    <label for="decimals" class="form-label">{{ __('admin.global_setting.deci_nu_format') }}</label>
                    <select name="decimals" id="decimals" class="form-control number-format-set">
                        @foreach (trans('admin.deci_nu_format') as $key => $value)
                            <option value="{{ $key }}"
                                {{ isset($global_settings) && $global_settings->decimals == $key ? 'selected' : '' }}>
                                {{ $value }}</option>
                        @endforeach


                    </select>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="align-items-center">
                    <label for="deci_point"
                        class="form-label">{{ __('admin.global_setting.deci_point_format') }}</label>
                    <select name="deci_point" id="deci_point" class="form-control number-format-set">
                        @foreach (trans('admin.deci_point_format') as $key => $value)
                            <option value="{{ $key }}"
                                {{ isset($global_settings) && $global_settings->deci_point == $key ? 'selected' : '' }}>
                                {{ $value }}</option>
                        @endforeach

                    </select>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="align-items-center">
                    <label for="deci_separator"
                        class="form-label">{{ __('admin.global_setting.deci_sep_format') }}</label>
                    <select name="deci_separator" id="deci_separator" class="form-control number-format-set">
                        @foreach (trans('admin.deci_sep_format') as $key => $value)
                            <option value="{{ $key }}"
                                {{ isset($global_settings) && $global_settings->deci_separator == $key ? 'selected' : '' }}>
                                {{ $value }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>


    </div>
</div>

@push('scripts')
    <script type="module">
        $(document).on('click', '.siteMaintenance', function() {
            var url = "{{ route('admin.settings.site_maintenance') }}";
            if ($(this).prop('checked') == true) {
                var value = 1;
                var text = "{{ __('admin.global_setting.site_maintenance_enable') }}";
            } else {
                var value = 0;
                var text = "{{ __('admin.global_setting.site_maintenance_disable') }}";
            }

            let payload = {
                text: text
            }
            SwalWarning(url, payload, function(url) {
                $.ajax({
                    url: url,
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        site_maintenance: value
                    },
                    success: function(response) {
                        if (response.status == "success") {
                            toastr.success(response.message);
                        } else {
                            toastr.error(response.message);
                        }
                    },
                    error: function(response) {
                        toastr.error(response.message);
                    }
                });
            })
        });

        var columnSearchStatus = $('.column-search');
        columnSearchStatus.change(function() {
            // Check if the checkbox is checked
            var value = false;
            if (columnSearchStatus.prop('checked')) {
                // Show the message if checked
                value = true;
            }

            $.ajax({
                url: "{{ route('admin.settings.grid_column_search') }}",
                type: 'POST',
                dataType: 'json',
                data: {
                    column_search_status: value
                },
                success: function(response) {
                    toastr.success(response.message);
                },
                error: function(response) {
                    toastr.error(response.message);
                }
            });
        });

        var dateFormatSet = $('.date-format-set');
        dateFormatSet.change(function() {
            var value = $(this).val();
            $.ajax({
                url: "{{ route('admin.settings.date_format_set') }}",
                type: 'POST',
                dataType: 'json',
                data: {
                    date_format_set: value
                },
                success: function(response) {
                    toastr.success(response.message);
                },
                error: function(response) {
                    toastr.error(response.message);
                }
            });
        });

        var timeFormatSet = $('.time-format-set');
        timeFormatSet.change(function() {
            var value = $(this).val();
            $.ajax({
                url: "{{ route('admin.settings.time_format_set') }}",
                type: 'POST',
                dataType: 'json',
                data: {
                    time_format_set: value
                },
                success: function(response) {
                    toastr.success(response.message);
                },
                error: function(response) {
                    toastr.error(response.message);
                }
            });
        });

        var timeFormatSet = $('.number-format-set');
        timeFormatSet.change(function() {
            var decimals = $('select[name="decimals"]').val();
            var decimalPoint = $('select[name="deci_point"]').val();
            var separator = $('select[name="deci_separator"]').val();
            console.log(decimals, decimalPoint, separator);
            $.ajax({
                url: "{{ route('admin.settings.number_format_set') }}",
                type: 'POST',
                dataType: 'json',
                data: {
                    decimals: decimals,
                    deci_point: decimalPoint,
                    deci_separator: separator
                },
                success: function(response) {
                    toastr.success(response.message);
                },
                error: function(response) {
                    toastr.error(response.message);
                }
            });
        });
    </script>
@endpush
