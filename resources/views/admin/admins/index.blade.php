@extends('admin.layouts.page')

@section('title', __('admin.admins.admin_user'))

@section('breadcrumb')
    <ol class="breadcrumb-item active">{{ __('admin.admins.admin_user') }}</ol>
@endsection

@permission('create-admins')
    @section('action_buttons')
        {!! addButton(route('admin.admins.create'), __('admin.admins.admin_user')) !!}
    @endsection
@endpermission

@section('content')
    {!! $dataGridHtml !!}
@endsection

@push('scripts')
    <script type="module">

        $(document).on('click', '.delete-grid-row', function (event) {
            event.preventDefault();
            let url = $(this).attr('data-url');
            SwalConfirm(url, function (url) {
                $.ajax({
                    url: url,
                    type: 'delete',
                    datatype: 'json',
                    success: function (response) {
                        toastr.success(response.message);
                        window.inic_grid_admins.refresh();
                    },
                    error: function (response) {
                        SwalAlert(response.status, response.message);
                        toastr.error(response.message);
                    }
                });
            })
        })

        $(document).on('change','.change-status', function(){
            var id =  $(this).attr('data-id');
            $.ajax({
                url: "{{ route('admin.admins.change_status')}}",
                type: "post",
                data: {"id": id},
                datatype: 'json',
                success: function (response) {
                    toastr.success(response.message);
                    window.inic_grid_admins.refresh();
                },
                error: function (response) {
                    toastr.error(response.message);
                }
            });
        });

        $(document).on('click', '.grid-bulk-action', function(event) {
            event.preventDefault();
            var id = [];var error = 0;
            $('.check_approval').each(function(index){
                if($(this).prop('checked') == true){
                    id.push($(this).val());
                    error = 1;
                }
            });

            var url= "{{ route('admin.admins.bulkAction') }}";
            var status=$(this).data('status');
            var type=$(this).data('action-type');

            if(error == 0){
                toastr.error("{{ __('admin.admins.select_one_user') }}");
            }else if(error == 1){
                SwalConfirm(url, function(url) {
                    $.ajax({
                        url: url,
                        type: 'post',
                        data: {id: id,status:status,type:type},
                        success: function(response) {
                            if(response.status == 'success'){
                                toastr.success(response.message);
                            }else{
                                toastr.error(response.message);
                            }
                            window.inic_grid_admins.refresh();
                        },
                        error: function(response) {
                            toastr.error(response.message);
                        }

                    });
                });
            }
        });


    </script>
@endpush
