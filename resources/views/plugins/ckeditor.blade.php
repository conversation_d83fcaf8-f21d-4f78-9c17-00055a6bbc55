@if(!defined('LOAD_CKEDITOR'))
    @push('scripts')
        <script src="https://cdn.ckeditor.com/4.12.1/standard/ckeditor.js"></script>
        <script type="module">
            window.options = {
                filebrowserUploadUrl: "{{ route('admin.ckeditor.upload', ['_token' => csrf_token() ]) }}",
                filebrowserUploadMethod: 'form',
            };

            function initializeCKEditor(elementId, options) {
                let editor = CKEDITOR.replace(elementId, options);
                editor.config.versionCheck = false;
                editor.config.allowedContent = true;
                editor.filebrowserUploadMethod = 'form';

                // Disable drag & drop image upload with alert
                editor.on('drop', function(evt) {
                    if (
                        evt.data &&
                        evt.data.dataTransfer &&
                        evt.data.dataTransfer.getFilesCount()
                    ) {
                        evt.cancel(); // Stop the drop
                        alert('Drag & drop image upload is disabled. Please use the upload button instead.');
                    }
                });

                // Auto-update textarea on content change
                for (var i in CKEDITOR.instances) {
                    if (CKEDITOR.instances.hasOwnProperty(i)) {
                        CKEDITOR.instances[i].on('change', function () {
                            CKEDITOR.instances[i].updateElement();
                        });
                    }
                }
            }

            window.initializeCKEditor = initializeCKEditor;
        </script>
    @endpush

    @php define('LOAD_CKEDITOR', true) @endphp
@endif
