<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@if(trim($__env->yieldContent('title'))) @yield('title') - @endif {{ config('app.name', 'Laravel') }}</title>
    <meta name="description"
        content="We offer you guidance and trustworthy allies to build a career safe from the lure of instant gratification and the pipe dream of easy money and fame.">
    <meta name="keywords" content="Manuscript Evaluation, Contract Negotiation">

    <!-- SEO and Security Meta Tags -->
    <meta name="robots" content="noindex, nofollow">
    <meta name="author" content="Sharjah Literary Agency">

    <link rel="shortcut icon" href="{{ asset('favicon.ico') }}" type="image/x-icon" crossorigin="anonymous">
    <link rel="icon" href="{{ asset('favicon.ico') }}" type="image/x-icon" crossorigin="anonymous">

    <!-- Accessibility improvements -->
    <meta name="theme-color" content="#ffffff">
    <meta name="color-scheme" content="light">

    @vite(['resources/front/js/app.js', 'resources/front/css/app.css', 'resources/front/css/style.css'])
    @stack('styles')
</head>

<body class="min-h-screen flex flex-col antialiased">
    <div id="page-loader">
        <div class="loader"></div>
    </div>

    <a href="#main-content" id="skip-to-content"
        class="sr-only focus:not-sr-only focus:absolute focus:top-2 focus:left-2 bg-primary-600 text-white px-4 py-2 rounded-md z-50">
        Skip to main content
    </a>
    @yield('body')
    @stack('scripts')

</body>

</html>
