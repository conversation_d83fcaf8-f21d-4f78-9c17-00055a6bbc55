<?php

return [
    // Success Messages
    'contact_success' => 'Your message has been sent successfully. We will get back to you soon.',
    'submission_success' => 'Your manuscript has been submitted successfully. We will review it and get back to you within 4-6 weeks.',
    'event_registration_success' => 'Your event registration has been completed successfully. You will receive a confirmation email shortly.',
    'newsletter_success' => 'Thank you for subscribing to our newsletter!',

    // Error Messages
    'contact_error' => 'There was an error sending your message. Please try again.',
    'submission_error' => 'There was an error submitting your manuscript. Please try again.',
    'event_registration_error' => 'There was an error processing your registration. Please try again.',
    'newsletter_error' => 'There was an error subscribing to the newsletter. Please try again.',

    // Validation Messages
    'required' => 'This field is required.',
    'email' => 'Please enter a valid email address.',
    'file' => 'Please select a valid file.',
    'max_file_size' => 'File size cannot exceed :max MB.',
    'accepted_file_types' => 'Only :types files are allowed.',

    // General Messages
    'no_results_found' => 'No results found.',
    'loading' => 'Loading...',
    'search_results_for' => 'Search results for ":query"',
    'page_not_found' => 'Page not found.',
    'server_error' => 'Server error. Please try again later.',
];
