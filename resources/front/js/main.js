/**
 * SLA Project - Main JavaScript
 * Minimal vanilla JavaScript for accessibility and interactions
 * Features: Language toggle (EN/AR), RTL support
 */

(function () {
    'use strict';
    // Get the current year for footer copyright
    document.getElementById("year").textContent = new Date().getFullYear();
    // Application state - get current language from server-side locale
    let currentLanguage = document.documentElement.getAttribute('lang') || 'en';

    // DOM Content Loaded
    document.addEventListener('DOMContentLoaded', function () {
        initializeApp();
        // Hide page loader after everything else is initialized
        const pageLoader = document.getElementById('page-loader');
        if (pageLoader) {
            pageLoader.classList.add('hidden');
            pageLoader.addEventListener('transitionend', function () {
                pageLoader.remove();
            }, { once: true });
        }
    });

    /**
     * Initialize the application
     */
    function initializeApp() {
        initializeLanguage();
        initializeAccessibility();
        initializeInteractions();
        initializeAnimations();
        initializeHeader();
        initializeSwiper();
        initializeFileUpload();
        initializeFAQFunctionality();
        initializeFilterSelectbox();
    }

    /**
     * Initialize language (EN/AR with RTL support)
     */
    function initializeLanguage() {
        // Apply saved language
        applyLanguage(currentLanguage);
        // Update language selector
        updateLanguageSelector();
        // Initialize custom selectbox
        initializeLanguageSelectbox();
    }

    /**
     * Initialize custom language selectbox
     */
    function initializeLanguageSelectbox() {
        const langSelector = document.getElementById('language-selector');
        const langMenu = document.getElementById('language-menu');
        const langOptions = document.querySelectorAll('#language-menu .selectbox-option');

        if (!langSelector || !langMenu || !langOptions.length) return;
        // Handle button click
        langSelector.addEventListener('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            const isOpen = langSelector.getAttribute('aria-expanded') === 'true';
            if (isOpen) {
                closeLangSelectbox();
            } else {
                openLangSelectbox();
            }
        });
        // Handle option selection - now using anchor links, so we don't prevent default
        langOptions.forEach(option => {
            option.addEventListener('click', function (e) {
                // Don't prevent default - let the anchor link navigate
                e.stopPropagation();
                closeLangSelectbox();
                // The page will reload with the new language, so no need to update manually
            });
        });
        // Handle keyboard navigation
        langSelector.addEventListener('keydown', function (e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                langSelector.click();
            } else if (e.key === 'Escape') {
                closeLangSelectbox();
            }
        });
    }

    /**
     * Open language selectbox
     */
    function openLangSelectbox() {
        const langSelector = document.getElementById('language-selector');
        const langMenu = document.getElementById('language-menu');
        if (langSelector && langMenu) {
            langSelector.setAttribute('aria-expanded', 'true');
            langMenu.classList.add('active');
            // Focus first option
            const firstOption = langMenu.querySelector('.selectbox-option');
            if (firstOption) firstOption.focus();
        }
    }
    /**
     * Close language selectbox
     */
    function closeLangSelectbox() {
        const langSelector = document.getElementById('language-selector');
        const langMenu = document.getElementById('language-menu');
        if (langSelector && langMenu) {
            langSelector.setAttribute('aria-expanded', 'false');
            langMenu.classList.remove('active');
        }
    }
    /**
     * Select language and update interface (for frontend-only changes)
     * Note: Actual language switching is handled by Laravel backend
     */
    function selectLanguage(newLang) {
        currentLanguage = newLang;
        applyLanguage(currentLanguage);
        updateLanguageSelector();
        announceToScreenReader(`Language changed to ${currentLanguage === 'en' ? 'English' : 'Arabic'}`);
    }
    /**
     * Apply language settings
     */
    function applyLanguage(lang) {
        const html = document.documentElement;
        if (lang === 'ar') {
            html.setAttribute('lang', 'ar');
            html.setAttribute('dir', 'rtl');
            html.classList.add('rtl');
        } else {
            html.setAttribute('lang', 'en');
            html.setAttribute('dir', 'ltr');
            html.classList.remove('rtl');
        }
        // Update text content based on language
        updateTextContent(lang);
    }

    /**
     * Update text content based on language
     */
    function updateTextContent(lang) {
        const translations = {
            en: {
                'nav-about': 'About Us',
                'nav-services': 'Services',
                'nav-client': 'Client',
                'nav-submissions': 'Submissions',
                'nav-news': 'News',
                'nav-contact': 'Contact Us',
                'skip-to-content': 'Skip to main content'
            },
            ar: {
                'nav-about': 'من نحن',
                'nav-services': 'الخدمات',
                'nav-client': 'العميل',
                'nav-submissions': 'المقترحات',
                'nav-news': 'الأخبار',
                'nav-contact': 'تواصل معنا',
                'skip-to-content': 'انتقل إلى المحتوى الرئيسي'
            }
        };

        const texts = translations[lang];
        if (!texts) {
            console.warn('No translations found for language:', lang);
            return;
        }
        Object.keys(texts).forEach(key => {
            const element = document.getElementById(key) || document.querySelector(`[data-i18n="${key}"]`);
            if (element) {
                element.textContent = texts[key];
            }
        });
    }

    /**
     * Update language selector
     */
    function updateLanguageSelector() {
        const langSelector = document.getElementById('language-selector');
        const langOptions = document.querySelectorAll('#language-menu .selectbox-option');
        const langText = langSelector ? langSelector.querySelector('.selectbox-text') : null;

        if (langSelector && langText) {
            // Update button text
            langText.textContent = currentLanguage.toUpperCase();

            // Update active state of options
            langOptions.forEach(option => {
                const optionValue = option.getAttribute('data-value');
                if (optionValue === currentLanguage) {
                    option.classList.add('active');
                } else {
                    option.classList.remove('active');
                }
            });
        }
    }

    /**
     * Initialize filter selectbox
     */
    function initializeFilterSelectbox() {
        const filterSelector = document.getElementById('filter-selector');
        const filterMenu = document.getElementById('filter-menu');
        const filterOptions = document.querySelectorAll('#filter-menu .selectbox-option');
        if (!filterSelector || !filterMenu || !filterOptions.length) return;
        // Handle button click
        filterSelector.addEventListener('click', function (e) {
            e.preventDefault();
            e.stopPropagation();

            const isOpen = filterSelector.getAttribute('aria-expanded') === 'true';

            if (isOpen) {
                closeFilterSelectbox();
            } else {
                openFilterSelectbox();
            }
        });

        // Handle option selection
        filterOptions.forEach(option => {
            option.addEventListener('click', function (e) {
                e.preventDefault();
                e.stopPropagation();

                const selectedValue = this.getAttribute('data-value');
                const selectedText = this.querySelector('.option-text').textContent;

                selectFilterOption(selectedValue, selectedText);
                closeFilterSelectbox();
            });
        });

        // Handle keyboard navigation
        filterSelector.addEventListener('keydown', function (e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                filterSelector.click();
            } else if (e.key === 'Escape') {
                closeFilterSelectbox();
            }
        });

        // Handle keyboard navigation in dropdown
        filterOptions.forEach((option, index) => {
            option.addEventListener('keydown', function (e) {
                if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                    e.preventDefault();
                    const direction = e.key === 'ArrowDown' ? 1 : -1;
                    const nextIndex = index + direction;
                    if (filterOptions[nextIndex]) {
                        filterOptions[nextIndex].focus();
                    }
                } else if (e.key === 'Escape') {
                    closeFilterSelectbox();
                    filterSelector.focus();
                }
            });
        });
    }

    /**
     * Open filter selectbox
     */
    function openFilterSelectbox() {
        const filterSelector = document.getElementById('filter-selector');
        const filterMenu = document.getElementById('filter-menu');

        if (filterSelector && filterMenu) {
            // Close other dropdowns first
            closeLangSelectbox();
            filterSelector.setAttribute('aria-expanded', 'true');
            filterMenu.classList.add('active');
            // Focus first option
            const firstOption = filterMenu.querySelector('.selectbox-option');
            if (firstOption) firstOption.focus();
        }
    }

    /**
     * Close filter selectbox
     */
    function closeFilterSelectbox() {
        const filterSelector = document.getElementById('filter-selector');
        const filterMenu = document.getElementById('filter-menu');

        if (filterSelector && filterMenu) {
            filterSelector.setAttribute('aria-expanded', 'false');
            filterMenu.classList.remove('active');
        }
    }

    /**
     * Select filter option and update interface
     */
    function selectFilterOption(value, text) {
        const filterSelector = document.getElementById('filter-selector');
        const filterText = filterSelector ? filterSelector.querySelector('.selectbox-text') : null;
        const filterOptions = document.querySelectorAll('#filter-menu .selectbox-option');

        if (filterSelector && filterText) {
            // Update button text
            filterText.textContent = text;

            // Update active state of options
            filterOptions.forEach(option => {
                const optionValue = option.getAttribute('data-value');
                if (optionValue === value) {
                    option.classList.add('active');
                } else {
                    option.classList.remove('active');
                }
            });

            // Here you can add logic to actually filter the Client
            announceToScreenReader(`Filter changed to ${text}`);

            // Apply filtering logic
            filterClientList(value);
        }
    }

    /**
     * Filter Client based on selected value
     */
    function filterClientList(filterValue) {
        // This function can be expanded to actually filter the displayed clients
        // based on the selected filter value
        console.log('Filtering clients by:', filterValue);
        // Example: Hide/show client items based on filter
        // const clientItems = document.querySelectorAll('[data-category]');
        // clientItems.forEach(item => {
        //     const itemCategory = item.getAttribute('data-category');
        //     if (!filterValue || itemCategory === filterValue) {
        //         item.style.display = 'block';
        //     } else {
        //         item.style.display = 'none';
        //     }
        // });
    }

    /**
     * Initialize accessibility features
     */
    function initializeAccessibility() {
        // Skip link functionality
        const skipLink = document.querySelector('#skip-to-content');
        if (skipLink) {
            skipLink.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.focus();
                    target.scrollIntoView();
                }
            });
        }

        // Announce dynamic content changes to screen readers
        const liveRegion = document.createElement('div');
        liveRegion.setAttribute('aria-live', 'polite');
        liveRegion.setAttribute('aria-atomic', 'true');
        liveRegion.className = 'sr-only';
        liveRegion.id = 'live-region';
        document.body.appendChild(liveRegion);
    }

    /**
     * Initialize interactive elements
     */
    function initializeInteractions() {
        // Close dropdowns and mobile menu when clicking outside
        document.addEventListener('click', function (e) {
            const isLanguageClick = e.target.closest('#language-selector') || e.target.closest('#language-menu');
            const isFilterClick = e.target.closest('#filter-selector') || e.target.closest('#filter-menu');
            const isViewClick = e.target.closest('#view-selector') || e.target.closest('#view-menu');
            const isMobileMenuClick = e.target.closest('#mobile-menu-button') || e.target.closest('#main-navigation') || e.target.closest('#mobile-menu-close');

            // Close dropdowns if clicking outside them (but don't interfere with calendar view selector)
            if (!isLanguageClick && !isFilterClick && !isViewClick) {
                closeAllDropdowns();
            }

            // Close mobile menu if clicking outside navigation (but not on the hamburger button or close button)
            const isMenuOpen = document.body.classList.contains('mobile-menu-open');
            if (!isMobileMenuClick && isMenuOpen) {
                // Click anywhere outside the navigation to close
                closeMobileMenu();
            }
        });

        // Close dropdowns and mobile menu on escape key
        document.addEventListener('keydown', function (e) {
            if (e.key === 'Escape') {
                closeAllDropdowns();

                // Close mobile menu if open
                const isMenuOpen = document.body.classList.contains('mobile-menu-open');
                if (isMenuOpen) {
                    closeMobileMenu();
                }
            }
        });
    }

    /**
     * Initialize animations with respect to user preferences
     */
    function initializeAnimations() {
        // Check for reduced motion preference only
        const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');

        // Animation logic can be added here when needed
        if (!prefersReducedMotion.matches) {
            // Future animation code can go here
        }
    }


    /**
     * Announce message to screen readers
     */
    function announceToScreenReader(message) {
        const liveRegion = document.getElementById('live-region');
        if (liveRegion) {
            liveRegion.textContent = message;
            setTimeout(() => {
                liveRegion.textContent = '';
            }, 1000);
        }
    }

    /**
     * Initialize header functionality
     */
    function initializeHeader() {
        // Mobile menu functionality
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mainNavigation = document.getElementById('main-navigation');
        const mobileMenuCloseBtn = document.getElementById('mobile-menu-close');

        if (mobileMenuButton && mainNavigation) {
            // Handle mobile menu button (hamburger)
            mobileMenuButton.addEventListener('click', function (e) {
                e.preventDefault();
                const isOpen = document.body.classList.contains('mobile-menu-open');

                if (isOpen) {
                    closeMobileMenu();
                } else {
                    openMobileMenu();
                }
            });

            // Handle keyboard navigation for mobile menu button
            mobileMenuButton.addEventListener('keydown', function (e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    mobileMenuButton.click();
                }
            });
        }

        // Handle mobile menu close button
        if (mobileMenuCloseBtn) {
            mobileMenuCloseBtn.addEventListener('click', function (e) {
                e.preventDefault();
                closeMobileMenu();
            });

            // Handle keyboard navigation for close button
            mobileMenuCloseBtn.addEventListener('keydown', function (e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    closeMobileMenu();
                }
                if (e.key === 'Escape') {
                    closeMobileMenu();
                }
            });
        }
        // Sticky header on scroll
        initializeStickyHeader();
    }
    /**
     * Close all dropdowns
     */
    function closeAllDropdowns() {
        closeLangSelectbox();
        closeFilterSelectbox();
    }
    /**
     * Open mobile menu
     */
    function openMobileMenu() {
        const mainNavigation = document.getElementById('main-navigation');
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const body = document.body;

        if (mainNavigation && mobileMenuButton) {
            closeLangSelectbox();
            closeFilterSelectbox();
            // Add body class to prevent scrolling and manage state
            body.classList.add('mobile-menu-open');
            // Update button state
            mobileMenuButton.setAttribute('aria-expanded', 'true');
            // Announce to screen readers
            announceToScreenReader('Mobile menu opened');
            // Trap focus in mobile menu
            trapFocusInMobileMenu();
        }
    }
    /**
     * Close mobile menu
     */
    function closeMobileMenu() {
        const mainNavigation = document.getElementById('main-navigation');
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const body = document.body;

        if (mainNavigation && mobileMenuButton) {
            // Remove body class to restore scrolling
            body.classList.remove('mobile-menu-open');
            // Update button state
            mobileMenuButton.setAttribute('aria-expanded', 'false');
            // Remove tab navigation listener if it exists
            if (mainNavigation._removeTabListener) {
                mainNavigation._removeTabListener();
                delete mainNavigation._removeTabListener;
            }
            // Announce to screen readers
            announceToScreenReader('Mobile menu closed');
            // Return focus to mobile menu button
            mobileMenuButton.focus();
        }
    }

    /**
     * Trap focus within mobile menu for accessibility
     */
    function trapFocusInMobileMenu() {
        const mainNavigation = document.getElementById('main-navigation');
        const closeButton = document.getElementById('mobile-menu-close');
        if (!mainNavigation) return;
        // Get all focusable elements within the mobile menu
        const focusableElements = [
            ...mainNavigation.querySelectorAll('a[href]'),
            closeButton
        ].filter(el => el && !el.disabled);

        if (focusableElements.length === 0) return;

        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        // Focus first navigation link
        setTimeout(() => {
            firstElement.focus();
        }, 100);

        // Handle tab navigation
        function handleTabNavigation(e) {
            if (e.key !== 'Tab') return;

            if (e.shiftKey) {
                // Shift + Tab
                if (document.activeElement === firstElement) {
                    e.preventDefault();
                    lastElement.focus();
                }
            } else {
                // Tab
                if (document.activeElement === lastElement) {
                    e.preventDefault();
                    firstElement.focus();
                }
            }
        }

        // Add event listener to document for tab navigation
        const addTabListener = () => {
            document.addEventListener('keydown', handleTabNavigation);
        };

        // Remove event listener when menu closes
        const removeTabListener = () => {
            document.removeEventListener('keydown', handleTabNavigation);
        };

        // Add listener when menu opens
        addTabListener();

        // Store reference to remove listener when menu closes
        mainNavigation._removeTabListener = removeTabListener;
    }

    /**
     * Initialize sticky header behavior
     */
    function initializeStickyHeader() {
        const header = document.getElementById('main-header');
        if (!header) return;

        let lastScrollY = window.scrollY;
        let headerHeight = header.offsetHeight;

        function handleScroll() {
            const currentScrollY = window.scrollY;

            // Add shadow when scrolled
            if (currentScrollY > 10) {
                header.classList.add('shadow-lg');
            } else {
                header.classList.remove('shadow-lg');
            }

            lastScrollY = currentScrollY;
        }

        // Throttle scroll events for performance
        let scrollTimeout;
        window.addEventListener('scroll', function () {
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }
            scrollTimeout = setTimeout(handleScroll, 10);
        });
    }

    /**
     * Initialize Swiper for Featured Authors section
     */
    function initializeSwiper() {
        // Wait for Swiper to be available
        if (typeof Swiper === 'undefined') {
            console.warn('Swiper is not loaded');
            return;
        }
        // Hero Banner swiper
        const heroBannerSwiper = new Swiper('.hero-banner-swiper', {
            slidesPerView: 5,           // always show five
            centeredSlides: true,
            spaceBetween: 32,
            loop: true,                 // optional – remove if you dislike cloning
            speed: 3000,
            a11y: false,
            autoplay: {
                delay: 0,
                disableOnInteraction: false,
            },
            breakpoints: {
                0: {
                    slidesPerView: 1.75
                },
                768: {
                    slidesPerView: 3
                },
                1200: {
                    slidesPerView: 5
                }
            },
            on: {
                init: function() {
                    initializeHeroBannerInteractions(this);
                }
            }
        });
        // Initialize Swiper
        const authorsSwiper = new Swiper('.authors-swiper', {
            // Basic settings
            slidesPerView: 1.2,
            spaceBetween: 20,
            centeredSlides: false,
            // Auto height
            autoHeight: false,
            // Loop for infinite scroll
            loop: false,
            // Navigation arrows
            navigation: false,
            // Pagination
            pagination: false,
            // Responsive breakpoints
            breakpoints: {
                375: {
                    slidesPerView: 1.8
                },
                480: {
                    slidesPerView: 2.5
                },
                640: {
                    slidesPerView: 3
                },
                768: {
                    slidesPerView: 3.7
                },
                1024: {
                    slidesPerView: 4.5
                },
                1200: {
                    slidesPerView: 5,
                    spaceBetween: 32
                }
            },

            // Accessibility - disable automatic ARIA roles since we handle them manually in HTML
            a11y: false,

            // Keyboard control
            keyboard: {
                enabled: true,
                onlyInViewport: true,
            },

            // Grab cursor
            grabCursor: true,

            // Touch events
            touchRatio: 1,
            touchAngle: 45,

            // Speed and effects
            speed: 600,

            // Events
            on: {
                init: function () {
                    console.log('Authors Swiper initialized');
                    announceToScreenReader('Authors carousel loaded');
                }
            }
        });

        // Handle reduced motion preference
        const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
        if (prefersReducedMotion.matches) {
            authorsSwiper.disable();
            heroBannerSwiper.disable();
            console.log('Swiper disabled due to reduced motion preference');
        }

        // Listen for changes in motion preference
        prefersReducedMotion.addListener((mediaQuery) => {
            if (mediaQuery.matches) {
                authorsSwiper.disable();
                heroBannerSwiper.disable();
            } else {
                authorsSwiper.enable();
                heroBannerSwiper.enable();
            }
        });

        const sliderGroup = [authorsSwiper, heroBannerSwiper];
        return sliderGroup;
    }

    // function updateTransforms(swiper){
    //     swiper.slides.forEach((slide, i) => {
    //       // diff from current active slide, works with looping too
    //       const diff = i - swiper.activeIndex;
    //       let offset = 0;           // far slides → 0 px

    //       if (diff ===  1 || diff === -4) offset = 100;  // next
    //       if (diff === -1 || diff ===  4) offset = 100;  // prev
    //       if (diff ===  0)              offset = 150;    // active

    //       slide.style.top = `${offset}px`;
    //     });
    //   }

    /**
     * Initialize file upload functionality for submission forms
     */
    function initializeFileUpload() {
        const fileInput = document.getElementById('file-upload');
        const fileNameDiv = document.getElementById('file-name');

        // Only initialize if both elements exist (for submission page)
        if (!fileInput || !fileNameDiv) return;

        fileInput.addEventListener('change', function (e) {
            const file = e.target.files[0];
            if (file) {
                fileNameDiv.textContent = `Selected file: ${file.name}`;
                fileNameDiv.classList.remove('hidden');
                announceToScreenReader(`File selected: ${file.name}`);
            } else {
                fileNameDiv.classList.add('hidden');
                announceToScreenReader('File selection cleared');
            }
        });
    }

    /**
     * Initialize hero banner interactions (hover pause/resume, click redirect)
     */
    function initializeHeroBannerInteractions(swiperInstance) {
        if (!swiperInstance || !swiperInstance.slides) return;

        // Track autoplay state
        let isAutoplayPaused = false;

        // Add hover and click event listeners to each slide
        swiperInstance.slides.forEach((slide, index) => {
            // Hover functionality - pause/resume autoplay
            slide.addEventListener('mouseenter', function() {
                if (swiperInstance.autoplay && swiperInstance.autoplay.running && !isAutoplayPaused) {
                    swiperInstance.autoplay.stop();
                    isAutoplayPaused = true;
                    announceToScreenReader('Banner autoplay paused');
                }
            });

            slide.addEventListener('mouseleave', function() {
                if (swiperInstance.autoplay && isAutoplayPaused) {
                    swiperInstance.autoplay.start();
                    isAutoplayPaused = false;
                    announceToScreenReader('Banner autoplay resumed');
                }
            });

            // Click functionality - redirect to URL
            slide.addEventListener('click', function(e) {
                // Prevent default swiper behavior during click
                e.preventDefault();
                e.stopPropagation();

                // Look for URL in various attributes and elements
                let targetUrl = null;

                // Check for data-url attribute on the slide
                targetUrl = slide.getAttribute('data-url');

                // Check for href attribute on the slide (if it's an anchor)
                if (!targetUrl && slide.tagName === 'A') {
                    targetUrl = slide.getAttribute('href');
                }

                // Check for anchor tag inside the slide
                if (!targetUrl) {
                    const anchorElement = slide.querySelector('a[href]');
                    if (anchorElement) {
                        targetUrl = anchorElement.getAttribute('href');
                    }
                }

                // Check for data-href attribute
                if (!targetUrl) {
                    targetUrl = slide.getAttribute('data-href');
                }

                // If URL found, redirect
                if (targetUrl && targetUrl !== '#' && targetUrl !== '') {
                    // Handle different URL types
                    if (targetUrl.startsWith('http') || targetUrl.startsWith('//')) {
                        // External URL - open in new tab
                        window.open(targetUrl, '_blank', 'noopener,noreferrer');
                        announceToScreenReader('Opening link in new tab');
                    } else {
                        // Internal URL - navigate in same window
                        window.location.href = targetUrl;
                        announceToScreenReader(`Navigating to ${targetUrl}`);
                    }
                } else {
                    console.warn('No valid URL found for hero banner slide', slide);
                }
            });

            // Add keyboard support for accessibility
            slide.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    slide.click();
                }
            });

            // Make slides focusable for keyboard navigation
            if (!slide.hasAttribute('tabindex')) {
                slide.setAttribute('tabindex', '0');
            }

            // Add role and aria-label for accessibility
            if (!slide.hasAttribute('role')) {
                slide.setAttribute('role', 'button');
            }

            if (!slide.hasAttribute('aria-label')) {
                const slideText = slide.textContent?.trim() || `Hero slide ${index + 1}`;
                slide.setAttribute('aria-label', `Click to open: ${slideText}`);
            }
        });

        // Handle window focus/blur for autoplay management
        window.addEventListener('blur', function() {
            if (swiperInstance.autoplay && swiperInstance.autoplay.running) {
                swiperInstance.autoplay.stop();
                isAutoplayPaused = true;
            }
        });

        window.addEventListener('focus', function() {
            if (swiperInstance.autoplay && isAutoplayPaused) {
                swiperInstance.autoplay.start();
                isAutoplayPaused = false;
            }
        });
    }

    /**
     * Initialize FAQ functionality for tabs and accordion
     */
    function initializeFAQFunctionality() {
        // Tab functionality - only handle panel switching
        const tabs = document.querySelectorAll('.custom-tab');
        const panels = document.querySelectorAll('.custom-tab-panel');

        // Only initialize if elements exist (for FAQ page)
        if (!tabs.length || !panels.length) return;

        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                const targetPanel = this.getAttribute('aria-controls');

                // Update tabs - remove active state and styling
                tabs.forEach(t => {
                    t.classList.remove('active', 'border-primary-500', 'text-primary-500');
                    t.classList.add('border-transparent', 'text-gray-900');
                    t.setAttribute('aria-selected', 'false');
                });

                // Update active tab - add active state and styling
                this.classList.add('active', 'border-primary-500', 'text-primary-500');
                this.classList.remove('border-transparent', 'text-gray-900');
                this.setAttribute('aria-selected', 'true');

                // Update panels
                panels.forEach(panel => {
                    panel.classList.add('hidden');
                });

                // Show active panel
                const targetElement = document.getElementById(targetPanel);
                if (targetElement) {
                    targetElement.classList.remove('hidden');
                    announceToScreenReader(`Switched to ${this.textContent} tab`);
                }
            });

            // Handle keyboard navigation for tabs
            tab.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.click();
                }
            });
        });

        // FAQ accordion functionality - Button elements
        const collapseButtons = document.querySelectorAll('.custom-collapse');

        collapseButtons.forEach(button => {
            button.addEventListener('click', function() {
                const targetId = this.getAttribute('data-target');
                const answer = document.getElementById(targetId);
                const icon = this.querySelector('.faq-icon');
                const isExpanded = this.getAttribute('aria-expanded') === 'true';

                if (!answer || !icon) return;

                if (isExpanded) {
                    // Collapse
                    answer.classList.add('hidden');
                    this.setAttribute('aria-expanded', 'false');
                    icon.classList.remove('icon-minus-cirlce');
                    icon.classList.add('icon-add-circle');
                    announceToScreenReader('FAQ collapsed');
                } else {
                    // Expand
                    answer.classList.remove('hidden');
                    this.setAttribute('aria-expanded', 'true');
                    icon.classList.remove('icon-add-circle');
                    icon.classList.add('icon-minus-cirlce');
                    announceToScreenReader('FAQ expanded');
                }
            });

            // Handle keyboard navigation for FAQ buttons
            button.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.click();
                }
            });
        });
    }

    // Utility functions
    window.SLA = {
        announceToScreenReader: announceToScreenReader,
        getCurrentLanguage: function () { return currentLanguage; },
        closeMobileMenu: closeMobileMenu,
        openMobileMenu: openMobileMenu,
        closeLangSelectbox: closeLangSelectbox
    };

})();
