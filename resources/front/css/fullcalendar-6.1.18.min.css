/* FullCalendar v6.1.18 - Essential Styles with Tailwind @apply */

.fc table {
  @apply border-collapse text-base w-full;
  border-spacing: 0;
}

.fc th {
  @apply text-center;
}

.fc th,
.fc td {
  @apply align-top p-0;
}

.fc-scrollgrid {
  @apply border-0 w-full rounded-none overflow-x-auto overflow-y-auto;
}

.fc-scrollgrid table {
  @apply w-full;
}

.fc .fc-col-header-cell {
  @apply bg-white font-semibold text-gray-900 p-4 text-base text-center uppercase tracking-wider border-r border-b border-gray-100;
  line-height: 19.5px;
  letter-spacing: 0.025em;
}

.fc-daygrid {
  @apply relative;
}

.fc-daygrid-body {
  @apply relative w-full;
}

.fc-daygrid-day {
  @apply border-r border-b bg-white p-0.5 align-top box-border border-gray-100;
  width: 14.285714%; /* 100% / 7 days */
}

.fc-daygrid-day:hover {
  @apply bg-primary-50;
}

.fc-daygrid-day-frame {
  @apply relative flex flex-col;
  min-height: 120px;
  height: auto;
}

.fc-daygrid-day-top {
  @apply relative;
}

.fc-daygrid-day-number {
  @apply relative z-10 p-2 no-underline text-gray-900 font-bold cursor-pointer transition-colors duration-200;
}

.fc-daygrid-day-number:hover {
  @apply text-primary-500 no-underline;
}

.fc-daygrid-day-number:focus {
  @apply outline-2 outline-primary-500 outline-offset-2;
}

.fc-daygrid-day-events {
  @apply mt-px;
}

.fc-daygrid-event-harness {
  @apply absolute left-0 right-0 mb-px;
}

.fc-daygrid-event {
  @apply relative z-10 rounded mx-0.5 my-px text-[0.75rem] text-gray-900;
  padding: 1px 4px;
}

.fc-event {
  @apply block relative text-white bg-primary-500 cursor-pointer rounded-2xl border-0 font-medium text-sm mx-0.5 my-px border-primary-500;
  padding: 4px 12px;
  line-height: 19px;
}

.fc-event:hover {
  @apply bg-primary-500 border-primary-500;
}

.fc-event-title {
  @apply px-0.5;
}

.fc-day-today {
  background-color: var(--fc-today-bg-color, rgba(255, 220, 40, 0.15));
}

.fc-day-past {
  @apply text-gray-100;
}

.fc-day-future {
  @apply text-gray-100;
}

.fc-toolbar {
  @apply flex justify-between items-center my-6;
}

.fc-toolbar-chunk {
  @apply flex items-center;
}

.fc-button-group {
  @apply relative inline-flex align-middle;
}

.fc-button {
  @apply border border-gray-100 bg-gray-100 text-gray-900 px-3 py-1.5 text-base rounded cursor-pointer select-none no-underline m-0 overflow-visible;
  line-height: 1.5;
  text-transform: none;
  -webkit-appearance: button;
}

.fc-button:hover {
  @apply bg-gray-100 border-gray-100 text-gray-900;
  background-color: var(--fc-button-hover-bg-color, #e9ecef);
  border-color: var(--fc-button-hover-border-color, #adb5bd);
}

.fc-button:focus {
  @apply outline-0;
  box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
}

.fc-button-primary {
  @apply bg-primary-500 border-primary-500 text-white;
}

.fc-button-primary:hover {
    @apply bg-primary-500 border-primary-500 text-white;
}

.fc-toolbar-title {
  @apply text-3xl m-0;
}

.fc-direction-ltr .fc-toolbar > * > :not(:first-child) {
  @apply ms-3;
}

.fc-scroller {
  @apply overflow-hidden relative;
}

.fc-theme-standard,
.fc-theme-standard .fc-scrollgrid {
  @apply border border-gray-100;
}

.fc-theme-standard td, 
.fc-theme-standard th {
  @apply border-gray-100;
}

/* Calendar container styling */
#calendar {
  @apply border-t border-l bg-white border-gray-100;
  border-right: none;
  border-bottom: none;
  border-radius: 0;
}

/* Limit calendar to 5 rows */
.fc-daygrid-body .fc-scrollgrid-sync-table tr:nth-child(n+6) {
  @apply hidden;
} 

/* Remove inner borders to prevent double borders */
.fc-theme-standard .fc-scrollgrid {
  @apply border-0;
}

.fc-theme-standard .fc-scrollgrid-sync-table {
  @apply border-0;
}

.fc-scrollgrid-sync-table {
  @apply border-0 border-collapse;
  border-spacing: 0;
}

.fc-scrollgrid-sync-table tbody {
  @apply border-0;
}

.fc-scrollgrid-sync-table td,
.fc-scrollgrid-sync-table th {
  @apply border-0 border-r border-b border-gray-100;
}

.fc-scrollgrid-sync-table td:last-child,
.fc-scrollgrid-sync-table th:last-child {
  @apply border-r-0;
}

.fc-more-link {
  @apply border-0 text-primary-500 cursor-pointer underline font-semibold m-0 p-0 bg-transparent text-[0.75rem];
  margin: 1px 3px;
}

.fc-more-link:hover {
  @apply text-primary-500;
}

.fc-popover {
  @apply absolute z-50 bg-white border border-gray-100;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.fc-popover-header {
  @apply flex flex-row justify-between items-center p-1 border-b border-gray-100;
}

.fc-popover-title {
  @apply mx-0.5 my-0;
}

.fc-popover-close {
  @apply cursor-pointer opacity-65 text-lg;
}

.fc-h-event .fc-event-main {
  @apply text-white;
}

.fc-daygrid-block-event .fc-event-time {
  @apply font-bold;
}

.fc-daygrid-block-event .fc-event-title {
  @apply mt-px;
}

.fc-liquid-hack .fc-daygrid-day-frame {
  @apply static;
}

.fc-liquid-hack .fc-daygrid-event-harness {
  @apply static;
}

/* Week and Day views */
.fc-timegrid {
  @apply relative z-10;
}

.fc-timegrid-divider {
  @apply pb-0.5;
  padding-left: 0;
  padding-right: 0;
  padding-top: 0;
}

.fc-timegrid-body {
  @apply relative z-10;
}

.fc-timegrid-axis-chunk {
  @apply relative;
}

.fc-timegrid-axis-chunk > table {
  @apply relative z-10;
}

.fc-timegrid-slots {
  @apply relative z-10;
}

.fc-timegrid-slot {
  @apply border-b border-gray-100;
  height: 1.5em;
}

.fc-timegrid-slot-minor {
  @apply border-t border-dotted;
}

.fc-timegrid-slot-label-cushion {
  @apply inline-block whitespace-nowrap;
}

.fc-timegrid-slot-label {
  @apply align-middle;
}

.fc-timegrid-axis {
  @apply align-middle;
}

.fc-timegrid-col-events {
  @apply relative z-10;
}

.fc-timegrid-col-bg {
  @apply z-20;
}

.fc-timegrid-event-harness {
  @apply absolute;
}

.fc-timegrid-event-harness > .fc-timegrid-event {
  @apply absolute z-10 left-0 right-0;
}

.fc-timegrid-event {
  @apply rounded px-px py-0.5 text-[0.75rem] text-gray-900;
}

.fc-timegrid-event .fc-event-main {
  @apply px-px py-px;
}

.fc-timegrid-event .fc-event-time {
  @apply font-bold text-[0.75rem] text-gray-900;
}

/* Hide default FullCalendar toolbar */
.fc-toolbar {
  @apply hidden;
}

/* Custom styles for calendar components */
.fc-day-today {
  @apply bg-primary-50;
}

/* Custom Toggle Button Styles */
.toggle-btn {
  @apply text-gray-900 bg-transparent px-6 py-3 flex items-center justify-center rounded-full font-semibold border-0 cursor-pointer transition-all duration-200 text-base uppercase;
}

.toggle-btn:hover {
  @apply text-primary-500;
}

.toggle-btn.active {
  @apply text-white bg-primary-500;
}

#prev-btn, #next-btn {
  @apply w-14 h-14 flex items-center justify-center border border-gray-100 bg-white rounded-full p-0 cursor-pointer transition-colors duration-200;
}

#prev-btn:hover, #next-btn:hover {
  @apply border-primary-500;
}

.today-btn {
  @apply px-6 py-4 flex items-center justify-center border bg-primary-50 rounded-full font-semibold text-primary-500 text-base uppercase cursor-pointer transition-all duration-200;
  border-color: #FFF2ED;
  background-color: #FFF2ED;
}

.today-btn:hover {
  @apply bg-primary-50 border-primary-500;
}

/* Custom view selector styles */
#view-selector.selectbox-button {
  @apply text-sm uppercase font-medium border-gray-100;
  min-width: 120px;
}

#view-selector.selectbox-button:hover {
  @apply border-primary-500;
}

#view-selector.selectbox-button:focus {
  @apply border-primary-500;
}

#prev-btn:focus, #next-btn:focus, .today-btn:focus {
  @apply outline-none border-primary-500;
}

/* Calendar title centering fix */
#custom-calendar-title {
  @apply text-center w-full m-0;
}

/* Arrow rotation for next button */
#next-btn .icon-arrow-down {
  @apply -rotate-90;
}

#prev-btn .icon-arrow-down {
  @apply rotate-90;
}

/* Responsive */
/* Ensure all day cells have equal dimensions */
.fc-scrollgrid table {
  @apply table-fixed;
}

.fc-daygrid-day-top {
  @apply relative flex justify-start items-start;
}

.fc-daygrid-day-events {
  @apply relative flex-1 mt-1;
}

@media (max-width: 768px) {
  .fc .fc-col-header-cell {
    @apply py-4 px-0 text-sm;
  }
  .fc-daygrid-event {
    @apply text-[0.75rem];
  }
  
  .fc-scrollgrid {
    @apply overflow-x-auto;
  }
}

@media (max-width: 480px) {
  .fc-daygrid-event {
    @apply px-0.5 py-px text-[0.75rem];
  }
  
  .fc-daygrid-day-number {
    @apply p-1 text-sm;
  }
  
  /* Responsive controls */
  #prev-btn, #next-btn {
    @apply w-12 h-12;
  }
  
  .today-btn, #view-selector {
    @apply px-4 py-3 text-xs;
  }
  
  .toggle-btn {
    @apply h-12 px-3 text-xs;
  }
}

/* Today's date highlighting */
.fc-day-today .fc-daygrid-day-number {
  @apply text-primary-500 font-bold;
}

/* Consistent day cell styling */
.fc-scrollgrid-section-body table {
  @apply w-full h-full;
}
