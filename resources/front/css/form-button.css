
/* Base button */
.btn {
  @apply relative inline-block cursor-pointer whitespace-nowrap text-base leading-[1.4] font-semibold text-primary-500 focus:outline-none focus:ring-0;
}
/* Primary button */
.btn-primary {
  @apply uppercase;
}
.btn-white {
  @apply uppercase text-white;
}
/* btn link underline animation */
.btn-line-effect::before,
.btn-line-effect::after {
  @apply content-[''] absolute left-0 top-[100%] w-full h-[1px] bg-[currentColor] transition-all duration-1000 pointer-events-none;
}
.btn-line-effect::before {
  @apply [transform-origin:0%_50%] [transform:scale3d(0,1,1)];
}   
.btn-line-effect:hover::before,.btn-line-effect:focus::before {
  @apply [transform:scale3d(1,1,1)];
}
.btn-line-effect::after {
  @apply top-[calc(100%_+_2px)] [transform-origin:100%_50%];
}
.btn-line-effect:hover::after,.btn-line-effect:focus::after {
  @apply [transform:scale3d(0,1,1)];
}

/* .btn {
  @apply inline-flex items-center justify-center px-5 py-3 lg:px-6 lg:py-4 border text-sm sm:text-base font-bold uppercase whitespace-nowrap rounded-full transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2;
}
.btn-primary {
  @apply bg-primary-500 text-white border-transparent hover:bg-primary-600 focus:ring-primary-500;
}

.btn-primary-outline {
  @apply bg-white text-primary-500 border-primary-500 hover:bg-primary-200 focus:ring-primary-500;
}
.btn-white {
  @apply bg-white text-primary-500 hover:bg-primary-200 focus:ring-offset-primary-500;
} */
/* Form input styles */
.form-input {
  @apply block w-full bg-white px-5 py-3 lg:px-6 lg:py-4 text-sm sm:text-base border border-transparent rounded-[29px] placeholder-gray-700 focus:outline-none focus:ring-primary-500 focus:border-primary-500;
}

.form-label {
  @apply block text-base leading-[1.4] font-normal text-gray-800 mb-1;
}

/* Custom Selectbox Styles */
.custom-selectbox {
  @apply relative;
}

.selectbox-button {
  @apply flex items-center justify-between min-w-16 px-3 py-2 border-0 border-gray-300 rounded-lg text-sm font-medium text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 cursor-pointer transition-all duration-200;
}

.selectbox-text {
  @apply font-medium;
}

.selectbox-arrow {
  @apply w-4 h-4 text-gray-800 transition-transform duration-200;
}

.selectbox-button[aria-expanded="true"] .selectbox-arrow {
  @apply rotate-180;
}

.selectbox-dropdown {
  @apply absolute right-0 mt-1 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible transform scale-95 transition-all duration-200 origin-top-right z-50;
}

.selectbox-dropdown.active {
  @apply opacity-100 visible scale-100;
}

.selectbox-options {
  @apply py-1;
}

.selectbox-option {
  @apply flex items-center justify-between w-full px-4 py-3 text-left text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100;
}

.selectbox-option.active {
  @apply bg-primary-50 text-primary-600;
}

.selectbox-option .option-text {
  @apply font-normal text-[1rem] leading-[1.3];
}

.selectbox-option .option-label {
  @apply text-xs text-gray-500;
}

.selectbox-option.active .option-label {
  @apply text-primary-500;
}