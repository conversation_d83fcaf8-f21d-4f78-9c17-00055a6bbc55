/* Import custom CSS files - must be at the top */
@import "fonts.css";
@import "form-button.css";
@import "navigation.css";
@import "swiper-custom.css";
@import "custom-component.css";
@import "timeline.css";
@import "fullcalendar-6.1.18.min.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Properties for consistent theming */
:root {
  --font-size-base: 16px;
  --color-text-800: #040708;
  --color-text-900: #494746;
  --color-background: #ffffff;
  --font-sans-normal: 'brando_sanstext', sans-serif;
  --font-sans-bold: 'brando_sansbold', sans-serif;
  --font-arabic: 'brando_arabicbold', sans-serif;
  --font-heading: 'Objektiv Mk2 Trial XBold', sans-serif;
  --font-heading-arabic: 'Araboto', sans-serif;
}

:root[lang="ar"] {
  body {
    font-family: var(--font-arabic);
  }

  h1 {
    font-family: var(--font-heading-arabic);
  }
}

/* Base styles */
@layer base {
  .fc {
    --fc-border-color: #D9D8D6;
    --fc-button-text-color: #ffffff;
    --fc-button-bg-color: #FF562F;
    --fc-button-border-color: #FF562F;
    --fc-button-hover-bg-color: #FF562F;
    --fc-button-hover-border-color: #FF562F;
    --fc-button-active-bg-color: #FF562F;
    --fc-button-active-border-color: #FF562F;
    --fc-event-bg-color: #FF562F;
    --fc-event-border-color: #FF562F;
    --fc-event-text-color: #ffffff;
    --fc-today-bg-color: #FFF9F7;
  }
  html {
    scroll-behavior: smooth;
  }

  body {
    font-feature-settings: "rlig" 1, "calt" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: var(--color-text-900);
    font-family: var(--font-sans-normal);
    font-size: var(--font-size-base);
    @apply leading-[1.2] text-[0.875rem] sm:text-base;
  }

  /* Focus styles for accessibility */
  /* :focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }

  :focus:not(:focus-visible) {
    outline: none;
  } */

  h1 {
    font-family: var(--font-heading);
    @apply text-gray-800 font-bold leading-[1.2] text-[2.5rem] sm:text-[3.5rem] lg:text-[4.5rem];
  }

  h2,.heading {
    font-family: var(--font-sans-bold);
    @apply text-gray-800 font-bold leading-[1.4] text-[2rem] sm:text-[2.5rem] lg:text-[3rem];
  }

  h3 {
    font-family: var(--font-sans-bold);
    @apply text-gray-800 font-bold leading-[1.4] text-[1.75rem] sm:text-[2rem] lg:text-[2.25rem];
  }

  h4 {
    font-family: var(--font-sans-bold);
    @apply text-gray-800 font-bold leading-[1.4] text-[1.25rem] sm:text-[1.5rem];
  }

  h5 {
    font-family: var(--font-sans-bold);
    @apply text-gray-900 font-bold leading-[1.4] text-[1rem];
  }

  h6 {
    font-family: var(--font-sans-bold);
    @apply text-gray-800 font-bold leading-[1.4] text-[1rem];
  }

  p {
    @apply leading-[1.4];
  }

}

/* Component styles */
@layer components {
  .card-shape {
    @apply relative border border-[#D9D8D6] mt-[64px] p-8 transition-all duration-200 hover:border-primary-500;
    @apply before:absolute before:-top-[2px] before:left-0 before:w-[29px] before:h-[3px] before:bg-white before:z-[1];
  }

  /* Container with responsive padding */
  .container {
    @apply mx-auto px-4 max-w-[1332px];
  }

  /* Language toggle button */
  .lang-toggle {
    @apply px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors duration-200;
  }

  /* Ensure backdrop blur works in all browsers */
  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
  }

  /* Custom dropdown animation */
  .dropdown-enter {
    opacity: 0;
    transform: scale(0.95);
    transition: all 0.2s ease-out;
  }

  .dropdown-enter-active {
    opacity: 1;
    transform: scale(1);
  }
}

/* Utility classes */
@layer utilities {
  /* Animation classes */
  .fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .slide-in {
    animation: slideIn 0.3s ease-out;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --color-primary: var(--color-primary);
    --color-text: var(--color-text-900);
    --color-background: #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .main-nav,
  .nav-link,
  .mobile-menu-close-btn,
  #header-logo {
    transition: none;
    animation: none !important;
  }

  body.mobile-menu-open .nav-list li {
    animation: none !important;
    transform: translateX(0);
    opacity: 1;
  }

  body.mobile-menu-open .nav-link::before {
    display: none;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}

/* Hero Section */
.hero-section {
  min-height: calc(100dvh - 104px);
}
.content-section, .acclaim-panel {
  p {
    @apply mb-5;
  }
}

/* Page Loader */
#page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #fff;
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: opacity 0.3s ease-in-out;
    opacity: 1;
    visibility: visible;
}

#page-loader.hidden {
    opacity: 0;
    visibility: hidden;
}

.loader {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #F05121;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}