@layer components {
    /* Body class management for mobile menu */
    body.mobile-menu-open {
      @apply overflow-hidden fixed w-full h-full;
    }
    .main-nav {
      @apply hidden lg:block;
    }
    .nav-list {
      @apply flex list-none m-0 p-0 gap-8;
    }
    .nav-link {
      @apply text-gray-800 no-underline font-normal py-2 transition-all duration-200 ease-in-out relative;
    }
    .nav-link:hover {
      @apply text-primary-500;
    }
    .nav-link:focus {
      /* @apply focus:outline focus:outline-2 focus:outline-primary-500 focus:outline-offset-2 focus:text-primary-500 focus:underline; */
      @apply transition-all duration-200 focus:outline-none focus:ring-0 focus:text-primary-500 focus:underline;
    }
    .nav-link.active {
      @apply text-primary-500;
    }
    .mobile-menu-close-btn {
      @apply hidden fixed top-[30px] right-4 z-[10001] items-center justify-center rounded-[12px] cursor-pointer transition-all duration-200 ease-in-out scale-[0.8] -rotate-90 opacity-0;
    }
  
    @media (min-width: 1024px) {
      body.mobile-menu-open {
        @apply overflow-auto static w-auto h-auto;
      }
  
      .mobile-menu-close-btn {
        @apply hidden;
      }
    }
  
    @media (max-width: 1023.98px) {
      .main-nav {
        @apply fixed top-0 left-0 w-full h-[100dvh] z-[10000];
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(10px);
        opacity: 0;
        visibility: hidden;
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }
  
      body.mobile-menu-open .main-nav {
        @apply flex flex-col justify-start items-center p-8 overflow-y-auto opacity-100 h-[calc(100dvh-72px)] top-[72px];
        visibility: visible;
      }
  
      body.mobile-menu-open .nav-list {
        @apply flex-col gap-0 items-center w-full;
      }
  
      body.mobile-menu-open .nav-list li {
        @apply w-full mb-6;
        transform: translateX(-50px);
        opacity: 0;
        animation: slideInMobile 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
      }
  
      body.mobile-menu-open .nav-link {
        @apply block w-full text-[2rem] font-medium text-gray-800 py-4 px-8 rounded-[12px] relative overflow-hidden;
      }
  
      body.mobile-menu-open .nav-link::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 86, 47, 0.1), transparent);
        transition: left 0.6s ease;
      }
  
      body.mobile-menu-open .nav-link:hover::before {
        left: 100%;
      }
  
      body.mobile-menu-open .nav-link:hover {
        @apply text-primary-500;
        background: rgba(255, 86, 47, 0.05);
        transform: translateY(-2px);
      }
  
      body.mobile-menu-open #header-logo {
        @apply fixed top-4 left-4 z-[10001];
        animation: logoSlideIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s both;
      }
  
      body.mobile-menu-open #header-controls {
        @apply opacity-0 pointer-events-none;
      }
  
      body.mobile-menu-open .mobile-menu-close-btn {
        @apply flex scale-100 rotate-0 opacity-100;
        animation: closeButtonSlideIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s both;
      }
  
      .mobile-menu-close-btn:hover {
        transform: scale(1.05);
      }
  
      .mobile-menu-close-btn:active {
        transform: scale(0.95);
      }
    }
  
    @media (max-width: 767.98px) {
      body.mobile-menu-open .nav-link {
        @apply text-[1.125rem] py-[0.75rem] px-3;
      }
  
      body.mobile-menu-open .nav-list li {
        @apply mb-4;
      }
    }
  }
  
  /* Animation keyframes (outside @layer) */
  @keyframes slideInMobile {
    from {
      transform: translateX(-50px);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes logoSlideIn {
    from {
      transform: translateX(-20px);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes closeButtonSlideIn {
    from {
      transform: scale(0.8) rotate(-90deg);
      opacity: 0;
    }
    to {
      transform: scale(1) rotate(0deg);
      opacity: 1;
    }
  }
  
  /* Dark mode */
  @media (prefers-color-scheme: dark) {
    @media (max-width: 1023px) {
      .main-nav {
        @apply bg-gray-800;
      }
  
      body.mobile-menu-open .nav-link {
        @apply text-white;
      }
  
      body.mobile-menu-open .nav-link:hover {
        @apply text-primary-500 bg-primary-500/10;
      }
  
      .mobile-menu-close-btn {
        @apply bg-gray-800/80 border-gray-500/50 text-gray-500;
      }

      .mobile-menu-close-btn:hover {
        @apply border-gray-500/50 text-gray-500;
      }
    }
  }
  