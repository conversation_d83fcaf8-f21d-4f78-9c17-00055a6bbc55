/* Timeline Section that matches the exact design */
.main-timeline-section {
    position: relative;
    .card-shape {
      @apply flex items-center justify-center min-h-[80px];
    }
    /* Horizontal center line */
    &::before {
      content: '';
      @apply bg-primary-500 w-full h-[2px] mt-[-1px] z-[1] left-0 absolute top-1/2;
    }
    
    .timeline-item {
      position: relative;
      &::before {
        content: attr(data-step);
        @apply text-primary-500 absolute top-[calc(100%+14px)] left-1/2 -translate-x-1/2 font-bold text-[24px] z-[10];
      }
      
      &::after {
        content: '';
        @apply bg-primary-500 absolute left-1/2 -translate-x-1/2 w-[2px] z-[5];
      }
      
      &:nth-child(odd) {
        .card-shape {
          @apply mb-[56px];
        }
        &::before {
        margin-top: -5px;
         
        }
        &::after {
          @apply top-[calc(100%-56px)] h-[56px];
        }
      }
      
      &:nth-child(even) {
        @apply self-end;
        &::before {
          @apply top-0 mt-[-35px];
         
        }
        .card-shape {
          @apply rotate-180 mb-[64px] mt-[56px];
          
          h3 {
            @apply rotate-180;
          }
        }
        
        &::after {
          @apply bottom-[calc(100%-56px)] h-[56px];
        }
        .content-date {
          bottom: inherit;
          top: -6px;
        }
      }
      .content-date {
        font-size: 0;
        @apply bg-primary-500 w-[12px] h-[12px] rounded-full absolute left-1/2 -translate-x-1/2 bottom-[-6px];
      }
    }
  }