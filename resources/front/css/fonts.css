/*! Generated by <PERSON>ont Squirrel (https://www.fontsquirrel.com) on July 14, 2025 */
@font-face {
    font-family: 'brando_arabicbold';
    src: url('../fonts/brandoarabic-bold-webfont.woff2') format('woff2'),
         url('../fonts/brandoarabic-bold-webfont.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'brando_arabictext';
    src: url('../fonts/brandoarabic-text-webfont.woff2') format('woff2'),
         url('../fonts/brandoarabic-text-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'brando_sansbold';
    src: url('../fonts/brandosans-bold-webfont.woff2') format('woff2'),
         url('../fonts/brandosans-bold-webfont.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'brando_sanstext';
    src: url('../fonts/brandosans-text-webfont.woff2') format('woff2'),
         url('../fonts/brandosans-text-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Araboto';
    src: url('../fonts/Araboto-Black.woff2') format('woff2'),
         url('../fonts/Araboto-Black.woff') format('woff');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Araboto';
    src: url('../fonts/Araboto-Bold.woff2') format('woff2'),
         url('../fonts/Araboto-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Araboto';
    src: url('../fonts/Araboto-Light.woff2') format('woff2'),
         url('../fonts/Araboto-Light.woff') format('woff');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Araboto';
    src: url('../fonts/Araboto-Medium.woff2') format('woff2'),
         url('../fonts/Araboto-Medium.woff') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Araboto';
    src: url('../fonts/Araboto-Normal.woff2') format('woff2'),
         url('../fonts/Araboto-Normal.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  @font-face {
    font-family: 'Araboto';
    src: url('../fonts/Araboto-Thin.woff2') format('woff2'),
         url('../fonts/Araboto-Thin.woff') format('woff');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
  }

  /* Objektiv Mk2 Heading Font */
  @font-face {
    font-family: 'Objektiv Mk2 Trial XBold';
    src: url('../fonts/ObjektivMk2Trial-XBold.woff2') format('woff2'),
         url('../fonts/ObjektivMk2Trial-XBold.woff') format('woff');
    font-weight: 800;
    font-style: normal;
    font-display: swap;
  }

  /* Font fallback classes using CSS custom properties */
  .font-sans-normal {
    font-family: var(--font-sans-normal);
  }

  .font-sans-bold {
    font-family: var(--font-sans-bold);
  }

  .font-arabic {
    font-family: var(--font-arabic);
  }

  .font-heading {
    font-family: var(--font-heading);
  }
  .font-heading-arabic {
    font-family: var(--font-heading-arabic);
    font-weight: 900;
  }
  @font-face {
    font-family: 'icomoon';
    src:  url('../fonts/icomoon.eot?vi21j2');
    src:  url('../fonts/icomoon.eot?vi21j2#iefix') format('embedded-opentype'),
      url('../fonts/icomoon.ttf?vi21j2') format('truetype'),
      url('../fonts/icomoon.woff?vi21j2') format('woff'),
      url('../fonts/icomoon.svg?vi21j2#icomoon') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
  }

  [class^="icon-"], [class*=" icon-"] {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'icomoon' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    @apply text-gray-900;
  }

  .icon-upload:before {
    content: "\e903";
  }
  .icon-calendar:before {
    content: "\e902";
  }
  .icon-sms:before {
    content: "\e916";
  }
  .icon-call:before {
    content: "\e90c";
  }
  .icon-location:before {
    content: "\e911";
  }
  .icon-link:before {
    content: "\e913";
  }
  .icon-minus-cirlce:before {
    content: "\e90d";
  }
  .icon-add-circle:before {
    content: "\e914";
  }
  .icon-arrow-down:before {
    content: "\e900";
  }
  .icon-arrow-left:before {
    content: "\e901";
  }
  .icon-facebook:before {
    content: "\e90e";
  }
  .icon-import:before {
    content: "\e90f";
  }
  .icon-instagram:before {
    content: "\e910";
  }
  .icon-linkedin:before {
    content: "\e912";
  }
  .icon-search:before {
    content: "\e915";
  }
