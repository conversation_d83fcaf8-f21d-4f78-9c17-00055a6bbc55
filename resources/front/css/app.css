/* Import custom CSS files - must be at the top */

/*! Generated by <PERSON>ont Squirrel (https://www.fontsquirrel.com) on July 14, 2025 */

@font-face {
  font-family: 'brando_arabicbold';

  src: url('../fonts/brandoarabic-bold-webfont.woff2') format('woff2'),
         url('../fonts/brandoarabic-bold-webfont.woff') format('woff');

  font-weight: bold;

  font-style: normal;

  font-display: swap;
}

@font-face {
  font-family: 'brando_arabictext';

  src: url('../fonts/brandoarabic-text-webfont.woff2') format('woff2'),
         url('../fonts/brandoarabic-text-webfont.woff') format('woff');

  font-weight: normal;

  font-style: normal;

  font-display: swap;
}

@font-face {
  font-family: 'brando_sansbold';

  src: url('../fonts/brandosans-bold-webfont.woff2') format('woff2'),
         url('../fonts/brandosans-bold-webfont.woff') format('woff');

  font-weight: bold;

  font-style: normal;

  font-display: swap;
}

@font-face {
  font-family: 'brando_sanstext';

  src: url('../fonts/brandosans-text-webfont.woff2') format('woff2'),
         url('../fonts/brandosans-text-webfont.woff') format('woff');

  font-weight: normal;

  font-style: normal;

  font-display: swap;
}

@font-face {
  font-family: 'Araboto';

  src: url('../fonts/Araboto-Black.woff2') format('woff2'),
         url('../fonts/Araboto-Black.woff') format('woff');

  font-weight: 900;

  font-style: normal;

  font-display: swap;
}

@font-face {
  font-family: 'Araboto';

  src: url('../fonts/Araboto-Bold.woff2') format('woff2'),
         url('../fonts/Araboto-Bold.woff') format('woff');

  font-weight: bold;

  font-style: normal;

  font-display: swap;
}

@font-face {
  font-family: 'Araboto';

  src: url('../fonts/Araboto-Light.woff2') format('woff2'),
         url('../fonts/Araboto-Light.woff') format('woff');

  font-weight: 300;

  font-style: normal;

  font-display: swap;
}

@font-face {
  font-family: 'Araboto';

  src: url('../fonts/Araboto-Medium.woff2') format('woff2'),
         url('../fonts/Araboto-Medium.woff') format('woff');

  font-weight: 500;

  font-style: normal;

  font-display: swap;
}

@font-face {
  font-family: 'Araboto';

  src: url('../fonts/Araboto-Normal.woff2') format('woff2'),
         url('../fonts/Araboto-Normal.woff') format('woff');

  font-weight: normal;

  font-style: normal;

  font-display: swap;
}

@font-face {
  font-family: 'Araboto';

  src: url('../fonts/Araboto-Thin.woff2') format('woff2'),
         url('../fonts/Araboto-Thin.woff') format('woff');

  font-weight: 100;

  font-style: normal;

  font-display: swap;
}

/* Objektiv Mk2 Heading Font */

@font-face {
  font-family: 'Objektiv Mk2 Trial XBold';

  src: url('../fonts/ObjektivMk2Trial-XBold.woff2') format('woff2'),
         url('../fonts/ObjektivMk2Trial-XBold.woff') format('woff');

  font-weight: 800;

  font-style: normal;

  font-display: swap;
}

/* Font fallback classes using CSS custom properties */

.font-sans-normal {
  font-family: var(--font-sans-normal);
}

.font-sans-bold {
  font-family: var(--font-sans-bold);
}

.font-arabic {
  font-family: var(--font-arabic);
}

.font-heading {
  font-family: var(--font-heading);
}

.font-heading-arabic {
  font-family: var(--font-heading-arabic);
  font-weight: 900;
}

@font-face {
  font-family: 'icomoon';

  src:  url('../fonts/icomoon.eot?vi21j2');

  src:  url('../fonts/icomoon.eot?vi21j2#iefix') format('embedded-opentype'),
      url('../fonts/icomoon.ttf?vi21j2') format('truetype'),
      url('../fonts/icomoon.woff?vi21j2') format('woff'),
      url('../fonts/icomoon.svg?vi21j2#icomoon') format('svg');

  font-weight: normal;

  font-style: normal;

  font-display: swap;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  --tw-text-opacity: 1;
  color: rgb(73 71 70 / var(--tw-text-opacity, 1));
}

.icon-upload:before {
  content: "\e903";
}

.icon-calendar:before {
  content: "\e902";
}

.icon-sms:before {
  content: "\e916";
}

.icon-call:before {
  content: "\e90c";
}

.icon-location:before {
  content: "\e911";
}

.icon-link:before {
  content: "\e913";
}

.icon-minus-cirlce:before {
  content: "\e90d";
}

.icon-add-circle:before {
  content: "\e914";
}

.icon-arrow-down:before {
  content: "\e900";
}

.icon-arrow-left:before {
  content: "\e901";
}

.icon-facebook:before {
  content: "\e90e";
}

.icon-import:before {
  content: "\e90f";
}

.icon-instagram:before {
  content: "\e910";
}

.icon-linkedin:before {
  content: "\e912";
}

.icon-search:before {
  content: "\e915";
}

/* Base button */

.btn {
  position: relative;
  display: inline-block;
  cursor: pointer;
  white-space: nowrap;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 600;
  line-height: 1.4;
  --tw-text-opacity: 1;
  color: rgb(255 86 47 / var(--tw-text-opacity, 1));
}

.btn:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

/* Primary button */

.btn-primary {
  text-transform: uppercase;
}

.btn-white {
  text-transform: uppercase;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

/* btn link underline animation */

.btn-line-effect::before,
.btn-line-effect::after {
  pointer-events: none;
  position: absolute;
  left: 0px;
  top: 100%;
  height: 1px;
  width: 100%;
  background-color: currentColor;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 1000ms;
  --tw-content: '';
  content: var(--tw-content);
}

.btn-line-effect::before {
  transform-origin: 0% 50%;
  transform: scale3d(0,1,1);
}

.btn-line-effect:hover::before,.btn-line-effect:focus::before {
  transform: scale3d(1,1,1);
}

.btn-line-effect::after {
  top: calc(100% + 2px);
  transform-origin: 100% 50%;
}

.btn-line-effect:hover::after,.btn-line-effect:focus::after {
  transform: scale3d(0,1,1);
}

/* .btn {
  @apply inline-flex items-center justify-center px-5 py-3 lg:px-6 lg:py-4 border text-sm sm:text-base font-bold uppercase whitespace-nowrap rounded-full transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2;
}
.btn-primary {
  @apply bg-primary-500 text-white border-transparent hover:bg-primary-600 focus:ring-primary-500;
}

.btn-primary-outline {
  @apply bg-white text-primary-500 border-primary-500 hover:bg-primary-200 focus:ring-primary-500;
}
.btn-white {
  @apply bg-white text-primary-500 hover:bg-primary-200 focus:ring-offset-primary-500;
} */

/* Form input styles */

.form-input {
  display: block;
  width: 100%;
  border-radius: 29px;
  border-width: 1px;
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.form-input::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(179 173 166 / var(--tw-placeholder-opacity, 1));
}

.form-input::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(179 173 166 / var(--tw-placeholder-opacity, 1));
}

.form-input:focus {
  --tw-border-opacity: 1;
  border-color: rgb(255 86 47 / var(--tw-border-opacity, 1));
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 86 47 / var(--tw-ring-opacity, 1));
}

@media (min-width: 640px) {
  .form-input {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .form-input {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}

.form-label {
  margin-bottom: 0.25rem;
  display: block;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
  line-height: 1.4;
  --tw-text-opacity: 1;
  color: rgb(4 7 8 / var(--tw-text-opacity, 1));
}

/* Custom Selectbox Styles */

.custom-selectbox {
  position: relative;
}

.selectbox-button {
  display: flex;
  min-width: 4rem;
  cursor: pointer;
  align-items: center;
  justify-content: space-between;
  border-radius: 0.5rem;
  border-width: 0px;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(73 71 70 / var(--tw-text-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

.selectbox-button:focus {
  --tw-border-opacity: 1;
  border-color: rgb(255 86 47 / var(--tw-border-opacity, 1));
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 86 47 / var(--tw-ring-opacity, 1));
}

.selectbox-text {
  font-weight: 500;
}

.selectbox-arrow {
  height: 1rem;
  width: 1rem;
  --tw-text-opacity: 1;
  color: rgb(4 7 8 / var(--tw-text-opacity, 1));
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

.selectbox-button[aria-expanded="true"] .selectbox-arrow {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.selectbox-dropdown {
  visibility: hidden;
  position: absolute;
  right: 0px;
  z-index: 50;
  margin-top: 0.25rem;
  transform-origin: top right;
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  border-radius: 0.5rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  opacity: 0;
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

.selectbox-dropdown.active {
  visibility: visible;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 1;
}

.selectbox-options {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.selectbox-option {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  text-align: left;
  --tw-text-opacity: 1;
  color: rgb(4 7 8 / var(--tw-text-opacity, 1));
}

.selectbox-option:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(217 216 214 / var(--tw-bg-opacity, 1));
}

.selectbox-option:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(217 216 214 / var(--tw-bg-opacity, 1));
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.selectbox-option.active {
  --tw-bg-opacity: 1;
  background-color: rgb(255 249 247 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(254 52 17 / var(--tw-text-opacity, 1));
}

.selectbox-option .option-text {
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.3;
}

.selectbox-option .option-label {
  font-size: 0.75rem;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgb(73 71 70 / var(--tw-text-opacity, 1));
}

.selectbox-option.active .option-label {
  --tw-text-opacity: 1;
  color: rgb(255 86 47 / var(--tw-text-opacity, 1));
}

/* Animation keyframes (outside @layer) */

@keyframes slideInMobile {
  from {
    transform: translateX(-50px);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes logoSlideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes closeButtonSlideIn {
  from {
    transform: scale(0.8) rotate(-90deg);
    opacity: 0;
  }

  to {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

/* Dark mode */

@media (prefers-color-scheme: dark) {
  @media (max-width: 1023px) {
    .main-nav {
      --tw-bg-opacity: 1;
      background-color: rgb(4 7 8 / var(--tw-bg-opacity, 1));
    }

    body.mobile-menu-open .nav-link {
      --tw-text-opacity: 1;
      color: rgb(255 255 255 / var(--tw-text-opacity, 1));
    }

    body.mobile-menu-open .nav-link:hover {
      background-color: rgb(255 86 47 / 0.1);
      --tw-text-opacity: 1;
      color: rgb(255 86 47 / var(--tw-text-opacity, 1));
    }

    .mobile-menu-close-btn {
      border-color: rgb(73 71 70 / 0.5);
      background-color: rgb(4 7 8 / 0.8);
      --tw-text-opacity: 1;
      color: rgb(73 71 70 / var(--tw-text-opacity, 1));
    }

    .mobile-menu-close-btn:hover {
      border-color: rgb(73 71 70 / 0.5);
      --tw-text-opacity: 1;
      color: rgb(73 71 70 / var(--tw-text-opacity, 1));
    }
  }
}

/**
 * Swiper 11.2.10
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2025 Vladimir Kharlampidi
 *
 * Released under the MIT License
 *
 * Released on: June 28, 2025
 */

@font-face{
  font-family:swiper-icons;

  src:url('data:application/font-woff;charset=utf-8;base64, 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');

  font-weight:400;

  font-style:normal
}

:root{
  --swiper-theme-color:#007aff
}

:host{
  position:relative;
  display:block;
  margin-left:auto;
  margin-right:auto;
  z-index:1
}

.swiper{
  margin-left:auto;
  margin-right:auto;
  position:relative;
  overflow:hidden;
  list-style:none;
  padding:0;
  z-index:1;
  display:block
}

.swiper-vertical>.swiper-wrapper{
  flex-direction:column
}

.swiper-wrapper{
  position:relative;
  width:100%;
  height:100%;
  z-index:1;
  display:flex;
  transition-property:transform;
  transition-timing-function:var(--swiper-wrapper-transition-timing-function,initial);
  box-sizing:content-box
}

.swiper-android .swiper-slide,.swiper-ios .swiper-slide,.swiper-wrapper{
  transform:translate3d(0px,0,0)
}

.swiper-horizontal{
  touch-action:pan-y
}

.swiper-vertical{
  touch-action:pan-x
}

.swiper-slide{
  flex-shrink:0;
  width:100%;
  height:100%;
  position:relative;
  transition-property:transform;
  display:block
}

.swiper-slide-invisible-blank{
  visibility:hidden
}

.swiper-autoheight,.swiper-autoheight .swiper-slide{
  height:auto
}

.swiper-autoheight .swiper-wrapper{
  align-items:flex-start;
  transition-property:transform,height
}

.swiper-backface-hidden .swiper-slide{
  transform:translateZ(0);
  backface-visibility:hidden
}

.swiper-3d.swiper-css-mode .swiper-wrapper{
  perspective:1200px
}

.swiper-3d .swiper-wrapper{
  transform-style:preserve-3d
}

.swiper-3d{
  perspective:1200px
}

.swiper-3d .swiper-cube-shadow,.swiper-3d .swiper-slide{
  transform-style:preserve-3d
}

.swiper-css-mode>.swiper-wrapper{
  overflow:auto;
  scrollbar-width:none;
  -ms-overflow-style:none
}

.swiper-css-mode>.swiper-wrapper::-webkit-scrollbar{
  display:none
}

.swiper-css-mode>.swiper-wrapper>.swiper-slide{
  scroll-snap-align:start start
}

.swiper-css-mode.swiper-horizontal>.swiper-wrapper{
  scroll-snap-type:x mandatory
}

.swiper-css-mode.swiper-vertical>.swiper-wrapper{
  scroll-snap-type:y mandatory
}

.swiper-css-mode.swiper-free-mode>.swiper-wrapper{
  scroll-snap-type:none
}

.swiper-css-mode.swiper-free-mode>.swiper-wrapper>.swiper-slide{
  scroll-snap-align:none
}

.swiper-css-mode.swiper-centered>.swiper-wrapper::before{
  content:'';
  flex-shrink:0;
  order:9999
}

.swiper-css-mode.swiper-centered>.swiper-wrapper>.swiper-slide{
  scroll-snap-align:center center;
  scroll-snap-stop:always
}

.swiper-css-mode.swiper-centered.swiper-horizontal>.swiper-wrapper>.swiper-slide:first-child{
  margin-inline-start:var(--swiper-centered-offset-before)
}

.swiper-css-mode.swiper-centered.swiper-horizontal>.swiper-wrapper::before{
  height:100%;
  min-height:1px;
  width:var(--swiper-centered-offset-after)
}

.swiper-css-mode.swiper-centered.swiper-vertical>.swiper-wrapper>.swiper-slide:first-child{
  margin-block-start:var(--swiper-centered-offset-before)
}

.swiper-css-mode.swiper-centered.swiper-vertical>.swiper-wrapper::before{
  width:100%;
  min-width:1px;
  height:var(--swiper-centered-offset-after)
}

.swiper-3d .swiper-slide-shadow,.swiper-3d .swiper-slide-shadow-bottom,.swiper-3d .swiper-slide-shadow-left,.swiper-3d .swiper-slide-shadow-right,.swiper-3d .swiper-slide-shadow-top{
  position:absolute;
  left:0;
  top:0;
  width:100%;
  height:100%;
  pointer-events:none;
  z-index:10
}

.swiper-3d .swiper-slide-shadow{
  background:rgba(0,0,0,.15)
}

.swiper-3d .swiper-slide-shadow-left{
  background-image:linear-gradient(to left,rgba(0,0,0,.5),rgba(0,0,0,0))
}

.swiper-3d .swiper-slide-shadow-right{
  background-image:linear-gradient(to right,rgba(0,0,0,.5),rgba(0,0,0,0))
}

.swiper-3d .swiper-slide-shadow-top{
  background-image:linear-gradient(to top,rgba(0,0,0,.5),rgba(0,0,0,0))
}

.swiper-3d .swiper-slide-shadow-bottom{
  background-image:linear-gradient(to bottom,rgba(0,0,0,.5),rgba(0,0,0,0))
}

.swiper-lazy-preloader{
  width:42px;
  height:42px;
  position:absolute;
  left:50%;
  top:50%;
  margin-left:-21px;
  margin-top:-21px;
  z-index:10;
  transform-origin:50%;
  box-sizing:border-box;
  border:4px solid var(--swiper-preloader-color,var(--swiper-theme-color));
  border-radius:50%;
  border-top-color:transparent
}

.swiper-watch-progress .swiper-slide-visible .swiper-lazy-preloader,.swiper:not(.swiper-watch-progress) .swiper-lazy-preloader{
  animation:swiper-preloader-spin 1s infinite linear
}

.swiper-lazy-preloader-white{
  --swiper-preloader-color:#fff
}

.swiper-lazy-preloader-black{
  --swiper-preloader-color:#000
}

@keyframes swiper-preloader-spin{
  0%{
    transform:rotate(0deg)
  }

  100%{
    transform:rotate(360deg)
  }
}

.swiper-virtual .swiper-slide{
  -webkit-backface-visibility:hidden;
  transform:translateZ(0)
}

.swiper-virtual.swiper-css-mode .swiper-wrapper::after{
  content:'';
  position:absolute;
  left:0;
  top:0;
  pointer-events:none
}

.swiper-virtual.swiper-css-mode.swiper-horizontal .swiper-wrapper::after{
  height:1px;
  width:var(--swiper-virtual-size)
}

.swiper-virtual.swiper-css-mode.swiper-vertical .swiper-wrapper::after{
  width:1px;
  height:var(--swiper-virtual-size)
}

:root{
  --swiper-navigation-size:44px
}

.swiper-button-next,.swiper-button-prev{
  position:absolute;
  top:var(--swiper-navigation-top-offset,50%);
  width:calc(var(--swiper-navigation-size)/ 44 * 27);
  height:var(--swiper-navigation-size);
  margin-top:calc(0px - (var(--swiper-navigation-size)/ 2));
  z-index:10;
  cursor:pointer;
  display:flex;
  align-items:center;
  justify-content:center;
  color:var(--swiper-navigation-color,var(--swiper-theme-color))
}

.swiper-button-next.swiper-button-disabled,.swiper-button-prev.swiper-button-disabled{
  opacity:.35;
  cursor:auto;
  pointer-events:none
}

.swiper-button-next.swiper-button-hidden,.swiper-button-prev.swiper-button-hidden{
  opacity:0;
  cursor:auto;
  pointer-events:none
}

.swiper-navigation-disabled .swiper-button-next,.swiper-navigation-disabled .swiper-button-prev{
  display:none!important
}

.swiper-button-next svg,.swiper-button-prev svg{
  width:100%;
  height:100%;
  -o-object-fit:contain;
     object-fit:contain;
  transform-origin:center
}

.swiper-rtl .swiper-button-next svg,.swiper-rtl .swiper-button-prev svg{
  transform:rotate(180deg)
}

.swiper-button-prev,.swiper-rtl .swiper-button-next{
  left:var(--swiper-navigation-sides-offset,10px);
  right:auto
}

.swiper-button-next,.swiper-rtl .swiper-button-prev{
  right:var(--swiper-navigation-sides-offset,10px);
  left:auto
}

.swiper-button-lock{
  display:none
}

.swiper-button-next:after,.swiper-button-prev:after{
  font-family:swiper-icons;
  font-size:var(--swiper-navigation-size);
  text-transform:none!important;
  letter-spacing:0;
  font-variant:initial;
  line-height:1
}

.swiper-button-prev:after,.swiper-rtl .swiper-button-next:after{
  content:'prev'
}

.swiper-button-next,.swiper-rtl .swiper-button-prev{
  right:var(--swiper-navigation-sides-offset,10px);
  left:auto
}

.swiper-button-next:after,.swiper-rtl .swiper-button-prev:after{
  content:'next'
}

.swiper-pagination{
  position:absolute;
  text-align:center;
  transition:.3s opacity;
  transform:translate3d(0,0,0);
  z-index:10
}

.swiper-pagination.swiper-pagination-hidden{
  opacity:0
}

.swiper-pagination-disabled>.swiper-pagination,.swiper-pagination.swiper-pagination-disabled{
  display:none!important
}

.swiper-horizontal>.swiper-pagination-bullets,.swiper-pagination-bullets.swiper-pagination-horizontal,.swiper-pagination-custom,.swiper-pagination-fraction{
  bottom:var(--swiper-pagination-bottom,8px);
  top:var(--swiper-pagination-top,auto);
  left:0;
  width:100%
}

.swiper-pagination-bullets-dynamic{
  overflow:hidden;
  font-size:0
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{
  transform:scale(.33);
  position:relative
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active{
  transform:scale(1)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main{
  transform:scale(1)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev{
  transform:scale(.66)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev{
  transform:scale(.33)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next{
  transform:scale(.66)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next{
  transform:scale(.33)
}

.swiper-pagination-bullet{
  width:var(--swiper-pagination-bullet-width,var(--swiper-pagination-bullet-size,8px));
  height:var(--swiper-pagination-bullet-height,var(--swiper-pagination-bullet-size,8px));
  display:inline-block;
  border-radius:var(--swiper-pagination-bullet-border-radius,50%);
  background:var(--swiper-pagination-bullet-inactive-color,#000);
  opacity:var(--swiper-pagination-bullet-inactive-opacity, .2)
}

button.swiper-pagination-bullet{
  border:none;
  margin:0;
  padding:0;
  box-shadow:none;
  -webkit-appearance:none;
  -moz-appearance:none;
       appearance:none
}

.swiper-pagination-clickable .swiper-pagination-bullet{
  cursor:pointer
}

.swiper-pagination-bullet:only-child{
  display:none!important
}

.swiper-pagination-bullet-active{
  opacity:var(--swiper-pagination-bullet-opacity, 1);
  background:var(--swiper-pagination-color,var(--swiper-theme-color))
}

.swiper-pagination-vertical.swiper-pagination-bullets,.swiper-vertical>.swiper-pagination-bullets{
  right:var(--swiper-pagination-right,8px);
  left:var(--swiper-pagination-left,auto);
  top:50%;
  transform:translate3d(0px,-50%,0)
}

.swiper-pagination-vertical.swiper-pagination-bullets .swiper-pagination-bullet,.swiper-vertical>.swiper-pagination-bullets .swiper-pagination-bullet{
  margin:var(--swiper-pagination-bullet-vertical-gap,6px) 0;
  display:block
}

.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic,.swiper-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic{
  top:50%;
  transform:translateY(-50%);
  width:8px
}

.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,.swiper-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{
  display:inline-block;
  transition:.2s transform,.2s top
}

.swiper-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet,.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet{
  margin:0 var(--swiper-pagination-bullet-horizontal-gap,4px)
}

.swiper-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic,.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic{
  left:50%;
  transform:translateX(-50%);
  white-space:nowrap
}

.swiper-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{
  transition:.2s transform,.2s left
}

.swiper-horizontal.swiper-rtl>.swiper-pagination-bullets-dynamic .swiper-pagination-bullet{
  transition:.2s transform,.2s right
}

.swiper-pagination-fraction{
  color:var(--swiper-pagination-fraction-color,inherit)
}

.swiper-pagination-progressbar{
  background:var(--swiper-pagination-progressbar-bg-color,rgba(0,0,0,.25));
  position:absolute
}

.swiper-pagination-progressbar .swiper-pagination-progressbar-fill{
  background:var(--swiper-pagination-color,var(--swiper-theme-color));
  position:absolute;
  left:0;
  top:0;
  width:100%;
  height:100%;
  transform:scale(0);
  transform-origin:left top
}

.swiper-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill{
  transform-origin:right top
}

.swiper-horizontal>.swiper-pagination-progressbar,.swiper-pagination-progressbar.swiper-pagination-horizontal,.swiper-pagination-progressbar.swiper-pagination-vertical.swiper-pagination-progressbar-opposite,.swiper-vertical>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite{
  width:100%;
  height:var(--swiper-pagination-progressbar-size,4px);
  left:0;
  top:0
}

.swiper-horizontal>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,.swiper-pagination-progressbar.swiper-pagination-horizontal.swiper-pagination-progressbar-opposite,.swiper-pagination-progressbar.swiper-pagination-vertical,.swiper-vertical>.swiper-pagination-progressbar{
  width:var(--swiper-pagination-progressbar-size,4px);
  height:100%;
  left:0;
  top:0
}

.swiper-pagination-lock{
  display:none
}

.swiper-scrollbar{
  border-radius:var(--swiper-scrollbar-border-radius,10px);
  position:relative;
  touch-action:none;
  background:var(--swiper-scrollbar-bg-color,rgba(0,0,0,.1))
}

.swiper-scrollbar-disabled>.swiper-scrollbar,.swiper-scrollbar.swiper-scrollbar-disabled{
  display:none!important
}

.swiper-horizontal>.swiper-scrollbar,.swiper-scrollbar.swiper-scrollbar-horizontal{
  position:absolute;
  left:var(--swiper-scrollbar-sides-offset,1%);
  bottom:var(--swiper-scrollbar-bottom,4px);
  top:var(--swiper-scrollbar-top,auto);
  z-index:50;
  height:var(--swiper-scrollbar-size,4px);
  width:calc(100% - 2 * var(--swiper-scrollbar-sides-offset,1%))
}

.swiper-scrollbar.swiper-scrollbar-vertical,.swiper-vertical>.swiper-scrollbar{
  position:absolute;
  left:var(--swiper-scrollbar-left,auto);
  right:var(--swiper-scrollbar-right,4px);
  top:var(--swiper-scrollbar-sides-offset,1%);
  z-index:50;
  width:var(--swiper-scrollbar-size,4px);
  height:calc(100% - 2 * var(--swiper-scrollbar-sides-offset,1%))
}

.swiper-scrollbar-drag{
  height:100%;
  width:100%;
  position:relative;
  background:var(--swiper-scrollbar-drag-bg-color,rgba(0,0,0,.5));
  border-radius:var(--swiper-scrollbar-border-radius,10px);
  left:0;
  top:0
}

.swiper-scrollbar-cursor-drag{
  cursor:move
}

.swiper-scrollbar-lock{
  display:none
}

.swiper-zoom-container{
  width:100%;
  height:100%;
  display:flex;
  justify-content:center;
  align-items:center;
  text-align:center
}

.swiper-zoom-container>canvas,.swiper-zoom-container>img,.swiper-zoom-container>svg{
  max-width:100%;
  max-height:100%;
  -o-object-fit:contain;
     object-fit:contain
}

.swiper-slide-zoomed{
  cursor:move;
  touch-action:none
}

.swiper .swiper-notification{
  position:absolute;
  left:0;
  top:0;
  pointer-events:none;
  opacity:0;
  z-index:-1000
}

.swiper-free-mode>.swiper-wrapper{
  transition-timing-function:ease-out;
  margin:0 auto
}

.swiper-grid>.swiper-wrapper{
  flex-wrap:wrap
}

.swiper-grid-column>.swiper-wrapper{
  flex-wrap:wrap;
  flex-direction:column
}

.swiper-fade.swiper-free-mode .swiper-slide{
  transition-timing-function:ease-out
}

.swiper-fade .swiper-slide{
  pointer-events:none;
  transition-property:opacity
}

.swiper-fade .swiper-slide .swiper-slide{
  pointer-events:none
}

.swiper-fade .swiper-slide-active{
  pointer-events:auto
}

.swiper-fade .swiper-slide-active .swiper-slide-active{
  pointer-events:auto
}

.swiper.swiper-cube{
  overflow:visible
}

.swiper-cube .swiper-slide{
  pointer-events:none;
  backface-visibility:hidden;
  z-index:1;
  visibility:hidden;
  transform-origin:0 0;
  width:100%;
  height:100%
}

.swiper-cube .swiper-slide .swiper-slide{
  pointer-events:none
}

.swiper-cube.swiper-rtl .swiper-slide{
  transform-origin:100% 0
}

.swiper-cube .swiper-slide-active,.swiper-cube .swiper-slide-active .swiper-slide-active{
  pointer-events:auto
}

.swiper-cube .swiper-slide-active,.swiper-cube .swiper-slide-next,.swiper-cube .swiper-slide-prev{
  pointer-events:auto;
  visibility:visible
}

.swiper-cube .swiper-cube-shadow{
  position:absolute;
  left:0;
  bottom:0px;
  width:100%;
  height:100%;
  opacity:.6;
  z-index:0
}

.swiper-cube .swiper-cube-shadow:before{
  content:'';
  background:#000;
  position:absolute;
  left:0;
  top:0;
  bottom:0;
  right:0;
  filter:blur(50px)
}

.swiper-cube .swiper-slide-next+.swiper-slide{
  pointer-events:auto;
  visibility:visible
}

.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-bottom,.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-left,.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-right,.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-top{
  z-index:0;
  backface-visibility:hidden
}

.swiper.swiper-flip{
  overflow:visible
}

.swiper-flip .swiper-slide{
  pointer-events:none;
  backface-visibility:hidden;
  z-index:1
}

.swiper-flip .swiper-slide .swiper-slide{
  pointer-events:none
}

.swiper-flip .swiper-slide-active,.swiper-flip .swiper-slide-active .swiper-slide-active{
  pointer-events:auto
}

.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-bottom,.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-left,.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-right,.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-top{
  z-index:0;
  backface-visibility:hidden
}

.swiper-creative .swiper-slide{
  backface-visibility:hidden;
  overflow:hidden;
  transition-property:transform,opacity,height
}

.swiper.swiper-cards{
  overflow:visible
}

.swiper-cards .swiper-slide{
  transform-origin:center bottom;
  backface-visibility:hidden;
  overflow:hidden
}

/* Swiper Custom Styles for Featured Authors */

.swiper {
  width: 100%;
  padding-top: 0;
  padding-bottom: 0;
}

.swiper-slide {
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2px;
}

.swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

/* Author card styling */

.author-card {
  width: 100%;
  max-width: 235px;
  margin: 0 auto;
}

.author-image {
  margin-bottom: 1rem;
  overflow: hidden;
}

.author-image img {
  width: 100%;
  aspect-ratio: 1 / 1.5;
  -o-object-fit: cover;
     object-fit: cover;
  transition: transform 0.3s ease;
}

.author-card:hover .author-image img {
  transform: scale(1.05);
}

.custom-tab {
  &.active,
    &:focus {
    border-bottom-width: 2px;
  }
  &.active,
    &:focus {
    --tw-border-opacity: 1;
    border-color: rgb(255 86 47 / var(--tw-border-opacity, 1));
  }
  &.active,
    &:focus {
    --tw-text-opacity: 1;
    color: rgb(255 86 47 / var(--tw-text-opacity, 1));
  }
  &.active {
    &:hover {
      --tw-border-opacity: 1;
      border-color: rgb(255 86 47 / var(--tw-border-opacity, 1));
    }
    &:hover {
      --tw-text-opacity: 1;
      color: rgb(255 86 47 / var(--tw-text-opacity, 1));
    }
  }
}

.custom-collapse {
  &:focus {
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
  &:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
  }
  &:focus {
    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  }
  &:focus {
    span:first-child,
        .faq-icon {
      --tw-text-opacity: 1;
      color: rgb(255 86 47 / var(--tw-text-opacity, 1));
    }
  }
}

/* Timeline Section that matches the exact design */

.main-timeline-section {
  position: relative;
  .card-shape {
    display: flex;
  }
  .card-shape {
    min-height: 80px;
  }
  .card-shape {
    align-items: center;
  }
  .card-shape {
    justify-content: center;
  }
  /* Horizontal center line */
  &::before {
    content: '';
  }
  &::before {
    position: absolute;
  }
  &::before {
    left: 0px;
  }
  &::before {
    top: 50%;
  }
  &::before {
    z-index: 1;
  }
  &::before {
    margin-top: -1px;
  }
  &::before {
    height: 2px;
  }
  &::before {
    width: 100%;
  }
  &::before {
    --tw-bg-opacity: 1;
    background-color: rgb(255 86 47 / var(--tw-bg-opacity, 1));
  }
  .timeline-item {
    position: relative;
    &::before {
      content: attr(data-step);
    }
    &::before {
      position: absolute;
    }
    &::before {
      top: calc(100% + 14px);
    }
    &::before {
      left: 50%;
    }
    &::before {
      z-index: 10;
    }
    &::before {
      --tw-translate-x: -50%;
      transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    }
    &::before {
      font-size: 24px;
    }
    &::before {
      font-weight: 700;
    }
    &::before {
      --tw-text-opacity: 1;
      color: rgb(255 86 47 / var(--tw-text-opacity, 1));
    }
    &::after {
      content: '';
    }
    &::after {
      position: absolute;
    }
    &::after {
      left: 50%;
    }
    &::after {
      z-index: 5;
    }
    &::after {
      width: 2px;
    }
    &::after {
      --tw-translate-x: -50%;
      transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    }
    &::after {
      --tw-bg-opacity: 1;
      background-color: rgb(255 86 47 / var(--tw-bg-opacity, 1));
    }
    &:nth-child(odd) {
      .card-shape {
        margin-bottom: 56px;
      }
      &::before {
        margin-top: -5px;
      }
      &::after {
        top: calc(100% - 56px);
      }
      &::after {
        height: 56px;
      }
    }
    &:nth-child(even) {
      align-self: flex-end;
    }
    &:nth-child(even) {
      &::before {
        top: 0px;
      }
      &::before {
        margin-top: -35px;
      }
      .card-shape {
        margin-bottom: 64px;
      }
      .card-shape {
        margin-top: 56px;
      }
      .card-shape {
        --tw-rotate: 180deg;
        transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
      }
      .card-shape {
        h3 {
          --tw-rotate: 180deg;
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
        }
      }
      &::after {
        bottom: calc(100% - 56px);
      }
      &::after {
        height: 56px;
      }
      .content-date {
        bottom: inherit;
        top: -6px;
      }
    }
    .content-date {
      font-size: 0;
    }
    .content-date {
      position: absolute;
    }
    .content-date {
      left: 50%;
    }
    .content-date {
      bottom: -6px;
    }
    .content-date {
      height: 12px;
    }
    .content-date {
      width: 12px;
    }
    .content-date {
      --tw-translate-x: -50%;
      transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    }
    .content-date {
      border-radius: 9999px;
    }
    .content-date {
      --tw-bg-opacity: 1;
      background-color: rgb(255 86 47 / var(--tw-bg-opacity, 1));
    }
  }
}

/* FullCalendar v6.1.18 - Essential Styles with Tailwind @apply */

.fc table {
  width: 100%;
  border-collapse: collapse;
  font-size: 1rem;
  line-height: 1.5rem;
  border-spacing: 0;
}

.fc th {
  text-align: center;
}

.fc th,
.fc td {
  padding: 0px;
  vertical-align: top;
}

.fc-scrollgrid {
  width: 100%;
  overflow-x: auto;
  overflow-y: auto;
  border-radius: 0px;
  border-width: 0px;
}

.fc-scrollgrid table {
  width: 100%;
}

.fc .fc-col-header-cell {
  border-right-width: 1px;
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(217 216 214 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 1rem;
  text-align: center;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 600;
  text-transform: uppercase;
  --tw-text-opacity: 1;
  color: rgb(73 71 70 / var(--tw-text-opacity, 1));
  line-height: 19.5px;
  letter-spacing: 0.025em;
}

.fc-daygrid {
  position: relative;
}

.fc-daygrid-body {
  position: relative;
  width: 100%;
}

.fc-daygrid-day {
  box-sizing: border-box;
  border-right-width: 1px;
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(217 216 214 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 0.125rem;
  vertical-align: top;
  width: 14.285714%;
  /* 100% / 7 days */
}

.fc-daygrid-day:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 249 247 / var(--tw-bg-opacity, 1));
}

.fc-daygrid-day-frame {
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 120px;
  height: auto;
}

.fc-daygrid-day-top {
  position: relative;
}

.fc-daygrid-day-number {
  position: relative;
  z-index: 10;
  cursor: pointer;
  padding: 0.5rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(73 71 70 / var(--tw-text-opacity, 1));
  text-decoration-line: none;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

.fc-daygrid-day-number:hover {
  --tw-text-opacity: 1;
  color: rgb(255 86 47 / var(--tw-text-opacity, 1));
  text-decoration-line: none;
}

.fc-daygrid-day-number:focus {
  outline-width: 2px;
  outline-offset: 2px;
  outline-color: #FF562F;
}

.fc-daygrid-day-events {
  margin-top: 1px;
}

.fc-daygrid-event-harness {
  position: absolute;
  left: 0px;
  right: 0px;
  margin-bottom: 1px;
}

.fc-daygrid-event {
  position: relative;
  z-index: 10;
  margin-left: 0.125rem;
  margin-right: 0.125rem;
  margin-top: 1px;
  margin-bottom: 1px;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  --tw-text-opacity: 1;
  color: rgb(73 71 70 / var(--tw-text-opacity, 1));
  padding: 1px 4px;
}

.fc-event {
  position: relative;
  margin-left: 0.125rem;
  margin-right: 0.125rem;
  margin-top: 1px;
  margin-bottom: 1px;
  display: block;
  cursor: pointer;
  border-radius: 1rem;
  border-width: 0px;
  --tw-border-opacity: 1;
  border-color: rgb(255 86 47 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 86 47 / var(--tw-bg-opacity, 1));
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  padding: 4px 12px;
  line-height: 19px;
}

.fc-event:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 86 47 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 86 47 / var(--tw-bg-opacity, 1));
}

.fc-event-title {
  padding-left: 0.125rem;
  padding-right: 0.125rem;
}

.fc-day-today {
  background-color: var(--fc-today-bg-color, rgba(255, 220, 40, 0.15));
}

.fc-day-past {
  --tw-text-opacity: 1;
  color: rgb(217 216 214 / var(--tw-text-opacity, 1));
}

.fc-day-future {
  --tw-text-opacity: 1;
  color: rgb(217 216 214 / var(--tw-text-opacity, 1));
}

.fc-toolbar {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.fc-toolbar-chunk {
  display: flex;
  align-items: center;
}

.fc-button-group {
  position: relative;
  display: inline-flex;
  vertical-align: middle;
}

.fc-button {
  margin: 0px;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  overflow: visible;
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(217 216 214 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(217 216 214 / var(--tw-bg-opacity, 1));
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(73 71 70 / var(--tw-text-opacity, 1));
  text-decoration-line: none;
  line-height: 1.5;
  text-transform: none;
  -webkit-appearance: button;
}

.fc-button:hover {
  --tw-border-opacity: 1;
  border-color: rgb(217 216 214 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(217 216 214 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(73 71 70 / var(--tw-text-opacity, 1));
  background-color: var(--fc-button-hover-bg-color, #e9ecef);
  border-color: var(--fc-button-hover-border-color, #adb5bd);
}

.fc-button:focus {
  outline-width: 0px;
  box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
}

.fc-button-primary {
  --tw-border-opacity: 1;
  border-color: rgb(255 86 47 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 86 47 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.fc-button-primary:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 86 47 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 86 47 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.fc-toolbar-title {
  margin: 0px;
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.fc-direction-ltr .fc-toolbar > * > :not(:first-child) {
  margin-inline-start: 0.75rem;
}

.fc-scroller {
  position: relative;
  overflow: hidden;
}

.fc-theme-standard,
.fc-theme-standard .fc-scrollgrid {
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(217 216 214 / var(--tw-border-opacity, 1));
}

.fc-theme-standard td,
.fc-theme-standard th {
  --tw-border-opacity: 1;
  border-color: rgb(217 216 214 / var(--tw-border-opacity, 1));
}

/* Calendar container styling */

#calendar {
  border-top-width: 1px;
  border-left-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(217 216 214 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  border-right: none;
  border-bottom: none;
  border-radius: 0;
}

/* Limit calendar to 5 rows */

.fc-daygrid-body .fc-scrollgrid-sync-table tr:nth-child(n+6) {
  display: none;
}

#page-loader.fc-daygrid-body .fc-scrollgrid-sync-table tr:nth-child(n+6) {
  opacity: 0;
  visibility: hidden;
}

/* Remove inner borders to prevent double borders */

.fc-theme-standard .fc-scrollgrid {
  border-width: 0px;
}

.fc-theme-standard .fc-scrollgrid-sync-table {
  border-width: 0px;
}

.fc-scrollgrid-sync-table {
  border-collapse: collapse;
  border-width: 0px;
  border-spacing: 0;
}

.fc-scrollgrid-sync-table tbody {
  border-width: 0px;
}

.fc-scrollgrid-sync-table td,
.fc-scrollgrid-sync-table th {
  border-width: 0px;
  border-right-width: 1px;
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(217 216 214 / var(--tw-border-opacity, 1));
}

.fc-scrollgrid-sync-table td:last-child,
.fc-scrollgrid-sync-table th:last-child {
  border-right-width: 0px;
}

.fc-more-link {
  margin: 0px;
  cursor: pointer;
  border-width: 0px;
  background-color: transparent;
  padding: 0px;
  font-size: 0.75rem;
  font-weight: 600;
  --tw-text-opacity: 1;
  color: rgb(255 86 47 / var(--tw-text-opacity, 1));
  text-decoration-line: underline;
  margin: 1px 3px;
}

.fc-more-link:hover {
  --tw-text-opacity: 1;
  color: rgb(255 86 47 / var(--tw-text-opacity, 1));
}

.fc-popover {
  position: absolute;
  z-index: 50;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(217 216 214 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.fc-popover-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(217 216 214 / var(--tw-border-opacity, 1));
  padding: 0.25rem;
}

.fc-popover-title {
  margin-left: 0.125rem;
  margin-right: 0.125rem;
  margin-top: 0px;
  margin-bottom: 0px;
}

.fc-popover-close {
  cursor: pointer;
  font-size: 1.125rem;
  line-height: 1.75rem;
  opacity: 0.65;
}

.fc-h-event .fc-event-main {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.fc-daygrid-block-event .fc-event-time {
  font-weight: 700;
}

.fc-daygrid-block-event .fc-event-title {
  margin-top: 1px;
}

.fc-liquid-hack .fc-daygrid-day-frame {
  position: static;
}

.fc-liquid-hack .fc-daygrid-event-harness {
  position: static;
}

/* Week and Day views */

.fc-timegrid {
  position: relative;
  z-index: 10;
}

.fc-timegrid-divider {
  padding-bottom: 0.125rem;
  padding-left: 0;
  padding-right: 0;
  padding-top: 0;
}

.fc-timegrid-body {
  position: relative;
  z-index: 10;
}

.fc-timegrid-axis-chunk {
  position: relative;
}

.fc-timegrid-axis-chunk > table {
  position: relative;
  z-index: 10;
}

.fc-timegrid-slots {
  position: relative;
  z-index: 10;
}

.fc-timegrid-slot {
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(217 216 214 / var(--tw-border-opacity, 1));
  height: 1.5em;
}

.fc-timegrid-slot-minor {
  border-top-width: 1px;
  border-style: dotted;
}

.fc-timegrid-slot-label-cushion {
  display: inline-block;
  white-space: nowrap;
}

.fc-timegrid-slot-label {
  vertical-align: middle;
}

.fc-timegrid-axis {
  vertical-align: middle;
}

.fc-timegrid-col-events {
  position: relative;
  z-index: 10;
}

.fc-timegrid-col-bg {
  z-index: 20;
}

.fc-timegrid-event-harness {
  position: absolute;
}

.fc-timegrid-event-harness > .fc-timegrid-event {
  position: absolute;
  left: 0px;
  right: 0px;
  z-index: 10;
}

.fc-timegrid-event {
  border-radius: 0.25rem;
  padding-left: 1px;
  padding-right: 1px;
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
  font-size: 0.75rem;
  --tw-text-opacity: 1;
  color: rgb(73 71 70 / var(--tw-text-opacity, 1));
}

.fc-timegrid-event .fc-event-main {
  padding-left: 1px;
  padding-right: 1px;
  padding-top: 1px;
  padding-bottom: 1px;
}

.fc-timegrid-event .fc-event-time {
  font-size: 0.75rem;
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(73 71 70 / var(--tw-text-opacity, 1));
}

/* Hide default FullCalendar toolbar */

.fc-toolbar {
  display: none;
}

#page-loader.fc-toolbar {
  opacity: 0;
  visibility: hidden;
}

/* Custom styles for calendar components */

.fc-day-today {
  --tw-bg-opacity: 1;
  background-color: rgb(255 249 247 / var(--tw-bg-opacity, 1));
}

/* Custom Toggle Button Styles */

.toggle-btn {
  display: flex;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  border-width: 0px;
  background-color: transparent;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 600;
  text-transform: uppercase;
  --tw-text-opacity: 1;
  color: rgb(73 71 70 / var(--tw-text-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

.toggle-btn:hover {
  --tw-text-opacity: 1;
  color: rgb(255 86 47 / var(--tw-text-opacity, 1));
}

.toggle-btn.active {
  --tw-bg-opacity: 1;
  background-color: rgb(255 86 47 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

#prev-btn, #next-btn {
  display: flex;
  height: 3.5rem;
  width: 3.5rem;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(217 216 214 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 0px;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

#prev-btn:hover, #next-btn:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 86 47 / var(--tw-border-opacity, 1));
}

.today-btn {
  display: flex;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  border-width: 1px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 249 247 / var(--tw-bg-opacity, 1));
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 600;
  text-transform: uppercase;
  --tw-text-opacity: 1;
  color: rgb(255 86 47 / var(--tw-text-opacity, 1));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
  border-color: #FFF2ED;
  background-color: #FFF2ED;
}

.today-btn:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 86 47 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 249 247 / var(--tw-bg-opacity, 1));
}

/* Custom view selector styles */

#view-selector.selectbox-button {
  --tw-border-opacity: 1;
  border-color: rgb(217 216 214 / var(--tw-border-opacity, 1));
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 500;
  text-transform: uppercase;
  min-width: 120px;
}

#view-selector.selectbox-button:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 86 47 / var(--tw-border-opacity, 1));
}

#view-selector.selectbox-button:focus {
  --tw-border-opacity: 1;
  border-color: rgb(255 86 47 / var(--tw-border-opacity, 1));
}

#prev-btn:focus, #next-btn:focus, .today-btn:focus {
  --tw-border-opacity: 1;
  border-color: rgb(255 86 47 / var(--tw-border-opacity, 1));
  outline: 2px solid transparent;
  outline-offset: 2px;
}

/* Calendar title centering fix */

#custom-calendar-title {
  margin: 0px;
  width: 100%;
  text-align: center;
}

/* Arrow rotation for next button */

#next-btn .icon-arrow-down {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

#prev-btn .icon-arrow-down {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

/* Responsive */

/* Ensure all day cells have equal dimensions */

.fc-scrollgrid table {
  table-layout: fixed;
}

.fc-daygrid-day-top {
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.fc-daygrid-day-events {
  position: relative;
  margin-top: 0.25rem;
  flex: 1 1 0%;
}

@media (max-width: 768px) {
  .fc .fc-col-header-cell {
    padding-top: 1rem;
    padding-bottom: 1rem;
    padding-left: 0px;
    padding-right: 0px;
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .fc-daygrid-event {
    font-size: 0.75rem;
  }

  .fc-scrollgrid {
    overflow-x: auto;
  }
}

@media (max-width: 480px) {
  .fc-daygrid-event {
    padding-left: 0.125rem;
    padding-right: 0.125rem;
    padding-top: 1px;
    padding-bottom: 1px;
    font-size: 0.75rem;
  }

  .fc-daygrid-day-number {
    padding: 0.25rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  /* Responsive controls */

  #prev-btn, #next-btn {
    height: 3rem;
    width: 3rem;
  }

  .today-btn, #view-selector {
    padding-left: 1rem;
    padding-right: 1rem;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    font-size: 0.75rem;
    line-height: 1rem;
  }

  .toggle-btn {
    height: 3rem;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    font-size: 0.75rem;
    line-height: 1rem;
  }
}

/* Today's date highlighting */

.fc-day-today .fc-daygrid-day-number {
  font-weight: 700;
  --tw-text-opacity: 1;
  color: rgb(255 86 47 / var(--tw-text-opacity, 1));
}

/* Consistent day cell styling */

.fc-scrollgrid-section-body table {
  height: 100%;
  width: 100%;
}

*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/* ! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com */

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

.fc {
  --fc-border-color: #D9D8D6;
  --fc-button-text-color: #ffffff;
  --fc-button-bg-color: #FF562F;
  --fc-button-border-color: #FF562F;
  --fc-button-hover-bg-color: #FF562F;
  --fc-button-hover-border-color: #FF562F;
  --fc-button-active-bg-color: #FF562F;
  --fc-button-active-border-color: #FF562F;
  --fc-event-bg-color: #FF562F;
  --fc-event-border-color: #FF562F;
  --fc-event-text-color: #ffffff;
  --fc-today-bg-color: #FFF9F7;
}

html {
  scroll-behavior: smooth;
}

body {
  font-feature-settings: "rlig" 1, "calt" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--color-text-900);
  font-family: var(--font-sans-normal);
  font-size: var(--font-size-base);
  font-size: 0.875rem;
  line-height: 1.2;
}

@media (min-width: 640px) {
  body {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

/* Focus styles for accessibility */

/* :focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
  }

  :focus:not(:focus-visible) {
    outline: none;
  } */

h1 {
  font-family: var(--font-heading);
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  --tw-text-opacity: 1;
  color: rgb(4 7 8 / var(--tw-text-opacity, 1));
}

@media (min-width: 640px) {
  h1 {
    font-size: 3.5rem;
  }
}

@media (min-width: 1024px) {
  h1 {
    font-size: 4.5rem;
  }
}

h2,.heading {
  font-family: var(--font-sans-bold);
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.4;
  --tw-text-opacity: 1;
  color: rgb(4 7 8 / var(--tw-text-opacity, 1));
}

@media (min-width: 640px) {
  h2,.heading {
    font-size: 2.5rem;
  }
}

@media (min-width: 1024px) {
  h2,.heading {
    font-size: 3rem;
  }
}

h3 {
  font-family: var(--font-sans-bold);
  font-size: 1.75rem;
  font-weight: 700;
  line-height: 1.4;
  --tw-text-opacity: 1;
  color: rgb(4 7 8 / var(--tw-text-opacity, 1));
}

@media (min-width: 640px) {
  h3 {
    font-size: 2rem;
  }
}

@media (min-width: 1024px) {
  h3 {
    font-size: 2.25rem;
  }
}

h4 {
  font-family: var(--font-sans-bold);
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1.4;
  --tw-text-opacity: 1;
  color: rgb(4 7 8 / var(--tw-text-opacity, 1));
}

@media (min-width: 640px) {
  h4 {
    font-size: 1.5rem;
  }
}

h5 {
  font-family: var(--font-sans-bold);
  font-size: 1rem;
  font-weight: 700;
  line-height: 1.4;
  --tw-text-opacity: 1;
  color: rgb(73 71 70 / var(--tw-text-opacity, 1));
}

h6 {
  font-family: var(--font-sans-bold);
  font-size: 1rem;
  font-weight: 700;
  line-height: 1.4;
  --tw-text-opacity: 1;
  color: rgb(4 7 8 / var(--tw-text-opacity, 1));
}

p {
  line-height: 1.4;
}

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 16px;
  padding-left: 16px;
}

@media (min-width: 1332px) {
  .container {
    max-width: 1332px;
  }
}

/* Body class management for mobile menu */

body.mobile-menu-open {
  position: fixed;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.main-nav {
  display: none;
}

#page-loader.main-nav {
  opacity: 0;
  visibility: hidden;
}

@media (min-width: 1024px) {
  .main-nav {
    display: block;
  }
}

.nav-list {
  margin: 0px;
  display: flex;
  list-style-type: none;
  gap: 2rem;
  padding: 0px;
}

.nav-link {
  position: relative;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(4 7 8 / var(--tw-text-opacity, 1));
  text-decoration-line: none;
  transition-property: all;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-link:hover {
  --tw-text-opacity: 1;
  color: rgb(255 86 47 / var(--tw-text-opacity, 1));
}

.nav-link:focus {
  /* @apply focus:outline focus:outline-2 focus:outline-primary-500 focus:outline-offset-2 focus:text-primary-500 focus:underline; */
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

.nav-link:focus:focus {
  --tw-text-opacity: 1;
  color: rgb(255 86 47 / var(--tw-text-opacity, 1));
  text-decoration-line: underline;
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.nav-link.active {
  --tw-text-opacity: 1;
  color: rgb(255 86 47 / var(--tw-text-opacity, 1));
}

.mobile-menu-close-btn {
  position: fixed;
  top: 30px;
  right: 1rem;
  z-index: 10001;
  display: none;
  --tw-rotate: -90deg;
  --tw-scale-x: 0.8;
  --tw-scale-y: 0.8;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  cursor: pointer;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  opacity: 0;
  transition-property: all;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

#page-loader.mobile-menu-close-btn {
  opacity: 0;
  visibility: hidden;
}

@media (min-width: 1024px) {
  body.mobile-menu-open {
    position: static;
    height: auto;
    width: auto;
    overflow: auto;
  }

  .mobile-menu-close-btn {
    display: none;
  }

  #page-loader.mobile-menu-close-btn {
    opacity: 0;
    visibility: hidden;
  }
}

@media (max-width: 1023.98px) {
  .main-nav {
    position: fixed;
    top: 0px;
    left: 0px;
    z-index: 10000;
    height: 100dvh;
    width: 100%;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  body.mobile-menu-open .main-nav {
    top: 72px;
    display: flex;
    height: calc(100dvh - 72px);
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    overflow-y: auto;
    padding: 2rem;
    opacity: 1;
    visibility: visible;
  }

  body.mobile-menu-open .nav-list {
    width: 100%;
    flex-direction: column;
    align-items: center;
    gap: 0px;
  }

  body.mobile-menu-open .nav-list li {
    margin-bottom: 1.5rem;
    width: 100%;
    transform: translateX(-50px);
    opacity: 0;
    animation: slideInMobile 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  }

  body.mobile-menu-open .nav-link {
    position: relative;
    display: block;
    width: 100%;
    overflow: hidden;
    border-radius: 12px;
    padding-top: 1rem;
    padding-bottom: 1rem;
    padding-left: 2rem;
    padding-right: 2rem;
    font-size: 2rem;
    font-weight: 500;
    --tw-text-opacity: 1;
    color: rgb(4 7 8 / var(--tw-text-opacity, 1));
  }

  body.mobile-menu-open .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 86, 47, 0.1), transparent);
    transition: left 0.6s ease;
  }

  body.mobile-menu-open .nav-link:hover::before {
    left: 100%;
  }

  body.mobile-menu-open .nav-link:hover {
    --tw-text-opacity: 1;
    color: rgb(255 86 47 / var(--tw-text-opacity, 1));
    background: rgba(255, 86, 47, 0.05);
    transform: translateY(-2px);
  }

  body.mobile-menu-open #header-logo {
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 10001;
    animation: logoSlideIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s both;
  }

  body.mobile-menu-open #header-controls {
    pointer-events: none;
    opacity: 0;
  }

  body.mobile-menu-open .mobile-menu-close-btn {
    display: flex;
    --tw-rotate: 0deg;
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    opacity: 1;
    animation: closeButtonSlideIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s both;
  }

  .mobile-menu-close-btn:hover {
    transform: scale(1.05);
  }

  .mobile-menu-close-btn:active {
    transform: scale(0.95);
  }
}

@media (max-width: 767.98px) {
  body.mobile-menu-open .nav-link {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    font-size: 1.125rem;
  }

  body.mobile-menu-open .nav-list li {
    margin-bottom: 1rem;
  }
}

.card-shape {
  position: relative;
  margin-top: 64px;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(217 216 214 / var(--tw-border-opacity, 1));
  padding: 2rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

.card-shape:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 86 47 / var(--tw-border-opacity, 1));
}

.card-shape::before {
  position: absolute;
  top: -2px;
  left: 0px;
  z-index: 1;
  height: 3px;
  width: 29px;
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

/* Container with responsive padding */

.container {
  margin-left: auto;
  margin-right: auto;
  max-width: 1332px;
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Language toggle button */

/* Ensure backdrop blur works in all browsers */

.backdrop-blur-sm {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Custom dropdown animation */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: sticky;
}

.inset-0 {
  inset: 0px;
}

.-left-\[1px\] {
  left: -1px;
}

.-top-\[56px\] {
  top: -56px;
}

.bottom-0 {
  bottom: 0px;
}

.end-1 {
  inset-inline-end: 0.25rem;
}

.left-0 {
  left: 0px;
}

.left-4 {
  left: 1rem;
}

.left-6 {
  left: 1.5rem;
}

.left-\[50\%\] {
  left: 50%;
}

.right-0 {
  right: 0px;
}

.top-0 {
  top: 0px;
}

.top-1\/2 {
  top: 50%;
}

.top-4 {
  top: 1rem;
}

.top-\[50\%\] {
  top: 50%;
}

.z-0 {
  z-index: 0;
}

.z-10 {
  z-index: 10;
}

.z-50 {
  z-index: 50;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.m-0 {
  margin: 0px;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-10 {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}

.mb-0 {
  margin-bottom: 0px;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.mb-16 {
  margin-bottom: 4rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.me-2 {
  margin-inline-end: 0.5rem;
}

.me-4 {
  margin-inline-end: 1rem;
}

.me-\[0\.5rem\] {
  margin-inline-end: 0.5rem;
}

.ms-2 {
  margin-inline-start: 0.5rem;
}

.ms-4 {
  margin-inline-start: 1rem;
}

.ms-auto {
  margin-inline-start: auto;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-12 {
  margin-top: 3rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mt-auto {
  margin-top: auto;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.line-clamp-5 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.list-item {
  display: list-item;
}

.hidden {
  display: none;
}

.aspect-\[1\/1\] {
  aspect-ratio: 1/1;
}

.aspect-\[16\/9\] {
  aspect-ratio: 16/9;
}

.aspect-\[4\/3\] {
  aspect-ratio: 4/3;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

.h-10 {
  height: 2.5rem;
}

.h-12 {
  height: 3rem;
}

.h-16 {
  height: 4rem;
}

.h-6 {
  height: 1.5rem;
}

.h-7 {
  height: 1.75rem;
}

.h-8 {
  height: 2rem;
}

.h-80 {
  height: 20rem;
}

.h-\[100\%\] {
  height: 100%;
}

.h-\[106px\] {
  height: 106px;
}

.h-\[15\%\] {
  height: 15%;
}

.h-\[350px\] {
  height: 350px;
}

.h-\[360px\] {
  height: 360px;
}

.h-\[40\%\] {
  height: 40%;
}

.h-\[400px\] {
  height: 400px;
}

.h-\[40px\] {
  height: 40px;
}

.h-\[45\%\] {
  height: 45%;
}

.h-\[466px\] {
  height: 466px;
}

.h-\[50\%\] {
  height: 50%;
}

.h-\[55\%\] {
  height: 55%;
}

.h-\[5rem\] {
  height: 5rem;
}

.h-\[60\%\] {
  height: 60%;
}

.h-\[70\%\] {
  height: 70%;
}

.h-auto {
  height: auto;
}

.h-full {
  height: 100%;
}

.max-h-\[100\%\] {
  max-height: 100%;
}

.min-h-\[400px\] {
  min-height: 400px;
}

.min-h-\[600px\] {
  min-height: 600px;
}

.min-h-screen {
  min-height: 100vh;
}

.w-10 {
  width: 2.5rem;
}

.w-12 {
  width: 3rem;
}

.w-16 {
  width: 4rem;
}

.w-56 {
  width: 14rem;
}

.w-6 {
  width: 1.5rem;
}

.w-7 {
  width: 1.75rem;
}

.w-8 {
  width: 2rem;
}

.w-\[106px\] {
  width: 106px;
}

.w-\[120px\] {
  width: 120px;
}

.w-\[150px\] {
  width: 150px;
}

.w-\[190px\] {
  width: 190px;
}

.w-\[25\%\] {
  width: 25%;
}

.w-\[301px\] {
  width: 301px;
}

.w-\[40px\] {
  width: 40px;
}

.w-\[70px\] {
  width: 70px;
}

.w-auto {
  width: auto;
}

.w-full {
  width: 100%;
}

.min-w-0 {
  min-width: 0px;
}

.min-w-\[58px\] {
  min-width: 58px;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-\[100\%\] {
  max-width: 100%;
}

.max-w-\[336px\] {
  max-width: 336px;
}

.max-w-\[470px\] {
  max-width: 470px;
}

.max-w-\[684px\] {
  max-width: 684px;
}

.max-w-lg {
  max-width: 32rem;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-none {
  max-width: none;
}

.max-w-xl {
  max-width: 36rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.shrink {
  flex-shrink: 1;
}

.flex-grow {
  flex-grow: 1;
}

.origin-top-right {
  transform-origin: top right;
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-rotate-90 {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform-none {
  transform: none;
}

.cursor-pointer {
  cursor: pointer;
}

.resize {
  resize: both;
}

.list-disc {
  list-style-type: disc;
}

.list-none {
  list-style-type: none;
}

.columns-1 {
  -moz-columns: 1;
       columns: 1;
}

.break-inside-avoid {
  -moz-column-break-inside: avoid;
       break-inside: avoid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-3 {
  gap: 0.75rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-x-12 {
  -moz-column-gap: 3rem;
       column-gap: 3rem;
}

.gap-y-4 {
  row-gap: 1rem;
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}

.self-start {
  align-self: flex-start;
}

.overflow-hidden {
  overflow: hidden;
}

.text-ellipsis {
  text-overflow: ellipsis;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.border {
  border-width: 1px;
}

.border-2 {
  border-width: 2px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-t {
  border-top-width: 1px;
}

.border-dashed {
  border-style: dashed;
}

.border-none {
  border-style: none;
}

.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(217 216 214 / var(--tw-border-opacity, 1));
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.border-primary-500 {
  --tw-border-opacity: 1;
  border-color: rgb(255 86 47 / var(--tw-border-opacity, 1));
}

.border-transparent {
  border-color: transparent;
}

.border-white\/20 {
  border-color: rgb(255 255 255 / 0.2);
}

.bg-gray-100\/20 {
  background-color: rgb(217 216 214 / 0.2);
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(246 246 246 / var(--tw-bg-opacity, 1));
}

.bg-primary-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 242 237 / var(--tw-bg-opacity, 1));
}

.bg-primary-100\/50 {
  background-color: rgb(255 242 237 / 0.5);
}

.bg-primary-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 249 247 / var(--tw-bg-opacity, 1));
}

.bg-primary-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 86 47 / var(--tw-bg-opacity, 1));
}

.bg-primary-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 52 17 / var(--tw-bg-opacity, 1));
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.bg-white\/95 {
  background-color: rgb(255 255 255 / 0.95);
}

.bg-\[linear-gradient\(180deg\2c rgba\(0\2c 0\2c 0\2c 0\)_15\.05\%\2c rgba\(0\2c 0\2c 0\2c 0\.8\)_112\.51\%\)\] {
  background-image: linear-gradient(180deg,rgba(0,0,0,0) 15.05%,rgba(0,0,0,0.8) 112.51%);
}

.bg-\[linear-gradient\(180deg\2c rgba\(0\2c 0\2c 0\2c 0\.2\)_20\%\2c rgba\(255\2c 86\2c 47\2c 0\.8\)_172\.67\%\)\] {
  background-image: linear-gradient(180deg,rgba(0,0,0,0.2) 20%,rgba(255,86,47,0.8) 172.67%);
}

.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}

.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}

.from-\[\#FFF2ED\] {
  --tw-gradient-from: #FFF2ED var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 242 237 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-white {
  --tw-gradient-from: #fff var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-50\% {
  --tw-gradient-from-position: 50%;
}

.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.to-50\% {
  --tw-gradient-to-position: 50%;
}

.bg-\[length\:70\%\] {
  background-size: 70%;
}

.bg-contain {
  background-size: contain;
}

.bg-cover {
  background-size: cover;
}

.bg-center {
  background-position: center;
}

.bg-repeat {
  background-repeat: repeat;
}

.bg-repeat-x {
  background-repeat: repeat-x;
}

.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}

.p-0 {
  padding: 0px;
}

.p-1 {
  padding: 0.25rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-4 {
  padding: 1rem;
}

.p-6 {
  padding: 1.5rem;
}

.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-\[0\.25rem\] {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-\[36px\] {
  padding-top: 36px;
  padding-bottom: 36px;
}

.pb-0 {
  padding-bottom: 0px;
}

.pb-16 {
  padding-bottom: 4rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pb-\[160px\] {
  padding-bottom: 160px;
}

.pb-\[40px\] {
  padding-bottom: 40px;
}

.pe-4 {
  padding-inline-end: 1rem;
}

.pe-6 {
  padding-inline-end: 1.5rem;
}

.ps-14 {
  padding-inline-start: 3.5rem;
}

.ps-6 {
  padding-inline-start: 1.5rem;
}

.pt-0 {
  padding-top: 0px;
}

.pt-16 {
  padding-top: 4rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pt-8 {
  padding-top: 2rem;
}

.pt-\[40px\] {
  padding-top: 40px;
}

.pt-\[72px\] {
  padding-top: 72px;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-end {
  text-align: end;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-\[1\.125rem\] {
  font-size: 1.125rem;
}

.text-\[1\.25rem\] {
  font-size: 1.25rem;
}

.text-\[1\.75rem\] {
  font-size: 1.75rem;
}

.text-\[16px\] {
  font-size: 16px;
}

.text-\[18px\] {
  font-size: 18px;
}

.text-\[1rem\] {
  font-size: 1rem;
}

.text-\[20px\] {
  font-size: 20px;
}

.text-\[24px\] {
  font-size: 24px;
}

.text-\[2rem\] {
  font-size: 2rem;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-bold {
  font-weight: 700;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.capitalize {
  text-transform: capitalize;
}

.leading-\[1\.1\] {
  line-height: 1.1;
}

.leading-\[1\.28\] {
  line-height: 1.28;
}

.leading-\[1\.3\] {
  line-height: 1.3;
}

.leading-\[1\.4\] {
  line-height: 1.4;
}

.leading-\[1\.5\] {
  line-height: 1.5;
}

.leading-relaxed {
  line-height: 1.625;
}

.leading-tight {
  line-height: 1.25;
}

.tracking-tight {
  letter-spacing: -0.025em;
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}

.text-danger-500 {
  --tw-text-opacity: 1;
  color: rgb(226 65 56 / var(--tw-text-opacity, 1));
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(73 71 70 / var(--tw-text-opacity, 1));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(4 7 8 / var(--tw-text-opacity, 1));
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(73 71 70 / var(--tw-text-opacity, 1));
}

.text-primary-500 {
  --tw-text-opacity: 1;
  color: rgb(255 86 47 / var(--tw-text-opacity, 1));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.underline {
  text-decoration-line: underline;
}

.overline {
  text-decoration-line: overline;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.placeholder-gray-800::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(4 7 8 / var(--tw-placeholder-opacity, 1));
}

.placeholder-gray-800::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(4 7 8 / var(--tw-placeholder-opacity, 1));
}

.opacity-0 {
  opacity: 0;
}

.opacity-25 {
  opacity: 0.25;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-\[0\.05\] {
  opacity: 0.05;
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-inherit {
  --tw-shadow-color: inherit;
  --tw-shadow: var(--tw-shadow-colored);
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.invert {
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-500 {
  transition-duration: 500ms;
}

.duration-\[3s\] {
  transition-duration: 3s;
}

.\!ease-linear {
  transition-timing-function: linear !important;
}

.ease-linear {
  transition-timing-function: linear;
}

/* Animation classes */

/* Custom Properties for consistent theming */

:root {
  --font-size-base: 16px;
  --color-text-800: #040708;
  --color-text-900: #494746;
  --color-background: #ffffff;
  --font-sans-normal: 'brando_sanstext', sans-serif;
  --font-sans-bold: 'brando_sansbold', sans-serif;
  --font-arabic: 'brando_arabicbold', sans-serif;
  --font-heading: 'Objektiv Mk2 Trial XBold', sans-serif;
  --font-heading-arabic: 'Araboto', sans-serif;
}

:root[lang="ar"] {
  body {
    font-family: var(--font-arabic);
  }
  h1 {
    font-family: var(--font-heading-arabic);
  }
}

/* Base styles */

/* Component styles */

/* Utility classes */

/* High contrast mode support */

@media (prefers-contrast: high) {
  :root {
    --color-primary: var(--color-primary);
    --color-text: var(--color-text-900);
    --color-background: #ffffff;
  }
}

/* Reduced motion support */

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .main-nav,
  .nav-link,
  .mobile-menu-close-btn,
  #header-logo {
    transition: none;
    animation: none !important;
  }

  body.mobile-menu-open .nav-list li {
    animation: none !important;
    transform: translateX(0);
    opacity: 1;
  }

  body.mobile-menu-open .nav-link::before {
    display: none;
  }
}

/* Print styles */

@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}

/* Hero Section */

.hero-section {
  min-height: calc(100dvh - 104px);
}

.content-section, .acclaim-panel {
  p {
    margin-bottom: 1.25rem;
  }
}

/* Page Loader */

#page-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: opacity 0.3s ease-in-out;
  opacity: 1;
  visibility: visible;
}

#page-loader.hidden {
  opacity: 0;
  visibility: hidden;
}

.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #F05121;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.hover\:scale-100:hover {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:transform-none:hover {
  transform: none;
}

.hover\:border-primary-500:hover {
  --tw-border-opacity: 1;
  border-color: rgb(255 86 47 / var(--tw-border-opacity, 1));
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(217 216 214 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(246 246 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-primary-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 86 47 / var(--tw-bg-opacity, 1));
}

.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(179 173 166 / var(--tw-text-opacity, 1));
}

.hover\:text-primary-500:hover {
  --tw-text-opacity: 1;
  color: rgb(255 86 47 / var(--tw-text-opacity, 1));
}

.hover\:text-primary-600:hover {
  --tw-text-opacity: 1;
  color: rgb(254 52 17 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.focus\:not-sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

.focus\:absolute:focus {
  position: absolute;
}

.focus\:left-2:focus {
  left: 0.5rem;
}

.focus\:top-2:focus {
  top: 0.5rem;
}

.focus\:border-b-2:focus {
  border-bottom-width: 2px;
}

.focus\:border-primary-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(255 86 47 / var(--tw-border-opacity, 1));
}

.focus\:border-primary-600:focus {
  --tw-border-opacity: 1;
  border-color: rgb(254 52 17 / var(--tw-border-opacity, 1));
}

.focus\:text-primary-500:focus {
  --tw-text-opacity: 1;
  color: rgb(255 86 47 / var(--tw-text-opacity, 1));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-primary-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 86 47 / var(--tw-ring-opacity, 1));
}

.focus\:ring-opacity-20:focus {
  --tw-ring-opacity: 0.2;
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.group:hover .group-hover\:scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:border-primary-500 {
  --tw-border-opacity: 1;
  border-color: rgb(255 86 47 / var(--tw-border-opacity, 1));
}

.group:hover .group-hover\:bg-primary-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 249 247 / var(--tw-bg-opacity, 1));
}

.group:hover .group-hover\:stroke-primary-500 {
  stroke: #FF562F;
}

.group:hover .group-hover\:text-primary-500 {
  --tw-text-opacity: 1;
  color: rgb(255 86 47 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.group:focus .group-focus\:text-primary-500 {
  --tw-text-opacity: 1;
  color: rgb(255 86 47 / var(--tw-text-opacity, 1));
}

.group.swiper-slide-active .group-\[\.swiper-slide-active\]\:pb-0 {
  padding-bottom: 0px;
}

.group.swiper-slide-active .group-\[\.swiper-slide-active\]\:pt-\[160px\] {
  padding-top: 160px;
}

.group.swiper-slide-next .group-\[\.swiper-slide-next\]\:pt-\[80px\] {
  padding-top: 80px;
}

.group.swiper-slide-prev .group-\[\.swiper-slide-prev\]\:pb-\[80px\] {
  padding-bottom: 80px;
}

.group.swiper-slide-prev .group-\[\.swiper-slide-prev\]\:pt-\[80px\] {
  padding-top: 80px;
}

@media (min-width: 640px) {
  .sm\:mb-12 {
    margin-bottom: 3rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:aspect-\[587\/634\] {
    aspect-ratio: 587/634;
  }

  .sm\:h-\[400px\] {
    height: 400px;
  }

  .sm\:h-\[500px\] {
    height: 500px;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:min-w-\[400px\] {
    min-width: 400px;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:flex-col {
    flex-direction: column;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-start {
    justify-content: flex-start;
  }

  .sm\:gap-8 {
    gap: 2rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:text-start {
    text-align: start;
  }

  .sm\:text-\[1\.125rem\] {
    font-size: 1.125rem;
  }

  .sm\:text-\[1\.5rem\] {
    font-size: 1.5rem;
  }

  .sm\:text-\[2\.5rem\] {
    font-size: 2.5rem;
  }

  .sm\:text-\[2rem\] {
    font-size: 2rem;
  }
}

@media (min-width: 768px) {
  .md\:order-1 {
    order: 1;
  }

  .md\:order-2 {
    order: 2;
  }

  .md\:mb-12 {
    margin-bottom: 3rem;
  }

  .md\:mb-8 {
    margin-bottom: 2rem;
  }

  .md\:block {
    display: block;
  }

  .md\:hidden {
    display: none;
  }

  .md\:aspect-\[4\/4\] {
    aspect-ratio: 4/4;
  }

  .md\:h-\[100\%\] {
    height: 100%;
  }

  .md\:h-\[500px\] {
    height: 500px;
  }

  .md\:columns-2 {
    -moz-columns: 2;
         columns: 2;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:gap-5 {
    gap: 1.25rem;
  }

  .md\:p-6 {
    padding: 1.5rem;
  }

  .md\:py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }

  .md\:pb-10 {
    padding-bottom: 2.5rem;
  }

  .md\:pb-20 {
    padding-bottom: 5rem;
  }

  .md\:pt-20 {
    padding-top: 5rem;
  }
}

@media (min-width: 1024px) {
  .lg\:order-1 {
    order: 1;
  }

  .lg\:order-2 {
    order: 2;
  }

  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .lg\:mb-12 {
    margin-bottom: 3rem;
  }

  .lg\:me-0 {
    margin-inline-end: 0px;
  }

  .lg\:ms-auto {
    margin-inline-start: auto;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:h-\[600px\] {
    height: 600px;
  }

  .lg\:max-w-\[440px\] {
    max-width: 440px;
  }

  .lg\:max-w-\[475px\] {
    max-width: 475px;
  }

  .lg\:max-w-\[634px\] {
    max-width: 634px;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:justify-center {
    justify-content: center;
  }

  .lg\:gap-12 {
    gap: 3rem;
  }

  .lg\:gap-8 {
    gap: 2rem;
  }

  .lg\:space-y-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(2rem * var(--tw-space-y-reverse));
  }

  .lg\:p-8 {
    padding: 2rem;
  }

  .lg\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .lg\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .lg\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .lg\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .lg\:py-\[48px\] {
    padding-top: 48px;
    padding-bottom: 48px;
  }

  .lg\:pb-12 {
    padding-bottom: 3rem;
  }

  .lg\:pb-20 {
    padding-bottom: 5rem;
  }

  .lg\:pb-\[0px\] {
    padding-bottom: 0px;
  }

  .lg\:pt-10 {
    padding-top: 2.5rem;
  }

  .lg\:pt-20 {
    padding-top: 5rem;
  }

  .lg\:pt-8 {
    padding-top: 2rem;
  }

  .lg\:pt-\[104px\] {
    padding-top: 104px;
  }

  .lg\:text-left {
    text-align: left;
  }

  .lg\:text-start {
    text-align: start;
  }

  .lg\:text-end {
    text-align: end;
  }

  .lg\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .lg\:text-\[1\.125rem\] {
    font-size: 1.125rem;
  }

  .lg\:text-\[2\.25rem\] {
    font-size: 2.25rem;
  }

  .lg\:text-\[2\.5rem\] {
    font-size: 2.5rem;
  }

  .lg\:text-\[24px\] {
    font-size: 24px;
  }

  .lg\:text-\[3rem\] {
    font-size: 3rem;
  }

  .lg\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1280px) {
  .xl\:mb-12 {
    margin-bottom: 3rem;
  }

  .xl\:mb-5 {
    margin-bottom: 1.25rem;
  }

  .xl\:line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  .xl\:max-w-\[482px\] {
    max-width: 482px;
  }

  .xl\:max-w-\[500px\] {
    max-width: 500px;
  }

  .xl\:gap-8 {
    gap: 2rem;
  }

  .xl\:text-\[3rem\] {
    font-size: 3rem;
  }
}
