<?php

use App\Http\Controllers\Frontend\{
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Author<PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    NewsC<PERSON>roller,
    Event<PERSON><PERSON>roller,
    TestimonialController,
    <PERSON>lient<PERSON><PERSON>roller,
    Submission<PERSON><PERSON>roller,
    <PERSON><PERSON><PERSON>roller,
    FAQController,
};
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Frontend Routes
|--------------------------------------------------------------------------
|
| Here are the frontend routes for the Sharjah Literary Agency website.
| All routes use Laravel 12 syntax with controller::class format.
|
*/

// Homepage
Route::get('/', [HomeController::class, 'index'])->name('home');

// About Us
Route::get('/about', [AboutController::class, 'index'])->name('about');

// Services
Route::get('/services', [ServiceController::class, 'index'])->name('services');

// Authors
Route::controller(AuthorController::class)->prefix('authors')->name('authors.')->group(function () {
    Route::get('/', 'index')->name('index');
    Route::get('/{author}', 'show')->name('show');
});

// Books
Route::controller(BookController::class)->prefix('books')->name('books.')->group(function () {
    Route::get('/', 'index')->name('index');
    Route::get('/{book}', 'show')->name('show');
});

// News
Route::controller(NewsController::class)->prefix('news')->name('news.')->group(function () {
    Route::get('/', 'index')->name('index');
    Route::get('/{news}', 'show')->name('show');
});

// Events
Route::controller(EventController::class)->prefix('events')->name('events.')->group(function () {
    Route::get('/', 'index')->name('index');
    Route::get('/{event}', 'show')->name('show');
    Route::get('/{event}/register', 'register')->name('register');
    Route::post('/{event}/register', 'storeRegistration')->name('registration.store');
    Route::get('/registration/success', 'registrationSuccess')->name('registration.success');
});

// Testimonials
Route::get('/testimonials', [TestimonialController::class, 'index'])->name('testimonials');

// Clients
Route::get('/clients', [ClientController::class, 'index'])->name('clients');

// Faqs
Route::get('/faqs', [FAQController::class, 'index'])->name('faqs');

// Submissions
Route::controller(SubmissionController::class)->prefix('submissions')->name('submissions.')->group(function () {
    Route::get('/', 'index')->name('index');
    Route::get('/create', 'create')->name('create');
    Route::post('/', 'store')->name('store');
    Route::get('/success', 'success')->name('success');
});

// Contact
Route::controller(ContactController::class)->prefix('contact')->name('contact.')->group(function () {
    Route::get('/', 'index')->name('index');
    Route::post('/', 'store')->name('store');
    Route::get('/success', 'success')->name('success');
});

// Newsletter Subscription (AJAX endpoint)
Route::post('/newsletter', [ContactController::class, 'newsletter'])->name('newsletter.subscribe');

// Language Switching
Route::get('/lang/{locale}', function (string $locale) {
    if (in_array($locale, ['en', 'ar'])) {
        // Persist in session
        session(['locale' => $locale]);

        // Also persist in cookie for guests/bots and to survive session expiry
        cookie()->queue(cookie('locale', $locale, 60 * 24 * 365)); // 1 year
    }

    // Apply immediately for current request lifecycle (useful in some contexts)
    app()->setLocale($locale);

    return redirect()->back();
})->name('lang.switch');
