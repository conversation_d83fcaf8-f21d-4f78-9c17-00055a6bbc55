<form method="POST" action="{{ $action }}" enctype="multipart/form-data">
    @csrf
    @if (isset($section->id))
        @method('PUT')
    @endif

    @include('section::partials.language-switcher')
    <div class="row">
        <div class="col-md-6 mb-3">
            <label for="parent_id" class="form-label">{{ __('section::section.parent') }}</label>
            <select name="parent_id" id="parent_id" class="form-select">
                {{-- <option value="">{{ __('section::section.parent_placeholder') }}</option> --}}
                @foreach ($parentSections as $key => $parentSection)
                    <option value="{{ $key }}"
                        {{ old('parent_id', $section->parent_id ?? '') == $key ? 'selected' : '' }}>
                        {{ $parentSection }}</option>
                @endforeach
            </select>
        </div>

        <div class="col-md-12 mb-4">
            <div class="card border-primary" style="border-width: 2px;">
                <div class="card-header bg-primary bg-opacity-10">
                    <h5 class="mb-0"><i class="fa fa-language"></i> {{ __('admin.common.translatable_fields') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="title_en" class="form-label">{{ __('section::section.title') }}</label>
                            <div class="form-group {{ isset($errors) && $errors->has('title.en') ? 'has-error' : '' }} translatable-field" data-language="en">
                                <input type="text" name="title[en]" id="title_en" class="form-control"
                                    value="{{ old('title.en', isset($section) ? $section->getTranslation('title', 'en', false) : '') }}"
                                    placeholder="{{ __('section::section.title_placeholder') }}">
                                @if (isset($errors) && $errors->has('title.en'))
                                    <span class="help-block">{{ $errors->first('title.en') }}</span>
                                @endif
                            </div>
                            <div class="form-group {{ isset($errors) && $errors->has('title.ar') ? 'has-error' : '' }} translatable-field" data-language="ar" style="display: none;">
                                <input type="text" name="title[ar]" id="title_ar" class="form-control" dir="rtl"
                                    value="{{ old('title.ar', isset($section) ? $section->getTranslation('title', 'ar', false) : '') }}"
                                    placeholder="{{ __('section::section.title_placeholder') }}">
                                @if (isset($errors) && $errors->has('title.ar'))
                                    <span class="help-block">{{ $errors->first('title.ar') }}</span>
                                @endif
                            </div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="sub_title_en" class="form-label">{{ __('section::section.sub_title') }}</label>
                            <div class="form-group {{ isset($errors) && $errors->has('sub_title.en') ? 'has-error' : '' }} translatable-field" data-language="en">
                                <input type="text" name="sub_title[en]" id="sub_title_en" class="form-control"
                                    value="{{ old('sub_title.en', isset($section) ? $section->getTranslation('sub_title', 'en', false) : '') }}"
                                    placeholder="{{ __('section::section.sub_title_placeholder') }}">
                                @if (isset($errors) && $errors->has('sub_title.en'))
                                    <span class="help-block">{{ $errors->first('sub_title.en') }}</span>
                                @endif
                            </div>
                            <div class="form-group {{ isset($errors) && $errors->has('sub_title.ar') ? 'has-error' : '' }} translatable-field" data-language="ar" style="display: none;">
                                <input type="text" name="sub_title[ar]" id="sub_title_ar" class="form-control" dir="rtl"
                                    value="{{ old('sub_title.ar', isset($section) ? $section->getTranslation('sub_title', 'ar', false) : '') }}"
                                    placeholder="{{ __('section::section.sub_title_placeholder') }}">
                                @if (isset($errors) && $errors->has('sub_title.ar'))
                                    <span class="help-block">{{ $errors->first('sub_title.ar') }}</span>
                                @endif
                            </div>
                        </div>

                        <div class="col-md-12 mb-3 body">
                            <label for="body" class="form-label">{{ __('section::section.body') }}</label>
                            <div class="translatable-field" data-language="en">
                                <textarea name="body[en]" id="body_en" class="form-control ck-editor">{{ old('body.en', isset($section) ? $section->getTranslation('body', 'en', false) : '') }}</textarea>
                            </div>
                            <div class="translatable-field" data-language="ar" style="display: none;">
                                <textarea name="body[ar]" id="body_ar" class="form-control ck-editor" dir="rtl">{{ old('body.ar', isset($section) ? $section->getTranslation('body', 'ar', false) : '') }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fa fa-cog"></i> {{ __('admin.common.general_settings') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="slug" class="form-label">{{ __('section::section.slug') }}</label>
                            <input type="text" name="slug" id="slug" class="form-control"
                                value="{{ old('slug', $section->slug ?? '') }}"
                                placeholder="{{ __('section::section.slug_placeholder') }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">{{ __('section::section.status') }}</label>
                            <select name="status" id="status" class="form-select">
                                <option value="1" {{ old('status', $section->status ?? true) == 1 ? 'selected' : '' }}>
                                    {{ __('section::section.active') }}
                                </option>
                                <option value="0" {{ old('status', $section->status ?? '') == 0 ? 'selected' : '' }}>
                                    {{ __('section::section.in_active') }}
                                </option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="meta_keywords" class="form-label">{{ __('section::section.meta_keywords') }}</label>
                            <input type="text" name="meta_keywords" id="meta_keywords" class="form-control"
                                value="{{ old('meta_keywords', $section->meta_keywords ?? '') }}"
                                placeholder="{{ __('section::section.meta_keywords_placeholder') }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="meta_description" class="form-label">{{ __('section::section.meta_description') }}</label>
                            <textarea name="meta_description" id="meta_description" class="form-control">{{ old('meta_description', $section->meta_description ?? '') }}</textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12">
            <div class="d-flex align-items-center justify-content-end gap-3">
                <button type="submit" class="btn btn-primary">{{ __('admin.common.submit') }}</button>
                <a href="{{ route('admin.sections.index') }}"
                    class="btn btn-secondary">{{ __('admin.common.cancel') }}</a>
            </div>
        </div>
    </div>
</form>
@include('plugins.ckeditor')
@push('scripts')
    @php
        $validator = JsValidator::formRequest('Modules\Section\app\Http\Requests\SectionRequest');
        $validator->ignore('');
    @endphp
    {!! $validator !!}

    <script type="module">
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize CKEditors for both languages
            initializeCKEditor('body_en', options);

            // Set up Arabic CKEditor with RTL direction when needed
            const arOptions = Object.assign({}, options, {
                contentsLangDirection: 'rtl'
            });
            initializeCKEditor('body_ar', arOptions);

            // Hide the Arabic editor initially
            if (typeof CKEDITOR !== 'undefined') {
                if (CKEDITOR.instances.body_ar && CKEDITOR.instances.body_ar.container) {
                    CKEDITOR.instances.body_ar.container.setStyle('display', 'none');
                }
            }

            // Auto-generate slug from title
            $('#title_en').on('keyup change', function() {
                var title = $(this).val();
                var slug = generateSlug(title);
                $('#slug').val(slug);
            });

            // Function to generate slug
            function generateSlug(text) {
                if(!text) return '';

                // Convert to lowercase
                var slug = text.toLowerCase();

                // Replace spaces with hyphens
                slug = slug.replace(/\s+/g, '-');

                // Remove special characters
                slug = slug.replace(/[^\w\-]+/g, '');

                // Remove duplicate hyphens
                slug = slug.replace(/\-\-+/g, '-');

                // Remove leading and trailing hyphens
                slug = slug.replace(/^-+/, '').replace(/-+$/, '');

                return slug;
            }
        });
    </script>
@endpush
