@extends('admin.layouts.page')

@section('title', __('section::section.sections'))

@section('breadcrumb')
    <ol class="breadcrumb-item active">{{ __('section::section.sections') }}</ol>
@endsection

@section('action_buttons')
@permission('create-sections')
    <a href="{{ route('admin.sections.create') }}" class="btn btn-primary">
        <em class="inic inic-add"></em> {{ __('section::section.add_section') }}</a>
@endpermission
@endsection

@section('content')
    {!! $dataGridHtml !!}
@endsection
@push('scripts')

    <script type="module">
        $(document).on('click', '.delete-grid-row', function (event) {
            event.preventDefault();
            let url = $(this).attr('data-url');
            SwalConfirm(url, function (url) {
                $.ajax({
                    url: url,
                    type: 'delete',
                    data: {
                    "_token": "{{ csrf_token() }}"
                    },
                    datatype: 'json',
                    success: function (response) {
                        toastr.success(response.message);
                        window.inic_grid_sections.refresh();
                    },
                    error: function (response) {
                        toastr.error(response.message);
                    }
                });
            })
        })

        $(document).on('change','.change-status', function(){
            var id =  $(this).attr('data-id');
            var url = "{{ route('admin.sections.change_status')}}";
            $.ajax({
                url: url,
                type: "post",
                data: {
                    "_token": "{{ csrf_token() }}",
                    "id": id
                    },
                datatype: 'json',
                success: function (response) {
                    toastr.success(response.message);
                    window.inic_grid_sections.refresh();
                },
                error: function (response) {
                    toastr.error(response.message);
                }
            });
        });

        $(document).on('click', '.grid-bulk-action', function(event) {
            event.preventDefault();
            var id = [];var error = 0;
            $('.check_approval').each(function(index){
                if($(this).prop('checked') == true){
                    id.push($(this).val());
                    error = 1;
                }
            });

            var url= "{{ route('admin.sections.bulkAction') }}";
            var status=$(this).data('status');
            var type=$(this).data('action-type');

            if(error == 0){
                toastr.error("{{ __('section::section.please_select_one_section') }}");
            }else if(error == 1){
                SwalConfirm(url, function(url) {
                    $.ajax({
                        url: url,
                        type: 'post',
                        data: {id: id,status:status,type:type},
                        success: function(response) {
                            if(response.status == 'success'){
                                toastr.success(response.message);
                            }else{
                                toastr.error(response.message);
                            }
                            window.inic_grid_sections.refresh();
                        },
                        error: function(response) {
                            toastr.error(response.message);
                        }

                    });
                });
            }
        });

    </script>
@endpush
