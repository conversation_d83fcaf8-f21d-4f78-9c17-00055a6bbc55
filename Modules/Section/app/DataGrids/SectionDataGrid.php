<?php

namespace Modules\Section\app\DataGrids;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Indianic\LaravelDataGrid\LaravelDataGrid;

class SectionDataGrid extends LaravelDataGrid
{
    public const SECTIONNAME_TRANS = "section::section.section_name";
    public const PARENTSECTIONNAME_TRANS = "section::section.parent_section_name";
    public const STATUS_TRANS = "section::section.status";
    public const ACTIVE_TRANS = 'section::section.active';

    public $guard = 'admin';
    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'sections';

    /**
     * Define how many rows you want to display on a page
     * @var int $rowPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define row per page dropdown options
     * @var array $recordsPerPageOptions
     */
    public $recordsPerPageOptions;

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy = 'id';

    /**
     * Define default soring direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection = 'DESC';

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'sections';

    protected const SECTION = 'section::section';
    protected const SECTIONNAME = 'section::section.section_name';
    protected const SECTIONPARENT = 'section::section.parent_section_name';
    protected const SECTIONSTATUS = 'section::section.status';

    /**
     * Get Resource of Query Builder
     */
    public function resource()
    {
        return DB::table('sections')
            ->select('parent_cms_section.*',
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(parent_cms_section.title, '$.en')) as section_name"),
                'parent_cms_section.id as section_id',
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(sections.title, '$.en')) as parentSectionName")
            )->rightJoin('sections as parent_cms_section', function ($join) {
                $join->on('sections.id', 'parent_cms_section.parent_id');
            });
    }

    public function columns(): array
    {
        return [
            'checkbox' => '<input class="select_all checkbox" type="checkbox" />',
            'section_name' => trans(self::SECTIONNAME_TRANS),
            'parentSectionName' => trans(self::PARENTSECTIONNAME_TRANS),
            'status' => trans(self::STATUS_TRANS),
            'action' => __('section::section.action'),
        ];
    }

    public function mapDBColumns(): array
    {
        return [
            'section_name' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(parent_cms_section.title, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'parentSectionName' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(sections.title, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'status' => 'parent_cms_section.status',
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'section_name',
            'status',
        ];
    }

    // public function downloadableColumns(): array
    // {
    //     return [
    //         'section_name' => trans(self::SECTIONNAME_TRANS),
    //         'parentSectionName' => trans(self::PARENTSECTIONNAME_TRANS),
    //         'status' => trans(self::STATUS_TRANS),
    //     ];
    // }

    public function gloablSearchableColumns(): array
    {
        return [
            'section_name' => 'string',
            'parentSectionName' => 'string',
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'section_name' => 'string',
            'parentSectionName' => 'string',
        ];
    }

    // public function getAdvanceSearchOptions(): array
    // {
    //     return [
    //         ['id' => 'section_name', 'label' => trans(self::SECTIONNAME_TRANS), 'type' => 'string'],
    //         ['id' => 'parentSectionName', 'label' => trans(self::PARENTSECTIONNAME_TRANS), 'type' => 'string'],
    //         ['id' => 'status', 'label' => trans(self::STATUS_TRANS), 'type' => 'string'],
    //     ];
    // }

    // public function filters(): array
    // {
    //     return [
    //         'status' => [
    //             'title' => __('admin.common.status'),
    //             'data' => [
    //                 '1' => __(self::ACTIVE_TRANS), // true = 1 = Active
    //                 '0' => __('section::section.in_active'), // false = 0 = Inactive
    //             ],
    //             'width' => '250',
    //             'type' => 'string',
    //             'auto_filter_val' => __(self::ACTIVE_TRANS),
    //         ],
    //     ];
    // }

    public function getBulkAction(): array
    {
        return [
            'Active' => [
                'class' => 'success',
                'title' => __(self::ACTIVE_TRANS),
                'url' => route('admin.sections.bulkAction'),
                'module' => 'Section',
                'type' => 'active'
            ],
            'Inactive' => [
                'class' => 'warning',
                'title' => __('section::section.in_active'),
                'url' => route('admin.sections.bulkAction'),
                'module' => 'Section',
                'type' => 'inactive'
            ],
            'Delete' => [
                'class' => 'danger',
                'title' => __('section::section.delete'),
                'url' => route('admin.sections.bulkDelete'),
                'module' => 'Section',
                'type' => 'delete'
            ],
        ];
    }

    public function getColumnCategory(array $data): string
    {
        return $data['section_name'];
    }

    public function getColumnParentSectionName(array $data): string
    {
        return $data['parentSectionName'] ?? '-';
    }

    public function getDownloadColumnStatus(array $data): string
    {
        return (bool) $data['status'] ? 'Active' : 'Inactive';
    }

    public function getColumnStatus(array $data): string
    {
        $isActive = (bool) $data['status'];
        return '<input type="checkbox" class="my-switch change-status" data-id = "' . $data['id'] .
        '" id="switchCheckbox' . $data['id'] . '" ' . ($isActive ? 'checked' : '') . '
        data-bs-toggle="tooltip" title="' . ($isActive ? 'Active' : 'Inactive') . ' Sections">';
    }

    public function getColumnAction(array $data): string
    {
        $return = '';
        $user = Auth::user();

        if ($user && $user->hasPermission('update-sections')) {
            $return = '<a class="me-3" href="' . route('admin.sections.edit', encrypt($data['id'])) . '">
            <span class="inic inic-edit fs-18" data-bs-toggle="tooltip" title="Edit"></span></a>';
        }

        if ($user && $user->hasPermission('delete-sections')) {
            $return .= '<a class="delete-grid-row" href="javascript:void(0)"
            data-grid="inic_grid_' . $this->uniqueID . '"
            data-url="' . route('admin.sections.destroy', encrypt($data['id'])) . '">
            <span class="inic inic-bin fs-18" data-bs-toggle="tooltip" title="Delete"></span></a>';
        }

        return $return;
    }

    public function getColumnCheckbox(array $data): string
    {
        return '<input type="checkbox" value="' . $data['id'] . '" class="check_approval" />';
    }
}
