<?php

namespace Modules\Section\app\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Modules\Section\app\DataGrids\SectionDataGrid;
use Modules\Section\app\Http\Requests\SectionRequest;
use Modules\Section\app\Models\Section;
use Modules\Section\app\Repositories\SectionRepository;
use Nwidart\Modules\Routing\Controller;

class SectionController extends Controller
{
    private const SECTION_INDEX_ROUTE = 'admin.sections.index';

    public function __construct(
        protected readonly SectionRepository $sectionRepository
    ) {
        $this->middleware('permission:create-sections')->only(['create', 'store']);
        $this->middleware('permission:update-sections')->only(['edit', 'update', 'changeStatus']);
        $this->middleware('permission:read-sections|create-sections|update-sections|delete-sections')
            ->only('index');
        $this->middleware('permission:delete-sections')->only('destroy');
    }

    public function index(): View|RedirectResponse
    {
        try {
            $dataGridHtml = SectionDataGrid::getHTML();
            return view('section::index', compact('dataGridHtml'));
        } catch (\Throwable $e) {
            return redirect()
                ->back()
                ->with([
                    'type' => 'error',
                    'message' => __($e->getMessage())
                ]);
        }
    }

    public function create(): View|JsonResponse
    {
        try {
            $parentSections = $this->sectionRepository->getParentSections();

            return view('section::create', [
                'parentSections' => $parentSections,
                'method' => 'POST',
                'action' => route('admin.sections.store')
            ]);
        } catch (\Throwable $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage())
            ]);
        }
    }

    public function store(SectionRequest $request): RedirectResponse
    {
        try {
            $data = $request->validated();
            $data['parent_id'] = $data['parent_id'] == 0 ? null : $data['parent_id'];

            $this->sectionRepository->create($data);

            return redirect()
                ->route(self::SECTION_INDEX_ROUTE)
                ->with([
                    'type' => 'success',
                    'message' => __('section::section.section_add_successfully')
                ]);
        } catch (\Throwable $e) {
            return redirect()
                ->back()
                ->with([
                    'type' => 'error',
                    'message' => $e->getMessage()
                ]);
        }
    }

    public function show(Section $section): View|JsonResponse
    {
        try {
            return view('section::show', ['model' => $section]);
        } catch (\Throwable $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage())
            ]);
        }
    }

    public function edit(Section $section): View|RedirectResponse
    {
        try {
            $parentSections = $this->sectionRepository->getParentSections($section->id);

            return view('section::edit', [
                'section' => $section,
                'parentSections' => $parentSections,
                'method' => 'PUT',
                'action' => route('admin.sections.update', encrypt($section->id))
            ]);
        } catch (\Throwable $e) {
            return redirect()
                ->back()
                ->with([
                    'status' => 'error',
                    'message' => __($e->getMessage())
                ]);
        }
    }

    public function update(SectionRequest $request, Section $section): RedirectResponse
    {
        try {
            $data = $request->validated();
            $data['parent_id'] = $data['parent_id'] == 0 ? null : $data['parent_id'];

            $this->sectionRepository->update($data, $section->id);

            return redirect()
                ->route(self::SECTION_INDEX_ROUTE)
                ->with([
                    'type' => 'success',
                    'message' => __('section::section.section_update_successfully')
                ]);
        } catch (\Throwable $e) {
            return redirect()
                ->back()
                ->with([
                    'type' => 'error',
                    'message' => $e->getMessage()
                ]);
        }
    }

    public function destroy(Section $section): JsonResponse
    {
        try {
            $this->sectionRepository->delete($section->id);

            return response()->json([
                'status' => 'success',
                'message' => __('section::section.section_delete_successfully')
            ]);
        } catch (\Throwable $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage())
            ]);
        }
    }

    public function changeStatus(Request $request): JsonResponse
    {
        if (!$request->ajax()) {
            return response()->json([
                'status' => 'error',
                'message' => __('section::section.invalid_request')
            ]);
        }

        $id = (int) $request->input('id');
        if ($id <= 0) {
            return response()->json([
                'status' => 'error',
                'message' => __('section::section.invalid_id')
            ]);
        }

        try {
            $model = $this->sectionRepository->show($id);

            $data = match ($model['status']) {
                false => [ // if inactive (false)
                    'status' => true, // set to active
                    'message' => __('section::section.status_updated', [
                        'status' => __('section::section.activated')
                    ])
                ],
                default => [ // if active (true)
                    'status' => false, // set to inactive
                    'message' => __('section::section.status_updated', [
                        'status' => __('section::section.de_activated')
                    ])
                ]
            };

            $this->sectionRepository->update(['status' => $data['status']], $id);

            return response()->json([
                'status' => 'success',
                'message' => $data['message']
            ]);
        } catch (\Throwable $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage())
            ]);
        }
    }

    public function bulkAction(Request $request): JsonResponse
    {
        try {
            $result = $this->sectionRepository->bulkAction(
                (array) $request->input('id'),
                $request->input('status'),
                $request->input('type')
            );

            return response()->json([
                'status' => $result['type'],
                'message' => $result['message']
            ]);
        } catch (\Throwable $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage())
            ]);
        }
    }
}
