<form method="POST" action="{{ $action }}">
    @csrf
    @if (isset($model))
        @method('PUT')
    @endif

    <!-- Language Switcher -->
    @include('newscategory::partials.language-switcher')

    <div class="row">
        <!-- Card for translatable fields -->
        <div class="col-md-12 mb-4">
            <div class="card border-primary" style="border-width: 2px;">
                <div class="card-header bg-primary bg-opacity-10">
                    <h5 class="mb-0"><i class="fa fa-language"></i> {{ __('admin.common.translatable_fields') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Name (translatable) -->
                        <div class="col-md-12 mb-3">
                            <label for="name_en" class="form-label">{{ __('newscategory::newscategory.category') }}</label>
                            <div class="form-group {{ $errors->has('name.en') ? 'has-error' : '' }} translatable-field" data-language="en">
                                <input type="text" name="name[en]" id="name_en" class="form-control"
                                    value="{{ old('name.en', isset($model) ? $model->getTranslation('name', 'en', false) : '') }}"
                                    placeholder="{{ __('newscategory::newscategory.name_placeholder') }}">
                                @if ($errors->has('name.en'))
                                    <span class="help-block">{{ $errors->first('name.en') }}</span>
                                @endif
                            </div>
                            <div class="form-group {{ $errors->has('name.ar') ? 'has-error' : '' }} translatable-field" data-language="ar" style="display: none;">
                                <input type="text" name="name[ar]" id="name_ar" class="form-control" dir="rtl"
                                    value="{{ old('name.ar', isset($model) ? $model->getTranslation('name', 'ar', false) : '') }}"
                                    placeholder="{{ __('newscategory::newscategory.name_placeholder') }}">
                                @if ($errors->has('name.ar'))
                                    <span class="help-block">{{ $errors->first('name.ar') }}</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card for non-translatable fields -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fa fa-cog"></i> {{ __('admin.common.general_settings') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Status -->
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">{{ __('newscategory::newscategory.status') }}</label>
                            <select name="status" id="status" class="form-select">
                                <option value="1" {{ old('status', $model->status ?? true) == 1 ? 'selected' : '' }}>
                                    {{ __('newscategory::newscategory.active') }}
                                </option>
                                <option value="0" {{ old('status', $model->status ?? '') == 0 ? 'selected' : '' }}>
                                    {{ __('newscategory::newscategory.in_active') }}
                                </option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form buttons -->
        <div class="col-md-12">
            <div class="d-flex align-items-center justify-content-end gap-3">
                <button type="submit" class="btn btn-primary">{{ __('admin.common.submit') }}</button>
                <a href="{{ route('admin.news-category.index') }}" class="btn btn-secondary">{{ __('admin.common.cancel') }}</a>
            </div>
        </div>
    </div>
</form>

@push('scripts')
    @php
        $validator = JsValidator::formRequest('Modules\NewsCategory\app\Http\Requests\NewsCategoryRequest');
    @endphp
    {!! $validator !!}
@endpush
