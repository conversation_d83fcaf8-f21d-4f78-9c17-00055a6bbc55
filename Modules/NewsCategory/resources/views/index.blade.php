@extends('admin.layouts.page')
@section('title', __('newscategory::newscategory.news_category'))

@section('breadcrumb')
    <ol class="breadcrumb-item active">{{ __('newscategory::newscategory.news_category') }}</ol>
@endsection

@permission('create-news-category')
    @section('action_buttons')
    <a href="{{ route('admin.news-category.create') }}" class="btn btn-primary">
        <em class="inic inic-add"></em> {{ __('newscategory::newscategory.add_news_category') }}
    </a>
    @endsection
@endpermission

@section('content')
    {!! $dataGridHtml !!}
@endsection

@push('scripts')
    <script type="module">
        $(document).on('change','.change-status', function(){
            var id =  $(this).attr('data-id');
            $.ajax({
                url: "{{ route('admin.news-category.change_status')}}",
                type: "post",
                data: {"id": id},
                datatype: 'json',
                success: function (response) {
                   toastr.success(response.message);
                   window.inic_grid_news_category.refresh();
                },
                error: function (response) {
                   toastr.error(response.message);
                }
            });
        });

        let csrfToken = $('meta[name="csrf-token"]').attr('content');
        $(document).on('click', '.delete-grid-row', function (event) {
            event.preventDefault();
            let url = $(this).attr('data-url');
            SwalConfirm(url, function (url) {
                $.ajax({
                    url: url,
                    type: 'delete',
                    datatype: 'json',
                    headers: {
                        'X-CSRF-TOKEN': csrfToken
                    },
                    success: function (response) {
                        toastr.success(response.message);
                        window.inic_grid_news_category.refresh();
                    },
                    error: function (response) {
                        toastr.error(response.message);
                    }
                });
            })
        })

        $(document).on('click', '.grid-bulk-action', function(event) {
            event.preventDefault();
            var id = [];var error = 0;
            $('.check_approval').each(function(index){
                if($(this).prop('checked') == true){
                    id.push($(this).val());
                    error = 1;
                }
            });

            var url= "{{ route('admin.news-category.bulkAction') }}";
            var status=$(this).data('status');
            var type=$(this).data('action-type');

            if(error == 0){
                toastr.error("{{ __('newscategory::newscategory.please_select_one_news_category') }}");
            }else if(error == 1){
                SwalConfirm(url, function(url) {
                    $.ajax({
                        url: url,
                        type: 'post',
                        data: {id: id,status:status,type:type},
                        success: function(response) {
                            if(response.status == 'success'){
                                toastr.success(response.message);
                            }else{
                                toastr.error(response.message);
                            }
                            window.inic_grid_news_category.refresh();
                        },
                        error: function(response) {
                            toastr.error(response.message);
                        }

                    });
                });
            }
        });
    </script>
@endpush
