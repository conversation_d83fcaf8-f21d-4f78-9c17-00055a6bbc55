<?php

namespace Modules\NewsCategory\app\DataGrids;

use Indianic\LaravelDataGrid\LaravelDataGrid;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Modules\NewsCategory\app\Models\NewsCategory;

class NewsCategoryDataGrid extends LaravelDataGrid
{
    public $guard = 'admin';
    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'news_category';

    /**
     * Define how many rows you want to display on a page
     * @var int $rowPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define row per page dropdown options
     * @var array $recordsPerPageOptions
     */
    public $recordsPerPageOptions;

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy = 'id';

    /**
     * Define default soring direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection = 'DESC';

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'news_category';

    protected const CATEGORY = 'newscategory::newscategory.category';
    protected const STATUS = 'newscategory::newscategory.status';
    protected const CREATED_AT = 'newscategory::newscategory.created_at';
    protected const COUNT = 'newscategory::newscategory.count';
    protected const ACTION = 'newscategory::newscategory.action';

    public function resource()
    {
        return NewsCategory::select(
            'news_category.id',
            'news_category.status',
            'news_category.created_at',
            DB::raw("JSON_EXTRACT(news_category.name, '$.en') as name"),
            DB::raw("count(news.id) as newsCount")
        )->leftJoin('news', 'news.category_id', 'news_category.id')->groupBy('news_category.id');
    }

    public function columns(): array
    {
        return [
            'checkbox'  => '<input class="select_all checkbox" type="checkbox" />',
            'name'      => __(self::CATEGORY),
            'status'    => __(self::STATUS),
            'newsCount'     => __(self::COUNT),
            'created_at'=> __(self::CREATED_AT),
            'action'    => __(self::ACTION)
        ];
    }

    public function mapDBColumns(): array
    {
        return [
            'name' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(news_category.name, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'status' => 'news_category.status',
            'created_at' => 'news_category.created_at'
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'name',
            'created_at',
            'newsCount',
            'status',
        ];
    }

    // public function downloadableColumns(): array
    // {
    //     return [
    //         'name'  => __(self::CATEGORY),
    //         'created_at'         => __(self::CREATED_AT),
    //         'status'             => __(self::STATUS),
    //         'newsCount'          => __(self::COUNT),
    //     ];
    // }

    public function getDownloadColumnName(array $data): string
    {
        $name = $data['name'] ?? '';
        $name = trim($name, '"');
        return $name;
    }

    public function gloablSearchableColumns(): array
    {
        return [
            'name'  => 'string',
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'name'  => 'string',
        ];
    }

    // public function getAdvanceSearchOptions(): array
    // {
    //     return [
    //         ['id' => 'name', 'label' => __(self::CATEGORY), 'type' => 'string'],
    //         ['id' => 'status', 'label' => __(self::STATUS), 'type' => 'string'],
    //     ];
    // }

    public function getBulkAction(): array
    {
        return [
            'Active' => [
                'class' => 'success',
                'title' => __('newscategory::newscategory.active'),
                'module' => 'NewsCategory',
                'type' => 'active'
            ],
            'Inactive' => [
                'class' => 'warning',
                'title' => __('newscategory::newscategory.in_active'),
                'module' => 'NewsCategory',
                'type' => 'inactive'
            ],
            'Delete' => [
                'class' => 'danger',
                'title' => __('newscategory::newscategory.delete'),
                'module' => 'NewsCategory',
                'type' => 'delete'
            ],
        ];
    }

    public function getColumnName(array $data): string
    {
        $name = $data['name'] ?? '';
        $name = trim($name, '"');
        return '<span class="badge bg-primary">' . htmlspecialchars($name) . '</span>';
    }

    public function getColumnCreatedAt(array $data): string
    {
        return adminDateTimeFormatShow($data['created_at']);
    }

    public function getDownloadColumnStatus(array $data): string
    {
        return (bool) $data['status'] ? 'Active' : 'Inactive';
    }

    public function getColumnStatus(array $data): string
    {
        $isActive = (bool) $data['status'];
        return '<input type="checkbox" class="my-switch change-status"
        data-id = "' . $data['id'] . '"
        id="switchCheckbox' . $data['id'] . '" ' . ($isActive ? 'checked' : '') . '
        data-bs-toggle="tooltip" title="' . ($isActive ? 'Active' : 'Inactive') . ' News Category">';
    }

    public function getColumnAction(array $data): string
    {
        $return = '';
        $user = Auth::user();

        if ($user->hasPermission('create-news')) {
            $return .= '<a class="me-3" href="' . route('admin.news.create', ['id' => encrypt($data['id'])]) . '">
            <span class="bx bx-plus-circle fs-18"  data-bs-toggle="tooltip" title="Add News"></span></a>';
        }

        if ($user->hasPermission('update-news-category')) {
            $return .= '<a class="me-3" href="' . route('admin.news-category.edit', encrypt($data['id'])) . '">
            <span class="inic inic-edit fs-18" data-bs-toggle="tooltip" title="Edit"></span></a>';
        }

        if ($user->hasPermission('delete-news-category')) {
            $return .= '<a class="delete-grid-row" href="javascript:void(0)" data-grid="inic_grid_' . $this->uniqueID . '"
            data-url="' . route('admin.news-category.destroy', encrypt($data['id'])) . '">
            <span class="inic inic-bin fs-18" data-bs-toggle="tooltip" title="Delete"></span></a>';
        }

        return $return;
    }

   public function getColumnCheckbox(array $data): string
   {
        return '<input type="checkbox" value="' . $data['id'] . '" class="check_approval" />';
   }
}
