<?php

namespace Modules\Page\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Page\app\Models\Page;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\Modules\Page\app\Models\Page>
 */
class PageFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Page::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'parent_id' => null,
            'slug' => $this->faker->unique()->slug,
            'title' => [
                'en' => $this->faker->sentence(3),
                'ar' => 'صفحة ' . $this->faker->word
            ],
            'sub_title' => [
                'en' => $this->faker->sentence(4),
                'ar' => 'عنوان فرعي ' . $this->faker->word
            ],
            'body' => [
                'en' => $this->faker->paragraphs(3, true),
                'ar' => 'محتوى الصفحة ' . $this->faker->paragraph
            ],
            'meta_keywords' => $this->faker->words(5, true),
            'meta_description' => $this->faker->sentence(10),
            'status' => true,
        ];
    }

    /**
     * Indicate that the page is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => true,
        ]);
    }

    /**
     * Indicate that the page is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => false,
        ]);
    }

    /**
     * Indicate that the page is a child page.
     */
    public function child(int $parentId): static
    {
        return $this->state(fn (array $attributes) => [
            'parent_id' => $parentId,
        ]);
    }

    /**
     * Create a page with minimal English data only.
     */
    public function englishOnly(): static
    {
        return $this->state(fn (array $attributes) => [
            'title' => [
                'en' => $this->faker->sentence(3),
            ],
            'sub_title' => [
                'en' => $this->faker->sentence(4),
            ],
            'body' => [
                'en' => $this->faker->paragraphs(2, true),
            ],
        ]);
    }

    /**
     * Create a page with specific slug.
     */
    public function withSlug(string $slug): static
    {
        return $this->state(fn (array $attributes) => [
            'slug' => $slug,
        ]);
    }

    /**
     * Create a page without optional fields.
     */
    public function minimal(): static
    {
        return $this->state(fn (array $attributes) => [
            'sub_title' => null,
            'meta_keywords' => null,
            'meta_description' => null,
        ]);
    }
}
