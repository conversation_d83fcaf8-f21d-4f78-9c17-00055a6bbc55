<form method="POST" action="{{ $action }}" enctype="multipart/form-data">
    @csrf
    @if (isset($page->id))
        @method('PUT')
    @endif

    <!-- Language Switcher -->
    @include('page::partials.language-switcher')

    <div class="row">
        <div class="col-md-6 mb-3">
            <label for="parent_id" class="form-label">{{ __('page::page.parent') }}</label>
            <select name="parent_id" id="parent_id" class="form-select">
                {{-- <option value="">{{ __('page::page.parent_placeholder') }}</option> --}}
                @foreach ($parentPages as $key => $parentPage)
                    <option value="{{ $key }}"
                        {{ old('parent_id', $page->parent_id ?? '') == $key ? 'selected' : '' }}>
                        {{ $parentPage }}</option>
                @endforeach
            </select>
        </div>

        <!-- Card for translatable fields -->
        <div class="col-md-12 mb-4">
            <div class="card border-primary" style="border-width: 2px;">
                <div class="card-header bg-primary bg-opacity-10">
                    <h5 class="mb-0"><i class="fa fa-language"></i> {{ __('admin.common.translatable_fields') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Title (translatable) -->
                        <div class="col-md-6 mb-3">
                            <label for="title_en" class="form-label">{{ __('page::page.title') }}</label>
                            <div class="form-group {{ isset($errors) && $errors->has('title.en') ? 'has-error' : '' }} translatable-field" data-language="en">
                                <input type="text" name="title[en]" id="title_en" class="form-control"
                                    value="{{ old('title.en', isset($page) ? $page->getTranslation('title', 'en', false) : '') }}"
                                    placeholder="{{ __('page::page.title_placeholder') }}">
                                @if (isset($errors) && $errors->has('title.en'))
                                    <span class="help-block">{{ isset($errors) ? $errors->first('title.en') : '' }}</span>
                                @endif
                            </div>
                            <div class="form-group {{ isset($errors) && $errors->has('title.ar') ? 'has-error' : '' }} translatable-field" data-language="ar" style="display: none;">
                                <input type="text" name="title[ar]" id="title_ar" class="form-control" dir="rtl"
                                    value="{{ old('title.ar', isset($page) ? $page->getTranslation('title', 'ar', false) : '') }}"
                                    placeholder="{{ __('page::page.title_placeholder') }}">
                                @if (isset($errors) && $errors->has('title.ar'))
                                    <span class="help-block">{{ isset($errors) ? $errors->first('title.ar') : '' }}</span>
                                @endif
                            </div>
                        </div>

                        <!-- Sub-title (translatable) -->
                        <div class="col-md-6 mb-3">
                            <label for="sub_title_en" class="form-label">{{ __('page::page.sub_title') }}</label>
                            <div class="form-group {{ isset($errors) && $errors->has('sub_title.en') ? 'has-error' : '' }} translatable-field" data-language="en">
                                <input type="text" name="sub_title[en]" id="sub_title_en" class="form-control"
                                    value="{{ old('sub_title.en', isset($page) ? $page->getTranslation('sub_title', 'en', false) : '') }}"
                                    placeholder="{{ __('page::page.sub_title_placeholder') }}">
                                @if (isset($errors) && $errors->has('sub_title.en'))
                                    <span class="help-block">{{ isset($errors) ? $errors->first('sub_title.en') : '' }}</span>
                                @endif
                            </div>
                            <div class="form-group {{ isset($errors) && $errors->has('sub_title.ar') ? 'has-error' : '' }} translatable-field" data-language="ar" style="display: none;">
                                <input type="text" name="sub_title[ar]" id="sub_title_ar" class="form-control" dir="rtl"
                                    value="{{ old('sub_title.ar', isset($page) ? $page->getTranslation('sub_title', 'ar', false) : '') }}"
                                    placeholder="{{ __('page::page.sub_title_placeholder') }}">
                                @if (isset($errors) && $errors->has('sub_title.ar'))
                                    <span class="help-block">{{ isset($errors) ? $errors->first('sub_title.ar') : '' }}</span>
                                @endif
                            </div>
                        </div>

                        <!-- Body (translatable) -->
                        <div class="col-md-12 mb-3 body">
                            <label for="body" class="form-label">{{ __('page::page.body') }}</label>
                            <div class="translatable-field" data-language="en">
                                <textarea name="body[en]" id="body_en" class="form-control ck-editor">{{ old('body.en', isset($page) ? $page->getTranslation('body', 'en', false) : '') }}</textarea>
                            </div>
                            <div class="translatable-field" data-language="ar" style="display: none;">
                                <textarea name="body[ar]" id="body_ar" class="form-control ck-editor" dir="rtl">{{ old('body.ar', isset($page) ? $page->getTranslation('body', 'ar', false) : '') }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card for non-translatable fields -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fa fa-cog"></i> {{ __('admin.common.general_settings') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">

                        <div class="col-md-6 mb-3">
                            <label for="slug" class="form-label">{{ __('page::page.slug') }}</label>
                            <input type="text" name="slug" id="slug" class="form-control"
                                value="{{ old('slug', $page->slug ?? '') }}"
                                placeholder="{{ __('page::page.slug_placeholder') }}">
                        </div>

                        <!-- Status -->
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">{{ __('page::page.status') }}</label>
                            <select name="status" id="status" class="form-select">
                                <option value="1" {{ old('status', $page->status ?? true) == 1 ? 'selected' : '' }}>
                                    {{ __('page::page.active') }}
                                </option>
                                <option value="0" {{ old('status', $page->status ?? '') == 0 ? 'selected' : '' }}>
                                    {{ __('page::page.in_active') }}
                                </option>
                            </select>
                        </div>

                        <!-- Meta Keywords -->
                        <div class="col-md-6 mb-3">
                            <label for="meta_keywords" class="form-label">{{ __('page::page.meta_keywords') }}</label>
                            <input type="text" name="meta_keywords" id="meta_keywords" class="form-control"
                                value="{{ old('meta_keywords', $page->meta_keywords ?? '') }}"
                                placeholder="{{ __('page::page.meta_keywords_placeholder') }}">
                        </div>

                        <!-- Meta Description -->
                        <div class="col-md-6 mb-3">
                            <label for="meta_description" class="form-label">{{ __('page::page.meta_description') }}</label>
                            <textarea name="meta_description" id="meta_description" class="form-control">{{ old('meta_description', $page->meta_description ?? '') }}</textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Page Sections -->
        <div class="col-md-12 mb-3">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fa fa-th-list"></i> {{ __('page::page.sections') }}</h5>
                </div>
                <div class="card-body">
                    @if(isset($errors) && $errors->has('sections'))
                        <div class="text-danger mb-2">{{ $errors->first('sections') }}</div>
                    @endif
                    <div id="sections-container" class="sections-container mb-3">
                        @php
                            // Use old('sections') if available (after validation error), else fallback to $pageSections
                            $sectionsInput = old('sections', isset($pageSections) ? $pageSections : []);
                        @endphp
                        @forelse($sectionsInput as $index => $sectionId)
                            <div class="section-item card mb-2" data-order="{{ $index }}">
                                <div class="card-body p-2">
                                    <div class="row align-items-center">
                                        <div class="col-auto section-drag-handle">
                                            <em class="inic inic-drag-handle"></em>
                                        </div>
                                        <div class="col">
                                            <select name="sections[{{ $index }}]" class="form-select section-select {{ isset($errors) && $errors->has('sections.' . $index) ? 'is-invalid' : '' }}">
                                                <option value="">{{ __('page::page.select_section') }}</option>
                                                @foreach($sections as $id => $name)
                                                    <option value="{{ $id }}" {{ old('sections.' . $index, $sectionId) == $id ? 'selected' : '' }}>{{ $name }}</option>
                                                @endforeach
                                            </select>
                                            @if(isset($errors) && $errors->has('sections.' . $index))
                                                <div class="invalid-feedback">{{ $errors->first('sections.' . $index) }}</div>
                                            @endif
                                        </div>
                                        <div class="col-auto">
                                            <button type="button" class="btn btn-danger btn-sm remove-section">
                                                <em class="inic inic-bin"></em> {{ __('page::page.remove_section') }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @empty
                            <div class="section-item card mb-2" data-order="0">
                                <div class="card-body p-2">
                                    <div class="row align-items-center">
                                        <div class="col-auto section-drag-handle">
                                            <em class="inic inic-drag-handle"></em>
                                        </div>
                                        <div class="col">
                                            <select name="sections[0]" class="form-select section-select {{ isset($errors) && $errors->has('sections.0') ? 'is-invalid' : '' }}">
                                                <option value="">{{ __('page::page.select_section') }}</option>
                                                @foreach($sections as $id => $name)
                                                    <option value="{{ $id }}" {{ old('sections.0') == $id ? 'selected' : '' }}>{{ $name }}</option>
                                                @endforeach
                                            </select>
                                            @if(isset($errors) && $errors->has('sections.0'))
                                                <div class="invalid-feedback">{{ $errors->first('sections.0') }}</div>
                                            @endif
                                        </div>
                                        <div class="col-auto">
                                            <button type="button" class="btn btn-danger btn-sm remove-section">
                                                <em class="inic inic-bin"></em> {{ __('page::page.remove_section') }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforelse
                    </div>
                    <div class="text-end">
                        <button type="button" id="add-section" class="btn btn-primary btn-sm">
                            <em class="inic inic-plus"></em> {{ __('page::page.add_section') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form buttons -->
        <div class="col-md-12">
            <div class="d-flex align-items-center justify-content-end gap-3">
                <button type="submit" class="btn btn-primary">{{ __('admin.common.submit') }}</button>
                <a href="{{ route('admin.pages.index') }}"
                    class="btn btn-secondary">{{ __('admin.common.cancel') }}</a>
            </div>
        </div>
    </div>
</form>

@include('plugins.ckeditor')
@push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    @php
        $validator = JsValidator::formRequest('Modules\Page\app\Http\Requests\PageRequest');
        $validator->ignore('');
    @endphp
    {!! $validator !!}

    <script type="module">
        document.addEventListener('DOMContentLoaded', () => {

            // Initialize CKEditors for both languages
            initializeCKEditor('body_en', options);

            // Set up Arabic CKEditor with RTL direction when needed
            const arOptions = Object.assign({}, options, {
                contentsLangDirection: 'rtl'
            });
            initializeCKEditor('body_ar', arOptions);

            // Hide the Arabic editor initially
            if (typeof CKEDITOR !== 'undefined') {
                if (CKEDITOR.instances.body_ar && CKEDITOR.instances.body_ar.container) {
                    CKEDITOR.instances.body_ar.container.setStyle('display', 'none');
                }
            }

            // Section management functionality
            const sectionsContainer = document.getElementById('sections-container');
            const addSectionBtn = document.getElementById('add-section');

            // Initialize sortable for drag-and-drop
            if (typeof Sortable !== 'undefined') {
                new Sortable(sectionsContainer, {
                    handle: '.section-drag-handle',
                    animation: 150,
                    ghostClass: 'section-item-ghost',
                    onEnd: () => updateSectionOrders(),
                });
            } else {
                console.warn('Sortable library not found. Drag and drop functionality will not work.');
            }

            // Add new section
            addSectionBtn.addEventListener('click', () => {
                const sectionItems = sectionsContainer.querySelectorAll('.section-item');
                const newOrder = sectionItems.length;

                const sectionHtml = `
                    <div class="section-item card mb-2" data-order="${newOrder}">
                        <div class="card-body p-2">
                            <div class="row align-items-center">
                                <div class="col-auto section-drag-handle">
                                    <em class="inic inic-drag-handle"></em>
                                </div>
                                <div class="col">
                                    <select name="sections[${newOrder}]" class="form-select section-select">
                                        <option value="">{{ __('page::page.select_section') }}</option>
                                        @foreach($sections as $id => $name)
                                            <option value="{{ $id }}">{{ $name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-auto">
                                    <button type="button" class="btn btn-danger btn-sm remove-section">
                                        <em class="inic inic-bin"></em> {{ __('page::page.remove_section') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                sectionsContainer.insertAdjacentHTML('beforeend', sectionHtml);

                // Add event listener for remove button
                const newSection = sectionsContainer.lastElementChild;
                const removeBtn = newSection.querySelector('.remove-section');
                removeBtn.addEventListener('click', () => removeSection(removeBtn));

                updateSectionOrders();
            });

            // Add event listeners to existing remove buttons
            document.querySelectorAll('.remove-section').forEach(button => {
                button.addEventListener('click', () => removeSection(button));
            });

            // Remove section function
            function removeSection(button) {
                const sectionItem = button.closest('.section-item');
                const sectionItems = sectionsContainer.querySelectorAll('.section-item');
                if (sectionItems.length > 1) {
                    sectionItem.remove();
                    updateSectionOrders();
                } else {
                    const select = sectionItem.querySelector('select');
                    select.value = '';
                }
            }

            // Update section order and input names
            function updateSectionOrders() {
                const sectionItems = sectionsContainer.querySelectorAll('.section-item');
                sectionItems.forEach((item, index) => {
                    item.setAttribute('data-order', index);
                    const select = item.querySelector('select');
                    select.name = `sections[${index}]`;
                });
            }

            // Auto-generate slug from title
            $('#title_en').on('keyup change', function() {
                var title = $(this).val();
                var slug = generateSlug(title);
                $('#slug').val(slug);
            });

            // Function to generate slug
            function generateSlug(text) {
                if(!text) return '';

                // Convert to lowercase
                var slug = text.toLowerCase();

                // Replace spaces with hyphens
                slug = slug.replace(/\s+/g, '-');

                // Remove special characters
                slug = slug.replace(/[^\w\-]+/g, '');

                // Remove duplicate hyphens
                slug = slug.replace(/\-\-+/g, '-');

                // Remove leading and trailing hyphens
                slug = slug.replace(/^-+/, '').replace(/-+$/, '');

                return slug;
            }
        });
    </script>
@endpush
