<?php

namespace Modules\Page\Tests\Unit;

use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Validator;
use Modules\Page\app\Http\Requests\PageRequest;
use Modules\Page\app\Models\Page;
use Modules\Section\app\Models\Section;

class PageRequestTest extends TestCase
{
    use RefreshDatabase;

    private PageRequest $request;

    protected function setUp(): void
    {
        parent::setUp();
        $this->request = new PageRequest();
    }

    #[Test]
    public function it_authorizes_requests(): void
    {
        $this->assertTrue($this->request->authorize());
    }

    #[Test]
    public function it_validates_required_fields(): void
    {
        $data = [];
        $validator = Validator::make($data, $this->request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('parent_id', $validator->errors()->toArray());
        $this->assertArrayHasKey('title', $validator->errors()->toArray());
        $this->assertArrayHasKey('body', $validator->errors()->toArray());
        $this->assertArrayHasKey('status', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_title_structure(): void
    {
        $data = [
            'title' => 'not an array',
            'parent_id' => 0,
            'body' => ['en' => 'Test content'],
            'status' => true
        ];

        $validator = Validator::make($data, $this->request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('title', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_title_en_is_required(): void
    {
        $data = [
            'title' => ['ar' => 'عنوان عربي'],
            'parent_id' => 0,
            'body' => ['en' => 'Test content'],
            'status' => true
        ];

        $validator = Validator::make($data, $this->request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('title.en', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_title_max_length(): void
    {
        $data = [
            'title' => ['en' => str_repeat('a', 151)], // Exceeds max:150
            'parent_id' => 0,
            'body' => ['en' => 'Test content'],
            'status' => true
        ];

        $validator = Validator::make($data, $this->request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('title.en', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_body_structure_and_requirements(): void
    {
        $data = [
            'title' => ['en' => 'Test Title'],
            'parent_id' => 0,
            'body' => 'not an array', // Should be array
            'status' => true
        ];

        $validator = Validator::make($data, $this->request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('body', $validator->errors()->toArray());

        // Test missing body.en
        $data['body'] = ['ar' => 'محتوى عربي'];
        $validator = Validator::make($data, $this->request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('body.en', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_sub_title_structure(): void
    {
        $data = [
            'title' => ['en' => 'Test Title'],
            'parent_id' => 0,
            'body' => ['en' => 'Test content'],
            'sub_title' => 'not an array', // Should be array if provided
            'status' => true
        ];

        $validator = Validator::make($data, $this->request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('sub_title', $validator->errors()->toArray());
    }

    #[Test]
    public function it_validates_status_as_boolean(): void
    {
        $data = [
            'title' => ['en' => 'Test Title'],
            'parent_id' => 0,
            'body' => ['en' => 'Test content'],
            'status' => 'not a boolean'
        ];

        $validator = Validator::make($data, $this->request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('status', $validator->errors()->toArray());
    }

    #[Test]
    public function it_passes_validation_with_valid_data(): void
    {
        $section = Section::factory()->create();

        $data = [
            'parent_id' => 0,
            'slug' => 'test-page',
            'title' => ['en' => 'Test Title'],
            'sub_title' => ['en' => 'Test Subtitle'],
            'body' => ['en' => 'Test content'],
            'meta_keywords' => 'test keywords',
            'meta_description' => 'Test description',
            'status' => true,
            'sections' => [$section->id]
        ];

        $validator = Validator::make($data, $this->request->rules());

        $this->assertFalse($validator->fails());
    }

    #[Test]
    public function it_handles_nullable_fields_correctly(): void
    {
        $data = [
            'parent_id' => 0,
            'slug' => 'test-page',
            'title' => ['en' => 'Test Title'],
            'body' => ['en' => 'Test content'],
            'status' => true,
            // Optional fields not provided
            'sub_title' => null,
            'meta_keywords' => null,
            'meta_description' => null,
            'sections' => null
        ];

        $validator = Validator::make($data, $this->request->rules());

        $this->assertFalse($validator->fails());
    }

    #[Test]
    public function it_validates_sections_array_structure(): void
    {
        $section = Section::factory()->create();

        $data = [
            'parent_id' => 0,
            'slug' => 'test-page',
            'title' => ['en' => 'Test Title'],
            'body' => ['en' => 'Test content'],
            'status' => true,
            'sections' => [$section->id, 999] // 999 doesn't exist
        ];

        $validator = Validator::make($data, $this->request->rules());

        $this->assertTrue($validator->fails());
        $this->assertArrayHasKey('sections.1', $validator->errors()->toArray());
    }

    #[Test]
    public function it_has_custom_error_messages(): void
    {
        $messages = $this->request->messages();

        $this->assertIsArray($messages);
        $this->assertArrayHasKey('parent_id.required', $messages);
        $this->assertArrayHasKey('status.required', $messages);
        $this->assertArrayHasKey('status.boolean', $messages);
    }
}
