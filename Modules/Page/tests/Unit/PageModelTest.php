<?php

namespace Modules\Page\Tests\Unit;

use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\Page\app\Models\Page;
use Modules\Section\app\Models\Section;

class PageModelTest extends TestCase
{
    use RefreshDatabase;

    private Page $page;

    protected function setUp(): void
    {
        parent::setUp();
        $this->page = new Page();
    }

    #[Test]
    public function it_has_correct_fillable_attributes(): void
    {
        $expected = [
            'parent_id',
            'title',
            'sub_title',
            'slug',
            'body',
            'meta_keywords',
            'meta_description',
            'status'
        ];

        $this->assertEquals($expected, $this->page->getFillable());
    }

    #[Test]
    public function it_has_correct_guarded_attributes(): void
    {
        $expected = ['id', 'created_at', 'updated_at'];
        $this->assertEquals($expected, $this->page->getGuarded());
    }

    #[Test]
    public function it_casts_status_to_boolean(): void
    {
        $page = Page::factory()->create(['status' => 1]);
        $this->assertTrue($page->status);

        $page = Page::factory()->create(['status' => 0]);
        $this->assertFalse($page->status);
    }

    #[Test]
    public function it_has_translatable_attributes(): void
    {
        $expected = ['title', 'sub_title', 'body'];
        $this->assertEquals($expected, $this->page->translatable);
    }

    #[Test]
    public function it_returns_correct_default_locale(): void
    {
        $this->assertEquals('en', $this->page->getDefaultLocale());
    }

    #[Test]
    public function it_can_check_if_page_is_active(): void
    {
        $activePage = Page::factory()->active()->create();
        $this->assertTrue($activePage->isActive());

        $inactivePage = Page::factory()->inactive()->create();
        $this->assertFalse($inactivePage->isActive());
    }

    #[Test]
    public function it_can_check_if_page_is_inactive(): void
    {
        $activePage = Page::factory()->active()->create();
        $this->assertFalse($activePage->isInactive());

        $inactivePage = Page::factory()->inactive()->create();
        $this->assertTrue($inactivePage->isInactive());
    }

    #[Test]
    public function it_can_set_page_as_active(): void
    {
        $page = Page::factory()->inactive()->create();
        $result = $page->setActive();

        $this->assertInstanceOf(Page::class, $result);
        $this->assertTrue($page->status);
    }

    #[Test]
    public function it_can_set_page_as_inactive(): void
    {
        $page = Page::factory()->active()->create();
        $result = $page->setInactive();

        $this->assertInstanceOf(Page::class, $result);
        $this->assertFalse($page->status);
    }

    #[Test]
    public function it_returns_correct_status_label(): void
    {
        $activePage = Page::factory()->active()->create();
        $this->assertEquals('Active', $activePage->getStatusLabelAttribute());

        $inactivePage = Page::factory()->inactive()->create();
        $this->assertEquals('Inactive', $inactivePage->getStatusLabelAttribute());
    }

    #[Test]
    public function it_can_scope_active_pages(): void
    {
        Page::factory()->active()->count(3)->create();
        Page::factory()->inactive()->count(2)->create();

        $activePages = Page::active()->get();
        $this->assertCount(3, $activePages);

        foreach ($activePages as $page) {
            $this->assertTrue($page->status);
        }
    }

    #[Test]
    public function it_can_scope_inactive_pages(): void
    {
        Page::factory()->active()->count(3)->create();
        Page::factory()->inactive()->count(2)->create();

        $inactivePages = Page::inactive()->get();
        $this->assertCount(2, $inactivePages);

        foreach ($inactivePages as $page) {
            $this->assertFalse($page->status);
        }
    }

    #[Test]
    public function it_has_sub_pages_relationship(): void
    {
        $parentPage = Page::factory()->create();
        $childPage = Page::factory()->child($parentPage->id)->create();

        $subPages = $parentPage->subPages;
        $this->assertCount(1, $subPages);
        $this->assertEquals($childPage->id, $subPages->first()->id);
    }

    #[Test]
    public function it_resolves_route_binding_with_encrypted_value(): void
    {
        $page = Page::factory()->create();
        $encryptedId = encrypt($page->id);

        $resolvedPage = $this->page->resolveRouteBinding($encryptedId);
        $this->assertEquals($page->id, $resolvedPage->id);
    }

    #[Test]
    public function it_handles_translation_methods(): void
    {
        $page = Page::factory()->create([
            'title' => [
                'en' => 'English Title',
                'ar' => 'العنوان العربي'
            ]
        ]);

        $this->assertEquals('English Title', $page->getTranslation('title', 'en'));
        $this->assertEquals('العنوان العربي', $page->getTranslation('title', 'ar'));
    }

    #[Test]
    public function it_uses_correct_table_name(): void
    {
        $this->assertEquals('pages', $this->page->getTable());
    }
}
