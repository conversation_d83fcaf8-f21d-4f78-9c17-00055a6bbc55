<?php

namespace Modules\Page\Tests\Unit;

use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\Page\app\Models\Page;
use Modules\Page\app\Models\PageSection;
use Modules\Section\app\Models\Section;
use Modules\Page\app\Repositories\PageRepository;

class PageRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private PageRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = new PageRepository(new Page());
    }

    #[Test]
    public function it_can_create_page(): void
    {
        $pageData = [
            'slug' => 'test-page',
            'title' => ['en' => 'Test Page'],
            'body' => ['en' => 'Test Content'],
            'status' => true
        ];

        $page = $this->repository->create($pageData);

        $this->assertInstanceOf(Page::class, $page);
        $this->assertEquals('test-page', $page->slug);
        $this->assertEquals('Test Page', $page->getTranslation('title', 'en'));
        $this->assertTrue($page->status);
        $this->assertDatabaseHas('pages', ['slug' => 'test-page']);
    }

    #[Test]
    public function it_can_update_page(): void
    {
        $page = Page::factory()->create(['slug' => 'original-slug']);

        $updateData = [
            'slug' => 'updated-slug',
            'title' => ['en' => 'Updated Title'],
            'status' => false
        ];

        $updatedPage = $this->repository->update($updateData, $page->id);

        $this->assertInstanceOf(Page::class, $updatedPage);
        $this->assertEquals('updated-slug', $updatedPage->slug);
        $this->assertEquals('Updated Title', $updatedPage->getTranslation('title', 'en'));
        $this->assertFalse($updatedPage->status);
        $this->assertDatabaseHas('pages', ['slug' => 'updated-slug']);
    }

    #[Test]
    public function it_can_delete_page(): void
    {
        $page = Page::factory()->create();

        $result = $this->repository->delete($page->id);

        $this->assertEquals(1, $result); // Laravel destroy() returns count of deleted records
        $this->assertDatabaseMissing('pages', ['id' => $page->id]);
    }

    #[Test]
    public function it_can_show_page(): void
    {
        $page = Page::factory()->create();

        $foundPage = $this->repository->show($page->id);

        $this->assertInstanceOf(Page::class, $foundPage);
        $this->assertEquals($page->id, $foundPage->id);
    }

    #[Test]
    public function it_can_get_all_pages(): void
    {
        Page::factory()->count(5)->create();

        $pages = Page::all();

        $this->assertCount(5, $pages);
        $this->assertInstanceOf(Page::class, $pages->first());
    }

    #[Test]
    public function it_can_get_parent_pages_without_exclusion(): void
    {
        Page::factory()->count(3)->create(['parent_id' => null]);
        Page::factory()->count(2)->create(['parent_id' => 1]); // Child pages

        $parentPages = $this->repository->getParentPages();

        $this->assertIsArray($parentPages);
        $this->assertArrayHasKey(0, $parentPages); // Root option
        $this->assertEquals('Root', $parentPages[0]);
        $this->assertCount(4, $parentPages); // 3 parent pages + Root option
    }

    #[Test]
    public function it_can_get_parent_pages_with_exclusion(): void
    {
        $page1 = Page::factory()->create(['parent_id' => null]);
        $page2 = Page::factory()->create(['parent_id' => null]);
        $page3 = Page::factory()->create(['parent_id' => null]);

        $parentPages = $this->repository->getParentPages($page2->id);

        $this->assertIsArray($parentPages);
        $this->assertArrayHasKey(0, $parentPages); // Root option
        $this->assertArrayHasKey($page1->id, $parentPages);
        $this->assertArrayHasKey($page3->id, $parentPages);
        $this->assertArrayNotHasKey($page2->id, $parentPages); // Excluded
    }

    #[Test]
    public function it_can_get_sections(): void
    {
        // Create some sections
        Section::factory()->count(3)->create(['status' => true]);
        Section::factory()->count(2)->create(['status' => false]);

        $sections = $this->repository->getSections();

        $this->assertIsArray($sections);
        $this->assertCount(3, $sections); // Only active sections
    }

    #[Test]
    public function it_can_save_sections(): void
    {
        $page = Page::factory()->create();
        $section1 = Section::factory()->create();
        $section2 = Section::factory()->create();

        $sections = [
            0 => $section1->id,
            1 => $section2->id
        ];

        $this->repository->saveSections($page->id, $sections);

        $this->assertDatabaseHas('page_sections', [
            'page_id' => $page->id,
            'section_id' => $section1->id,
            'section_order' => 0
        ]);

        $this->assertDatabaseHas('page_sections', [
            'page_id' => $page->id,
            'section_id' => $section2->id,
            'section_order' => 1
        ]);
    }

    #[Test]
    public function it_can_perform_bulk_delete_action(): void
    {
        $pages = Page::factory()->count(3)->create();
        $ids = $pages->pluck('id')->toArray();

        $result = $this->repository->bulkAction($ids, null, 'delete');

        $this->assertEquals('success', $result['type']);
        $this->assertStringContainsString('Page deleted Successfully', $result['message']);

        foreach ($ids as $id) {
            $this->assertDatabaseMissing('pages', ['id' => $id]);
        }
    }

    #[Test]
    public function it_can_perform_bulk_active_action(): void
    {
        $pages = Page::factory()->inactive()->count(3)->create();
        $ids = $pages->pluck('id')->toArray();

        $result = $this->repository->bulkAction($ids, null, 'active');

        $this->assertEquals('success', $result['type']);
        $this->assertStringContainsString('Page Updated Successfully', $result['message']);

        foreach ($ids as $id) {
            $this->assertDatabaseHas('pages', ['id' => $id, 'status' => true]);
        }
    }

    #[Test]
    public function it_can_perform_bulk_inactive_action(): void
    {
        $pages = Page::factory()->active()->count(3)->create();
        $ids = $pages->pluck('id')->toArray();

        $result = $this->repository->bulkAction($ids, null, 'inactive');

        $this->assertEquals('success', $result['type']);
        $this->assertStringContainsString('Page Updated Successfully', $result['message']);

        foreach ($ids as $id) {
            $this->assertDatabaseHas('pages', ['id' => $id, 'status' => false]);
        }
    }

    #[Test]
    public function it_handles_invalid_bulk_action_type(): void
    {
        $pages = Page::factory()->count(2)->create();
        $ids = $pages->pluck('id')->toArray();

        $result = $this->repository->bulkAction($ids, null, 'invalid_action');

        $this->assertEquals('error', $result['type']);
        $this->assertStringContainsString('Something went wrong', $result['message']);
    }

    #[Test]
    public function it_returns_null_when_updating_non_existent_page(): void
    {
        $result = $this->repository->update(['title' => ['en' => 'Test']], 999);

        $this->assertNull($result);
    }

    #[Test]
    public function it_replaces_existing_sections_when_saving(): void
    {
        $page = Page::factory()->create();
        $section1 = Section::factory()->create();
        $section2 = Section::factory()->create();
        $section3 = Section::factory()->create();

        // First, save initial sections
        $this->repository->saveSections($page->id, [0 => $section1->id, 1 => $section2->id]);

        // Then, replace with new sections
        $this->repository->saveSections($page->id, [0 => $section3->id]);

        // Old sections should be removed
        $this->assertDatabaseMissing('page_sections', [
            'page_id' => $page->id,
            'section_id' => $section1->id
        ]);

        $this->assertDatabaseMissing('page_sections', [
            'page_id' => $page->id,
            'section_id' => $section2->id
        ]);

        // New section should exist
        $this->assertDatabaseHas('page_sections', [
            'page_id' => $page->id,
            'section_id' => $section3->id,
            'section_order' => 0
        ]);
    }
}
