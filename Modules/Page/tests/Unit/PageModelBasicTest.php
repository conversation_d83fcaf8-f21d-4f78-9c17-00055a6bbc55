<?php

namespace Modules\Page\Tests\Unit;

use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\Attributes\Test;
use Modules\Page\app\Models\Page;

class PageModelBasicTest extends TestCase
{
    private Page $page;

    protected function setUp(): void
    {
        parent::setUp();
        $this->page = new Page();
    }

    #[Test]
    public function it_has_correct_fillable_attributes(): void
    {
        $expected = [
            'parent_id',
            'title',
            'sub_title',
            'slug',
            'body',
            'meta_keywords',
            'meta_description',
            'status'
        ];

        $this->assertEquals($expected, $this->page->getFillable());
    }

    #[Test]
    public function it_has_correct_guarded_attributes(): void
    {
        $expected = ['id', 'created_at', 'updated_at'];
        $this->assertEquals($expected, $this->page->getGuarded());
    }

    #[Test]
    public function it_has_translatable_attributes(): void
    {
        $expected = ['title', 'sub_title', 'body'];
        $this->assertEquals($expected, $this->page->translatable);
    }

    #[Test]
    public function it_returns_correct_default_locale(): void
    {
        $this->assertEquals('en', $this->page->getDefaultLocale());
    }

    #[Test]
    public function it_uses_correct_table_name(): void
    {
        $this->assertEquals('pages', $this->page->getTable());
    }

    #[Test]
    public function it_has_status_cast_configured(): void
    {
        $casts = $this->page->getCasts();
        $this->assertArrayHasKey('status', $casts);
        $this->assertEquals('boolean', $casts['status']);
    }

    #[Test]
    public function it_can_set_and_check_status_methods(): void
    {
        // Test isActive method exists
        $this->assertTrue(method_exists($this->page, 'isActive'));

        // Test isInactive method exists
        $this->assertTrue(method_exists($this->page, 'isInactive'));

        // Test setActive method exists
        $this->assertTrue(method_exists($this->page, 'setActive'));

        // Test setInactive method exists
        $this->assertTrue(method_exists($this->page, 'setInactive'));
    }

    #[Test]
    public function it_can_set_status_using_methods(): void
    {
        // Test setActive returns self
        $result = $this->page->setActive();
        $this->assertInstanceOf(Page::class, $result);
        $this->assertTrue($this->page->status);

        // Test setInactive returns self
        $result = $this->page->setInactive();
        $this->assertInstanceOf(Page::class, $result);
        $this->assertFalse($this->page->status);
    }

    #[Test]
    public function it_returns_correct_status_label(): void
    {
        // Test active status label
        $this->page->status = true;
        $this->assertEquals('Active', $this->page->getStatusLabelAttribute());

        // Test inactive status label
        $this->page->status = false;
        $this->assertEquals('Inactive', $this->page->getStatusLabelAttribute());
    }
}
