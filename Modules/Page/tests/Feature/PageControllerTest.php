<?php

namespace Modules\Page\Tests\Feature;

use Tests\TestCase;
use PHPUnit\Framework\Attributes\Test;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithoutMiddleware;
use Illuminate\Support\MessageBag;
use Modules\Page\app\Models\Page;
use Modules\Section\app\Models\Section;
use App\Models\Admin;

class PageControllerTest extends TestCase
{
    use RefreshDatabase, WithoutMiddleware;

    private Admin $admin;

    protected function setUp(): void
    {
        parent::setUp();

        // Create and authenticate admin user
        $this->admin = Admin::factory()->create();
        $this->actingAs($this->admin, 'admin');

        // Ensure errors bag is available in tests (for Blade templates)
        $this->session(['errors' => new MessageBag()]);
    }

    #[Test]
    public function it_can_access_pages_route(): void
    {
        $response = $this->get(route('admin.pages.index'));
        $this->assertTrue(in_array($response->status(), [200, 302]));
    }

    #[Test]
    public function it_can_display_pages_index(): void
    {
        Page::factory()->count(3)->create();

        $response = $this->get(route('admin.pages.index'));

        $response->assertOk();
        $response->assertViewIs('page::index');
        $response->assertViewHas('dataGridHtml');
    }

    #[Test]
    public function it_can_display_create_form(): void
    {
        Section::factory()->count(2)->create();

        $response = $this->get(route('admin.pages.create'));

        $response->assertOk();
        $response->assertViewIs('page::create');
        $response->assertViewHas(['parentPages', 'sections', 'method', 'action']);
    }

    #[Test]
    public function it_can_store_new_page(): void
    {
        $section = Section::factory()->create();

        $pageData = [
            'parent_id' => 0,
            'slug' => 'test-page',
            'title' => ['en' => 'Test Page', 'ar' => 'صفحة تجريبية'],
            'sub_title' => ['en' => 'Test Subtitle'],
            'body' => ['en' => 'Test content', 'ar' => 'محتوى تجريبي'],
            'meta_keywords' => 'test page keywords',
            'meta_description' => 'Test page description',
            'status' => true,
            'sections' => [$section->id]
        ];

        $response = $this->post(route('admin.pages.store'), $pageData);

        $response->assertRedirect(route('admin.pages.index'));
        $this->assertDatabaseHas('pages', [
            'slug' => 'test-page',
            'status' => true
        ]);
    }

    #[Test]
    public function it_validates_required_fields_on_store(): void
    {
        $response = $this->post(route('admin.pages.store'), []);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['parent_id', 'title', 'body', 'status']);
    }

    #[Test]
    public function it_can_show_page(): void
    {
        $page = Page::factory()->create();

        $response = $this->get(route('admin.pages.show', encrypt($page->id)));

        $response->assertOk();
        $response->assertViewIs('page::show');
        $response->assertViewHas('model');
    }

    #[Test]
    public function it_can_display_edit_form(): void
    {
        $page = Page::factory()->create();
        Section::factory()->count(2)->create();

        $response = $this->get(route('admin.pages.edit', encrypt($page->id)));

        $response->assertOk();
        $response->assertViewIs('page::edit');
        $response->assertViewHas(['model', 'parentPages', 'sections', 'method', 'action']);
    }

    #[Test]
    public function it_can_update_page(): void
    {
        $page = Page::factory()->create(['slug' => 'original-slug']);

        $updateData = [
            'parent_id' => 0,
            'slug' => 'updated-slug',
            'title' => ['en' => 'Updated Title'],
            'body' => ['en' => 'Updated content'],
            'status' => false
        ];

        $response = $this->put(route('admin.pages.update', encrypt($page->id)), $updateData);

        $response->assertRedirect(route('admin.pages.index'));
        $response->assertSessionHas(['type' => 'success']);
    }

    #[Test]
    public function it_can_delete_page(): void
    {
        $page = Page::factory()->create();

        $response = $this->delete(route('admin.pages.destroy', encrypt($page->id)));

        $response->assertRedirect(route('admin.pages.index'));
        $response->assertSessionHas(['type' => 'success']);
    }

    #[Test]
    public function it_can_change_page_status(): void
    {
        $page = Page::factory()->active()->create();

        $response = $this->withHeaders([
            'X-Requested-With' => 'XMLHttpRequest',
            'Accept' => 'application/json',
        ])->post(route('admin.pages.change_status'), [
            'id' => $page->id,
            'status' => false
        ]);

        $response->assertOk();
        $response->assertJson(['status' => 'success']);
    }

    #[Test]
    public function it_validates_ajax_request_for_status_change(): void
    {
        $response = $this->withHeaders([
            'X-Requested-With' => 'XMLHttpRequest',
            'Accept' => 'application/json',
        ])->post(route('admin.pages.change_status'), []);

        $response->assertOk();
        $response->assertJson(['status' => 'error']);
    }

    #[Test]
    public function it_validates_id_for_status_change(): void
    {
        $response = $this->withHeaders([
            'X-Requested-With' => 'XMLHttpRequest',
            'Accept' => 'application/json',
        ])->post(route('admin.pages.change_status'), [
            'id' => 999, // Non-existent ID
            'status' => true
        ]);

        $response->assertOk();
        $response->assertJson(['status' => 'error']);
    }

    #[Test]
    public function it_can_perform_bulk_actions(): void
    {
        $pages = Page::factory()->count(3)->create();
        $ids = $pages->pluck('id')->toArray();

        $response = $this->withHeaders([
            'X-Requested-With' => 'XMLHttpRequest',
            'Accept' => 'application/json',
        ])->post(route('admin.pages.bulkAction'), [
            'id' => $ids,
            'type' => 'active'
        ]);

        $response->assertOk();
        $response->assertJson(['status' => 'success']);
    }

    #[Test]
    public function it_handles_translation_processing_on_store(): void
    {
        $pageData = [
            'parent_id' => 0,
            'slug' => 'translation-test',
            'title' => ['en' => 'English Title'], // No Arabic provided
            'body' => ['en' => 'English content'], // No Arabic provided
            'status' => true
        ];

        $response = $this->post(route('admin.pages.store'), $pageData);

        $response->assertRedirect(route('admin.pages.index'));

        $page = Page::where('slug', 'translation-test')->first();
        $this->assertNotNull($page);

        // Check that English title is copied to Arabic when Arabic is missing
        $this->assertEquals('English Title', $page->getTranslation('title', 'ar'));
        $this->assertEquals('English content', $page->getTranslation('body', 'ar'));
    }

    #[Test]
    public function it_handles_translation_processing_on_update(): void
    {
        $page = Page::factory()->create();

        $updateData = [
            'parent_id' => 0,
            'slug' => $page->slug,
            'title' => ['en' => 'Updated English Title'], // No Arabic provided
            'body' => ['en' => 'Updated English content'], // No Arabic provided
            'status' => true
        ];

        $response = $this->put(route('admin.pages.update', encrypt($page->id)), $updateData);

        $response->assertRedirect(route('admin.pages.index'));
    }
}
