<?php

namespace Modules\Page\app\Repositories;

use Modules\Page\app\Models\Page;
use Modules\Page\app\Models\PageSection;
use Modules\Section\app\Models\Section;
use App\Repositories\Admin\Repository;

class PageRepository extends Repository
{
    /**
     * PageRepository constructor.
     * @param Page $model
     */
    public function __construct(Page $model)
    {
        $this->model = $model;
    }

    public function bulkAction($ids, $status, $actionType): array
    {
        $type = 'success';
        switch ($actionType) {
            case 'delete':
                foreach ($ids as $id) {
                    $this->model->where('id', $id)->delete();
                }
                $message = __('page::page.page_delete_successfully');
                break;
            case 'active':
            case 'inactive':
                $statusValue = $actionType === 'active' ? true : false;
                $this->model->whereIn('id', $ids)->update(['status' => $statusValue]);
                $message = __('page::page.page_update_successfully');
                break;
            default:
                $type = 'error';
                $message = __('page::page.something_wrong');
                break;
        }
        return [
            'type' => $type,
            'message' => $message
        ];
    }

    public function getParentPages(string|int|null $id = null): array
    {
        $parentPages = $this->model->whereNull('parent_id')
                                    ->when($id, function ($q) use ($id) {
                                        $q->where('id', '!=', $id);
                                    })
                                    ->orderBy('title', 'ASC')
                                    ->pluck('title', 'id')
                                    ->toArray();
        return [0 => 'Root'] + $parentPages;
    }

    /**
     * Get all active sections
     *
     * @return array
     */
    public function getSections(): array
    {
        return Section::where('status', true) // Updated to boolean
                     ->orderBy('title', 'ASC')
                     ->pluck('title', 'id')
                     ->toArray();
    }

    /**
     * Save or update page sections
     *
     * @param int $pageId
     * @param array $sections
     * @return void
     */
    public function saveSections(int $pageId, array $sections = []): void
    {
        // Delete existing relations
        PageSection::where('page_id', $pageId)->delete();

        // Add new relations if sections are provided
        if (!empty($sections)) {
            $sectionData = [];
            foreach ($sections as $order => $sectionId) {
                if (!empty($sectionId)) {
                    $sectionData[] = [
                        'page_id' => $pageId,
                        'section_id' => $sectionId,
                        'section_order' => $order,
                        'created_at' => now(),
                        'updated_at' => now()
                    ];
                }
            }

            if (!empty($sectionData)) {
                PageSection::insert($sectionData);
            }
        }
    }

    /**
     * Get a page by slug with its active sections.
     *
     * @param string $slug
     * @return Page|null
     */
    public function getPageWithActiveSectionsBySlug(string $slug): ?Page
    {
        return $this->model->where('slug', $slug)
            ->with(['sections' => function($q) {
                $q->where('status', true);
            }])
            ->first();
    }
}
