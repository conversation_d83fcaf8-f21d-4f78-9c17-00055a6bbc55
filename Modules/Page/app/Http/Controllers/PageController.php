<?php

namespace Modules\Page\app\Http\Controllers;

use Modules\Page\app\Models\Page;
use Illuminate\Http\Request;
use Nwidart\Modules\Routing\Controller;
use Modules\Page\app\DataGrids\PageDataGrid;
use Modules\Page\app\Http\Requests\PageRequest;
use Modules\Page\app\Repositories\PageRepository;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;

class PageController extends Controller
{
    public const PAGE_INDEX_ROUTE = "admin.pages.index";
    /**
     * PageController constructor.
     * @param PageRepository $pageRepository
     */
    public function __construct(protected PageRepository $pageRepository)
    {
        $this->middleware('permission:create-pages', ['only' => ['create', 'store']]);
        $this->middleware('permission:update-pages', ['only' => ['edit', 'update', 'changeStatus']]);
        $this->middleware(
            'permission:read-pages|create-pages|update-pages|delete-pages',
            ['only' => ['index']]
        );
        $this->middleware('permission:delete-pages', ['only' => ['destroy']]);
    }

    public function index(): View|RedirectResponse
    {
        try{
            $dataGridHtml = PageDataGrid::getHTML();
            return view('page::index', compact('dataGridHtml'));
        } catch (\Exception  $e) {
            return redirect()->back()->with(['type' => 'error', 'message' => __($e->getMessage())]);
        }
    }

    public function create(): View
    {
        try{
            $parentPages = $this->pageRepository->getParentPages();
            $sections = $this->pageRepository->getSections();
            return view('page::create', [
                'parentPages' => $parentPages,
                'sections' => $sections,
                'method' => 'POST',
                'action' => route('admin.pages.store')
            ]);
        }catch(\Exception  $e){
            return response()->json([
                'status'  => 'error',
                'message' => __($e->getMessage())
            ]);
        }
    }

    public function store(PageRequest $request): RedirectResponse
    {
        try{
            $data = $request->validated();

            if ($data['parent_id'] == 0) {
                $data['parent_id'] = null;
            }

            // Create the page
            $page = $this->pageRepository->create($data);

            // Save the sections if present
            if ($request->has('sections')) {
                $this->pageRepository->saveSections($page->id, $request->sections);
            }

            return redirect()->route(self::PAGE_INDEX_ROUTE)->with([
                'type' => 'success',
                'message' => __('page::page.page_add_successfully')
            ]);
        } catch (\Exception  $e) {
            return redirect()->back()->with([
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function show(Page $page): View
    {
        try{
            return view('page::show', ['model' => $page]);
        }catch(\Exception  $e){
            return response()->json([
                'status'  => 'error',
                'message' => __($e->getMessage())
            ]);
        }
    }

    public function edit(Page $page): View|RedirectResponse
    {
        try{
            $parentPages = $this->pageRepository->getParentPages($page->id);
            $sections = $this->pageRepository->getSections();
            $pageSections = $page->sections()->orderBy('page_sections.section_order')->pluck('sections.id')->toArray();

            return view('page::edit', [
                'model' => $page,
                'page' => $page,
                'parentPages' => $parentPages,
                'sections' => $sections,
                'pageSections' => $pageSections,
                'method' => 'PUT',
                'action' => route('admin.pages.update', encrypt($page->id))
            ]);
        }catch(\Exception  $e){
            return redirect()->back()->with([
                'status'  => 'error',
                'message' => __($e->getMessage())
            ]);
        }
    }

    public function update(PageRequest $request, $pageId): RedirectResponse
    {
        try{
            // Manually resolve the page to handle route model binding issues in tests
            $page = Page::findOrFail(decrypt($pageId));
            $data = $request->validated();

            if ($data['parent_id'] == 0) {
                $data['parent_id'] = null;
            }

            $this->pageRepository->update($data, $page->id);

            // Save the sections if present
            if ($request->has('sections')) {
                $this->pageRepository->saveSections($page->id, $request->sections);
            } else {
                // Clear all sections if none were submitted
                $this->pageRepository->saveSections($page->id, []);
            }

            return redirect()->route(self::PAGE_INDEX_ROUTE)->with([
                'type' => 'success',
                'message' => __('page::page.page_update_successfully')
            ]);
        } catch (\Exception  $e) {
            return redirect()->back()->with([
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function destroy(Page $page): JsonResponse
    {
        try{
            $this->pageRepository->delete($page->id);

            return response()->json([
                'status'  => 'success',
                'message' => __('page::page.page_delete_successfully')
            ]);
        }catch(\Exception  $e){
            return response()->json([
                'status'  => 'error',
                'message' => __($e->getMessage())
            ]);
        }
    }

    public function changeStatus(Request $request): JsonResponse
    {
        try {
            if ($request->ajax()) {
                if ($request->input('id') > 0) {
                    $model = $this->pageRepository->show($request->input('id'));
                    if (!$model->status) { // if inactive (false)
                        $data['status'] = true; // set to active
                        $status = __('page::page.activated');
                    } else {
                        $data['status'] = false; // set to inactive
                        $status = __('page::page.de_activated');
                    }
                    $this->pageRepository->update($data, $request->input('id'));

                    return response()->json([
                        'status'     => 'success',
                        'message'    => __('page::page.status_updated',['status' => $status])
                    ]);
                }

                return response()->json([
                    'status'     => 'error',
                    'message'    => __('page::page.something_wrong')
                ]);
            }
        } catch (\Exception  $e) {
            return response()->json(['status'  => 'error', 'message' => __($e->getMessage())]);
        }

        // Return error for non-AJAX requests
        return response()->json([
            'status'  => 'error',
            'message' => __('page::page.something_wrong')
        ]);
    }

    public function bulkAction(Request $request): JsonResponse
    {
        try {
            $ids = $request->id;
            $status = $request->status;
            $actionType = $request->type;
            $result = $this->pageRepository->bulkAction($ids, $status, $actionType);

            return response()->json([
                'status' => $result['type'],
                'message' => $result['message']
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage())
            ]);
        }
    }
}
