<?php

namespace Modules\Page\app\Http\Requests;

use App\Rules\StripTags;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Modules\Page\app\Models\PageSection;

class PageRequest extends FormRequest
{
    protected const MAX = 'max:150';
    protected const REGEX = 'regex:/^[a-zA-Z0-9\s]+$/';

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $rules = [
            'parent_id'              => ['required'],
            'title'                  => ['required', 'array'],
            'title.en'               => ['required', 'string', self::MAX],
            'title.ar'               => ['nullable', 'string', self::MAX],
            'sub_title'              => ['nullable', 'array'],
            'sub_title.en'           => ['nullable', 'string', self::MAX],
            'sub_title.ar'           => ['nullable', 'string', self::MAX],
            'body'                   => ['required', 'array'],
            'body.en'                => ['required', 'string'],
            'body.ar'                => ['nullable', 'string'],
            'meta_keywords'          => ['nullable', 'string', self::REGEX],
            'meta_description'       => ['nullable', 'string', new StripTags],
            'status'                 => ['required', 'boolean'],
            'sections'               => ['nullable', 'array'],
            'sections.*'             => ['nullable', 'integer', 'exists:sections,id', 'distinct'],
        ];

        $route = null;
        if (request()->route()) {
            $routeName = explode('.', request()->route()->getName());
            $route = array_pop($routeName);
        }

        switch($route){
            case 'create':
            case 'store':
                $rules = array_merge($rules, [
                    'slug'       => ['required', 'string', self::MAX, 'unique:pages'],
                ]);
                break;
            case 'edit':
            case 'update':
                $pageParam = request()->route()->parameter('page');
                if (is_string($pageParam)) {
                    try {
                        $pageId = decrypt($pageParam);
                    } catch (\Exception $e) {
                        $pageId = $pageParam;
                    }
                } else {
                    $pageId = $pageParam->id ?? null;
                }
                $rules = array_merge($rules, [
                    'slug' => ['required', 'string', self::MAX, 'unique:pages,slug,' . $pageId . ',id'],
                ]);
                break;
            default :
                return $rules;
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            // General required validations
            'parent_id.required' => __('page::page.parent_id_required'),
            'status.required' => __('page::page.status_required'),
            'status.boolean' => __('page::page.status_boolean'),

            // Title validations
            'title.required' => __('page::page.title_required'),
            'title.array' => __('page::page.title_required'),
            'title.en.required' => __('page::page.title_en_required'),
            'title.ar.required' => __('page::page.title_ar_required'),
            'title.en.string' => __('page::page.title_en_string'),
            'title.ar.string' => __('page::page.title_ar_string'),
            'title.en.max' => __('page::page.title_en_max'),
            'title.ar.max' => __('page::page.title_ar_max'),

            // Sub title validations (nullable, so no required messages)
            'sub_title.array' => __('page::page.sub_title_required'),
            'sub_title.en.string' => __('page::page.sub_title_en_string'),
            'sub_title.ar.string' => __('page::page.sub_title_ar_string'),
            'sub_title.en.max' => __('page::page.sub_title_en_max'),
            'sub_title.ar.max' => __('page::page.sub_title_ar_max'),

            // Body validations
            'body.required' => __('page::page.body_required'),
            'body.array' => __('page::page.body_required'),
            'body.en.required' => __('page::page.body_en_required'),
            'body.ar.required' => __('page::page.body_ar_required'),
            'body.en.string' => __('page::page.body_en_string'),
            'body.ar.string' => __('page::page.body_ar_string'),

            // Slug validations
            'slug.required' => __('page::page.slug_required'),
            'slug.string' => __('page::page.slug_string'),
            'slug.max' => __('page::page.slug_max'),
            'slug.unique' => __('page::page.slug_unique'),

            // Meta fields validations
            'meta_keywords.regex' => __('page::page.meta_keywords_regex'),
            'meta_keywords.string' => __('page::page.meta_keywords_regex'),

            // Sections validations
            'sections.array' => __('page::page.sections_required'),
            'sections.*.exists' => __('page::page.section_exists'),
            'sections.*.distinct' => __('page::page.section_distinct'),
            'sections.*.integer' => __('page::page.section_exists'),
        ];
    }
}
