<?php

namespace Modules\Page\app\DataGrids;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Indianic\LaravelDataGrid\LaravelDataGrid;

class PageDataGrid extends LaravelDataGrid
{
    public const PAGENAME_TRANS = "page::page.page_name";
    public const PARENTPAGENAME_TRANS = "page::page.parent_page_name";
    public const STATUS_TRANS = "page::page.status";
    public const ACTIVE_TRANS = 'page::page.active';

    public $guard = 'admin';
    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'pages';

    /**
     * Define how many rows you want to display on a page
     * @var int $rowPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define row per page dropdown options
     * @var array $recordsPerPageOptions
     */
    public $recordsPerPageOptions;

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy = 'id';

    /**
     * Define default soring direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection = 'DESC';

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'pages';

    protected const CMS = 'page::page';
    protected const CMSPAGENAME = 'page::page.page_name';
    protected const CMSPARENT = 'page::page.parent_page_name';
    protected const CMSSTATUS = 'page::page.status';

    /**
     * Get Resource of Query Builder
     */
    public function resource()
    {
        return DB::table('pages')
            ->select('parent_cms_page.*',
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(parent_cms_page.title, '$.en')) as page_name"),
                'parent_cms_page.id as page_id',
                DB::raw("JSON_UNQUOTE(JSON_EXTRACT(pages.title, '$.en')) as parentPageName")
            )->rightJoin('pages as parent_cms_page', function ($join) {
            $join->on('pages.id', 'parent_cms_page.parent_id');
        });
    }

    public function columns(): array
    {
        return [
            'checkbox' => '<input class="select_all checkbox" type="checkbox" />',
            'page_name' => trans(self::PAGENAME_TRANS),
            'parentPageName' => trans(self::PARENTPAGENAME_TRANS),
            'status' => trans(self::STATUS_TRANS),
            'action' => __('page::page.action'),
        ];
    }

    public function mapDBColumns(): array
    {
        return [
            'page_name' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(parent_cms_page.title, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'parentPageName' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(pages.title, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'status' => 'parent_cms_page.status',
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'page_name',
            'status',
        ];
    }

    // public function downloadableColumns(): array
    // {
    //     return [
    //         'page_name' => trans(self::PAGENAME_TRANS),
    //         'parentPageName' => trans(self::PARENTPAGENAME_TRANS),
    //         'status' => trans(self::STATUS_TRANS),
    //     ];
    // }

    public function gloablSearchableColumns(): array
    {
        return [
            'page_name' => 'string',
            'parentPageName' => 'string',
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'page_name' => 'string',
            'parentPageName' => 'string',
        ];
    }

    // public function getAdvanceSearchOptions(): array
    // {
    //     return [
    //         ['id' => 'page_name', 'label' => trans(self::PAGENAME_TRANS), 'type' => 'string'],
    //         ['id' => 'parentPageName', 'label' => trans(self::PARENTPAGENAME_TRANS), 'type' => 'string'],
    //         ['id' => 'status', 'label' => trans(self::STATUS_TRANS), 'type' => 'string'],
    //     ];
    // }

    // public function filters(): array
    // {
    //     return [
    //         'status' => [
    //             'title' => __('admin.common.status'),
    //             'data' => [
    //                 '1' => __(self::ACTIVE_TRANS), // true = 1 = Active
    //                 '0' => __('page::page.in_active'), // false = 0 = Inactive
    //             ],
    //             'width' => '250',
    //             'type' => 'string',
    //             'auto_filter_val' => __(self::ACTIVE_TRANS),
    //         ],
    //     ];
    // }

    public function getBulkAction(): array
    {
        return [
            'Active' => [
                'class' => 'success',
                'title' => __(self::ACTIVE_TRANS),
                'url' => route('admin.pages.bulkAction'),
                'module' => 'Page',
                'type' => 'active'
            ],
            'Inactive' => [
                'class' => 'warning',
                'title' => __('page::page.in_active'),
                'url' => route('admin.pages.bulkAction'),
                'module' => 'Page',
                'type' => 'inactive'
            ],
            'Delete' => [
                'class' => 'danger',
                'title' => __('page::page.delete'),
                'url' => route('admin.pages.bulkDelete'),
                'module' => 'Page',
                'type' => 'delete'
            ],
        ];
    }

    public function getColumnCategory(array $data): string
    {
        return $data['page_name'];
    }

    public function getColumnParentPageName(array $data): string
    {
        return $data['parentPageName'] ?? '-';
    }

    public function getDownloadColumnStatus(array $data): string
    {
        return (bool) $data['status'] ? 'Active' : 'Inactive';
    }

    public function getColumnStatus(array $data): string
    {
        $isActive = (bool) $data['status'];
        return '<input type="checkbox" class="my-switch change-status" data-id = "' . $data['id'] .
        '" id="switchCheckbox' . $data['id'] . '" ' . ($isActive ? 'checked' : '') . '
        data-bs-toggle="tooltip" title="' . ($isActive ? 'Active' : 'Inactive') . ' Pages">';
    }

    public function getColumnAction(array $data): string
    {
        $return = '';
        $user = Auth::user();

        if ($user && $user->hasPermission('update-pages')) {
            $return = '<a class="me-3" href="' . route('admin.pages.edit', encrypt($data['id'])) . '">
            <span class="inic inic-edit fs-18" data-bs-toggle="tooltip" title="Edit"></span></a>';
        }

        if ($user && $user->hasPermission('delete-pages')) {
            $return .= '<a class="delete-grid-row" href="javascript:void(0)"
            data-grid="inic_grid_' . $this->uniqueID . '"
            data-url="' . route('admin.pages.destroy', encrypt($data['id'])) . '">
            <span class="inic inic-bin fs-18" data-bs-toggle="tooltip" title="Delete"></span></a>';
        }

        return $return;
    }

    public function getColumnCheckbox(array $data): string
    {
        return '<input type="checkbox" value="' . $data['id'] . '" class="check_approval" />';
    }
}
