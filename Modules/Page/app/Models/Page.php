<?php

namespace Modules\Page\app\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Modules\Section\app\Models\Section;
use Modules\Page\Database\Factories\PageFactory;
use Spatie\Translatable\HasTranslations;

class Page extends Model
{
    use HasTranslations, HasFactory;

    /**
     * The attributes that are not mass assignable
     *
     * @var array
     */
    protected $guarded = ['id', 'created_at', 'updated_at'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'parent_id',
        'title',
        'sub_title',
        'slug',
        'body',
        'meta_keywords',
        'meta_description',
        'status'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Check if the page is active.
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->status === true;
    }

    /**
     * Check if the page is inactive.
     *
     * @return bool
     */
    public function isInactive(): bool
    {
        return $this->status === false;
    }

    /**
     * Set the page as active.
     *
     * @return $this
     */
    public function setActive(): self
    {
        $this->status = true;
        return $this;
    }

    /**
     * Set the page as inactive.
     *
     * @return $this
     */
    public function setInactive(): self
    {
        $this->status = false;
        return $this;
    }

    /**
     * Get status as string for display purposes.
     *
     * @return string
     */
    public function getStatusLabelAttribute(): string
    {
        return $this->status ? 'Active' : 'Inactive';
    }

    /**
     * Scope to get only active pages.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope to get only inactive pages.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInactive($query)
    {
        return $query->where('status', false);
    }

    /**
     * The attributes that are translatable.
     *
     * @var array
     */
    public $translatable = ['title', 'sub_title', 'body'];

    /**
     * The default locale to use for translations.
     *
     * @return string
     */
    public function getDefaultLocale(): string
    {
        return 'en';
    }

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'pages';

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): PageFactory
    {
        return PageFactory::new();
    }

    public function subPages(): HasMany
    {
        return  $this->hasMany($this, 'parent_id');
    }

    public function resolveRouteBinding($value, $field = null): self
    {
        return $this->findOrFail(decrypt($value));
    }

    /**
     * Get the sections associated with the page.
     */
    public function sections(): BelongsToMany
    {
        return $this->belongsToMany(Section::class, 'page_sections')
                    ->withPivot('section_order')
                    ->orderBy('page_sections.section_order');
    }

    /**
     * Get the page section pivot records.
     */
    public function pageSections(): HasMany
    {
        return $this->hasMany(PageSection::class);
    }
}
