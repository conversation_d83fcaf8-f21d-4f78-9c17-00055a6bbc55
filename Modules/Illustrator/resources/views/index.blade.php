@extends('admin.layouts.page')
@section('title', __('illustrator::illustrator.illustrator'))

@section('breadcrumb')
    <ol class="breadcrumb-item active">{{ __('illustrator::illustrator.illustrator') }}</ol>
@endsection

@permission('create-illustrator')
    @section('action_buttons')
    <a href="{{ route('admin.illustrators.create') }}" class="btn btn-primary">
        <em class="inic inic-add"></em> {{ __('illustrator::illustrator.add_illustrator') }}
    </a>
    @endsection
@endpermission

@section('content')
    {!! $dataGridHtml !!}
@endsection

@include('plugins.sweetalert2')
@push('scripts')

    <script type="module">
        let csrfToken = $('meta[name="csrf-token"]').attr('content');
        $(document).on('click', '.delete-grid-row', function (event) {
            event.preventDefault();
            let url = $(this).attr('data-url');
            SwalConfirm(url, function (url) {
                $.ajax({
                    url: url,
                    type: 'delete',
                    datatype: 'json',
                    headers: {
                        'X-CSRF-TOKEN': csrfToken
                    },
                    success: function (response) {
                        toastr.success(response.message);
                        window.inic_grid_illustrator.refresh();
                    },
                    error: function (response) {
                        toastr.error(response.message);
                    }
                });
            })
        })

        $(document).on('click', '.grid-bulk-action', function(event) {
            event.preventDefault();
            var id = [];var error = 0;
            $('.check_approval').each(function(index){
                if($(this).prop('checked') == true){
                    id.push($(this).val());
                    error = 1;
                }
            });

            var url= "{{ route('admin.illustrators.bulk-action') }}";
            var status=$(this).data('status');
            var type=$(this).data('action-type');

            if(error == 0){
                toastr.error("{{ __('illustrator::illustrator.please_select_one_illustrator') }}");
            }else if(error == 1){
                SwalConfirm(url, function(url) {
                    $.ajax({
                        url: url,
                        type: 'post',
                        data: {id: id,status:status,type:type},
                        success: function(response) {
                            if(response.status == 'success'){
                                toastr.success(response.message);
                            }else{
                                toastr.error(response.message);
                            }
                            window.inic_grid_illustrator.refresh();
                        },
                        error: function(response) {
                            toastr.error(response.message);
                        }

                    });
                });
            }
        });

    </script>
@endpush
