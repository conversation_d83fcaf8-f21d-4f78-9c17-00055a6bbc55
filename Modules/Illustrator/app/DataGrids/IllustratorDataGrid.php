<?php

namespace Modules\Illustrator\app\DataGrids;

use Indianic\LaravelDataGrid\LaravelDataGrid;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Modules\Illustrator\Models\Illustrator;

class IllustratorDataGrid extends LaravelDataGrid
{
    public $guard = 'admin';
    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'illustrator';

    /**
     * Define how many rows you want to display on a page
     * @var int $rowPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define row per page dropdown options
     * @var array $recordsPerPageOptions
     */
    public $recordsPerPageOptions;

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy = 'id';

    /**
     * Define default soring direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection = 'DESC';

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'illustrator';

    protected const NAME = 'illustrator::illustrator.illustrator_name';
    protected const SLUG = 'illustrator::illustrator.slug';
    protected const CREATED_AT = 'illustrator::illustrator.created_at';
    protected const ACTION = 'illustrator::illustrator.action';

    public function resource()
    {
        return Illustrator::select(
            'illustrators.id',
            'illustrators.created_at as illustrator_created_at',
            'illustrators.slug',
            DB::raw("JSON_EXTRACT(illustrators.name, '$.en') as name")
        );
    }

    public function columns(): array
    {
        return [
            'checkbox'  => '<input class="select_all checkbox" type="checkbox" />',
            'name'      => __(self::NAME),
            'slug'      => __(self::SLUG),
            'illustrator_created_at'=> __(self::CREATED_AT),
            'action'    => __(self::ACTION)
        ];
    }

    public function mapDBColumns(): array
    {
        return [
            'name' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(illustrators.name, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'slug' => 'illustrators.slug',
            'illustrator_created_at' => 'illustrators.created_at'
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'name',
            'slug',
            'illustrator_created_at',
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'name' => 'string',
            'slug' => 'string',
            'illustrator_created_at' => 'string'
        ];
    }

    public function gloablSearchableColumns(): array
    {
        return [
            'name' => 'string',
            'slug' => 'string',
            'illustrator_created_at' => 'string'
        ];
    }

    public function getBulkAction(): array
    {
        return [
            'delete' => [
                'class' => 'danger',
                'title' => __('illustrator::illustrator.delete'),
                'module' => 'Illustrator',
                'type' => 'delete'
            ],
        ];
    }

    public function getColumnName(array $data): string
    {
        $name = $data['name'] ?? $data['name'];
        $name = trim($name, '"');
        return $name;
    }


    public function getSlugColumn(): array
    {
        return [
            'class' => 'text-left',
            'orderable' => true,
            'setCallback' => function ($data) {
                return $data->slug;
            }
        ];
    }

    public function getIllustratorCreatedAtColumn(): array
    {
        return [
            'class' => 'text-center',
            'orderable' => true,
            'setCallback' => function ($data) {
                return date('d M Y', strtotime($data->illustrator_created_at));
            }
        ];
    }

    public function getColumnAction(array $data): string
    {
        $return = '';
        $user = Auth::user();

        if ($user->hasPermission('update-illustrator')) {
            $return .= '<a class="me-3" href="' . route('admin.illustrators.edit', encrypt($data['id'])) . '">
            <span class="inic inic-edit fs-18" data-bs-toggle="tooltip" title="Edit"></span></a>';
        }

        if ($user->hasPermission('delete-illustrator')) {
            $return .= '<a class="delete-grid-row" href="javascript:void(0)" data-grid="inic_grid_' . $this->uniqueID . '"
            data-url="' . route('admin.illustrators.destroy', encrypt($data['id'])) . '">
            <span class="inic inic-bin fs-18" data-bs-toggle="tooltip" title="Delete"></span></a>';
        }

        return $return;
    }

    public function getColumnCheckbox(array $data): string
    {
        return '<input type="checkbox" value="' . $data['id'] . '" class="check_approval" />';
    }
}
