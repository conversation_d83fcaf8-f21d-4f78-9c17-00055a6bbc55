<div class="card">
    <form method="POST" action="{{ $action }}" class="closeModalAfter reset">
        @csrf
        @if (isset($newsletter->id))
            @method('PUT')
        @endif

        <div class="row">
            <div class="col-lg-6 col-sm-6 mb-3 position-relative">
                <label for="title" class="form-label">{{ __('newsletter::newsletter.title') }}</label>
                <input type="text" name="title" class="form-control" id="title"
                    placeholder="{{ __('newsletter::newsletter.title') }}"
                    value="{{ old('title', $newsletter->title ?? '') }}">
            </div>
            <div class="col-lg-12 col-sm-6 mb-3 position-relative">
                <label for="body" class="form-label">{{ __('newsletter::newsletter.body') }}</label>
                <textarea name="body" class="form-control ck-editor" id="body" rows="3">{{ old('body', $newsletter->body ?? '') }}</textarea>
            </div>

            <div class="col-lg-6 col-sm-6 mb-3 position-relative">
                <label for="sending" class="form-label">{{ __('newsletter::newsletter.when_to_send') }}</label>
                <div class="d-flex flex-wrap gap-4">
                    <div class="form-check">
                        <input type="radio" name="sending" id="sending_now" class="form-check-input" value="now"
                            {{ old('sending', $newsletter->sending ?? '') == 'now' ? 'checked' : '' }}>
                        <label class="form-check-label"
                            for="sending_now">{{ __('newsletter::newsletter.send_now') }}</label>
                    </div>
                    <div class="form-check">
                        <input type="radio" name="sending" id="sending_later" class="form-check-input" value="later"
                            {{ (isset($model) && old('sending', $newsletter->sending ?? '') == 'later') || !isset($model) ? 'checked' : '' }}>
                        <label class="form-check-label"
                            for="sending_later">{{ __('newsletter::newsletter.send_later') }}</label>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 col-sm-6 mb-3 position-relative">
                <label for="send_to" class="form-label mr-5">{{ __('newsletter::newsletter.send_to') }}</label>
                <div class="d-flex flex-wrap gap-4">
                    @php
                        $sentUsers = [];
                        $isAllUsers = true;
                        $isCustomUsers = false;

                        // Check if we're editing and determine the selection type
                        if (isset($newsletter->send_to)) {
                            if ($newsletter->send_to === 'all_users') {
                                $isAllUsers = true;
                                $isCustomUsers = false;
                            } else {
                                // If send_to is not 'all_users', it contains user IDs
                                $isAllUsers = false;
                                $isCustomUsers = true;
                                $sentUsers = explode(',', $newsletter->send_to);
                            }
                        }

                        // Handle old input (form validation errors)
                        if (old('send_to')) {
                            $isAllUsers = old('send_to') === 'all_users';
                            $isCustomUsers = old('send_to') === 'custom_users';
                        }
                    @endphp

                    <div class="form-check">
                        <input type="radio" name="send_to" id="send_to_all_users" class="form-check-input"
                            value="all_users"
                            {{ $isAllUsers ? 'checked' : '' }}>
                        <label class="form-check-label"
                            for="send_to_all_users">{{ __('newsletter::newsletter.all_users') }}</label>
                    </div>
                    <div class="form-check">
                        <input type="radio" name="send_to" id="send_to_custom_users" class="form-check-input"
                            value="custom_users"
                            {{ $isCustomUsers ? 'checked' : '' }}>
                        <label class="form-check-label"
                            for="send_to_custom_users">{{ __('newsletter::newsletter.custom_users') }}</label>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 position-relative mb-3">
                <div id="send_date_control" style="display:none;">
                    <label for="send_date" class="form-label">{{ __('newsletter::newsletter.date_time') }}</label>
                    <input type="datetime-local" name="send_date" class="form-control col-md-6" id="send_date" min="{{ now()->format('Y-m-d\TH:i') }}"
                        value="{{ old('send_date', $newsletter->send_date ?? '') }}"
                        placeholder="{{ __('newsletter::newsletter.date_time') }}">
                </div>
            </div>
            <div class="col-lg-6 position-relative mb-3">
                <div id="user_select" style="display:none;">
                    <label for="userSelectField"
                        class="form-label">{{ __('newsletter::newsletter.select_users') }}</label>
                    <select name="send_users[]" id="userSelectField" class="select2" multiple>
                        @foreach ($userData as $key => $value)
                            @php
                                $isSelected = false;
                                // Check old input first (for validation errors)
                                if (old('send_users')) {
                                    $isSelected = in_array($key, old('send_users'));
                                } elseif (!empty($sentUsers)) {
                                    // Check if this user was previously selected
                                    $isSelected = in_array($key, $sentUsers);
                                }
                            @endphp
                            <option value="{{ $key }}" {{ $isSelected ? 'selected' : '' }}>
                                {{ $value }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
            <div class="col-md-12">
                <div class="d-flex align-items-center justify-content-end gap-3">
                    <button type="submit" class="btn btn-sm btn-primary">{{ __('admin.common.submit') }}</button>
                    <a href="{{ route('admin.newsletters.index') }}"
                        class="btn btn-sm btn-secondary">{{ __('admin.common.cancel') }}</a>
                </div>
            </div>
        </div>
    </form>
</div>
@include('plugins.select2')
@include('plugins.ckeditor')
@push('scripts')
    @php
        $validator = JsValidator::formRequest('Modules\Newsletter\app\Http\Requests\NewsletterRequest');
        $validator->ignore('');
    @endphp ?> ?> ?> ?> ?>
    {!! $validator !!}

    <script type="module">
        $(document).ready(function() {
            if ($('#sending_later').prop('checked')) {
                $('#send_date_control').slideDown();
            } else {
                $('#send_date_control').slideUp();
            }

            $('#sending_later, #sending_now').on('change', function() {
                if ($('#sending_later').prop('checked')) {
                    $('#send_date_control').slideDown();
                } else {
                    $('#send_date_control').slideUp();
                }
            });

            // Initializing Datetimepicker
            // $('.datetime-picker').datetimepicker({
            //     minDate: new Date(),
            //     startDate: new Date(),
            //     showClear: true,
            //     showClose: true,
            //     sideBySide: true,
            //     autoclose: true,
            // });

            if ($('#send_to_custom_users').prop('checked')) {
                $('#user_select').slideDown();
            } else {
                $('#user_select').slideUp();
            }

            $('#send_to_custom_users, #send_to_all_users').on('change', function() {
                if ($('#send_to_custom_users').prop('checked')) {
                    $('#user_select').slideDown();
                } else {
                    $('#user_select').slideUp();
                    $("#userSelectField").val(null).trigger('change');
                }
            });

        });

        // Ckeditor
        document.addEventListener('DOMContentLoaded', function() {
            initializeCKEditor('body', options);
        });
    </script>
@endpush
