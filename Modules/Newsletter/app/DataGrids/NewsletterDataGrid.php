<?php

namespace Modules\Newsletter\app\DataGrids;

use Indianic\LaravelDataGrid\LaravelDataGrid;
use Illuminate\Support\Facades\Auth;
use Modules\Newsletter\app\Models\Newsletter;
use DB;

class NewsletterDataGrid extends LaravelDataGrid
{
    public $guard = 'admin';
    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'newsletters';

    /**
     * Define how many rows you want to display on a page
     * @var int $rowPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define row per page dropdown options
     * @var array $recordsPerPageOptions
     */
    public $recordsPerPageOptions;

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy;

    /**
     * Define default soring direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection = 'DESC';

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'newsletters';

    protected const TRNS_NEWSLETTER_TITLE = 'newsletter::newsletter.title';
    protected const TRNS_NEWSLETTER_DELIVERED = 'newsletter::newsletter.delivered';
    protected const TRNS_NEWSLETTER_SEND_DATE = 'newsletter::newsletter.send_date';
    protected const TRNS_NEWSLETTER_ACTION = 'newsletter::newsletter.action';

    /**
     * Get Resource of Query Builder
     */
    public function resource(): object
    {
        return Newsletter::select();
    }

    public function columns(): array
    {
        return [
            'title' => __(self::TRNS_NEWSLETTER_TITLE),
            'delivered' => __(self::TRNS_NEWSLETTER_DELIVERED),
            'send_date' => __(self::TRNS_NEWSLETTER_SEND_DATE),
            'action' => __(self::TRNS_NEWSLETTER_ACTION)
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'title',
            'delivered',
            'send_date',
        ];
    }

    // public function downloadableColumns(): array
    // {
    //     return [
    //         'title' =>  __(self::TRNS_NEWSLETTER_TITLE),
    //         'delivered' =>__(self::TRNS_NEWSLETTER_DELIVERED),
    //         'send_date' => __(self::TRNS_NEWSLETTER_SEND_DATE),
    //     ];
    // }

    public function gloablSearchableColumns(): array
    {
        return [
            'title' => 'string',
            'delivered' => 'string',
            'send_date' => 'string',
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'title' => 'string',
            'delivered' => 'string',
            'send_date' => 'string',
        ];
    }

    // public function getAdvanceSearchOptions(): array
    // {
    //     return [
    //         ['id' => 'title', 'label' =>__(self::TRNS_NEWSLETTER_TITLE), 'type' => 'string'],
    //         ['id' => 'delivered', 'label' =>__(self::TRNS_NEWSLETTER_DELIVERED), 'type' => 'string'],
    //     ];
    // }

    public function getColumnCheckbox(array $data): string
    {
        return '<input type="checkbox" value="' . $data['id'] . '" class="check_approval" />';
    }

    public function getColumnSendDate(array $data): string
    {
        return adminDateTimeFormatShow($data['send_date']);
    }

    public function getColumnAction(array $data): string
    {
        $return = '';

        if ((strtotime($data['send_date']) - strtotime(date('Y-m-d H:i:s'))) >= 2 &&
        Auth::user()->hasPermission('update-newsletters')) {
            $return = '<a class="me-3" href="' . route(
                'admin.newsletters.edit',
                encrypt($data['id'])
            ) . '"><span class="inic inic-edit fs-18" data-bs-toggle="tooltip" title="Edit"></a>';
        }
        $return .= '<a class="me-3" href="' . route(
            'admin.newsletters.show',
            encrypt($data['id'])
        ) . '"><span class="inic inic-visibility fs-18" data-bs-toggle="tooltip" title="Show"></span></a>';

        return $return;
    }
}
