<form method="POST" action="{{ $action }}" enctype="multipart/form-data">
    @csrf
    @if (isset($model))
        @method('PUT')
    @endif

    <!-- Language Switcher -->
    @include('publisher::partials.language-switcher')

    <div class="row">
        <!-- Card for translatable fields -->
        <div class="col-md-12 mb-4">
            <div class="card border-primary" style="border-width: 2px;">
                <div class="card-header bg-primary bg-opacity-10">
                    <h5 class="mb-0"><i class="fa fa-language"></i> {{ __('admin.common.translatable_fields') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Name (translatable) -->
                        <div class="col-md-12 mb-3">
                            <label for="name_en" class="form-label">{{ __('publisher::publisher.name') }}</label>
                            <div class="form-group {{ $errors->has('name.en') ? 'has-error' : '' }} translatable-field" data-language="en">
                                <input type="text" name="name[en]" id="name_en" class="form-control"
                                    value="{{ old('name.en', isset($model) ? $model->getTranslation('name', 'en', false) : '') }}"
                                    placeholder="{{ __('publisher::publisher.name_placeholder') }}">
                                @if ($errors->has('name.en'))
                                    <span class="help-block">{{ $errors->first('name.en') }}</span>
                                @endif
                            </div>
                            <div class="form-group {{ $errors->has('name.ar') ? 'has-error' : '' }} translatable-field" data-language="ar" style="display: none;">
                                <input type="text" name="name[ar]" id="name_ar" class="form-control" dir="rtl"
                                    value="{{ old('name.ar', isset($model) ? $model->getTranslation('name', 'ar', false) : '') }}"
                                    placeholder="{{ __('publisher::publisher.name_placeholder') }}">
                                @if ($errors->has('name.ar'))
                                    <span class="help-block">{{ $errors->first('name.ar') }}</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card for non-translatable fields -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fa fa-cog"></i> {{ __('admin.common.general_settings') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Slug -->
                        <div class="col-md-6 mb-3">
                            <label for="slug" class="form-label">{{ __('publisher::publisher.slug') }}</label>
                            <input type="text" name="slug" id="slug" class="form-control"
                                value="{{ old('slug', $model->slug ?? '') }}"
                                placeholder="{{ __('publisher::publisher.slug_placeholder') }}">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form buttons -->
        <div class="col-md-12">
            <div class="d-flex align-items-center justify-content-end gap-3">
                <button type="submit" class="btn btn-primary">{{ __('admin.common.submit') }}</button>
                <a href="{{ route('admin.publishers.index') }}" class="btn btn-secondary">{{ __('admin.common.cancel') }}</a>
            </div>
        </div>
    </div>
</form>

@push('scripts')
    @php
        $validator = JsValidator::formRequest('Modules\Publisher\app\Http\Requests\PublisherRequest');
        $validator->ignore('');
    @endphp
    {!! $validator !!}

    <script type="module">
        document.addEventListener('DOMContentLoaded', () => {
            // Auto-generate slug from name
            $('#name_en').on('keyup change', function() {
                var name = $(this).val();
                var slug = generateSlug(name);
                $('#slug').val(slug);
            });

            // Function to generate slug
            function generateSlug(text) {
                if(!text) return '';

                // Convert to lowercase
                var slug = text.toLowerCase();

                // Replace spaces with hyphens
                slug = slug.replace(/\s+/g, '-');

                // Remove special characters
                slug = slug.replace(/[^\w\-]+/g, '');

                // Remove duplicate hyphens
                slug = slug.replace(/\-\-+/g, '-');

                // Remove leading and trailing hyphens
                slug = slug.replace(/^-+/, '').replace(/-+$/, '');

                return slug;
            }
        });
    </script>
@endpush
