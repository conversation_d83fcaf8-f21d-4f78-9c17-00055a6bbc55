<?php

namespace Modules\FAQCategory\app\DataGrids;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Indianic\LaravelDataGrid\LaravelDataGrid;
use Modules\FAQCategory\app\Models\FAQCategory;

class FAQCategoryDataGrid extends LaravelDataGrid
{
    public $guard = 'admin';
    public $sno = 1;
    public const CATEGORY_TRANS = "faqcategory::admin.datagrid.category";
    protected const STATUS = 'faqcategory::admin.datagrid.status';

    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'FAQCategory';

    /**
     * Define how many rows you want to display on a page
     * @var int $rowPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define row per page dropdown options
     * @var array $recordsPerPageOptions
     */
    public $recordsPerPageOptions = [10, 25, 50];

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy;

    /**
     * Define default soring direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection;

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'faq-category';

    /**
     * Get Resource of Query Builder
     */
    public function resource()
    {
        return FAQCategory::select(
            'faq_categories.id',
            'faq_categories.status',
            'faq_categories.created_at',
            DB::raw("JSON_EXTRACT(faq_categories.category, '$.en') as category")
        );
    }

    public function mapDBColumns(): array
    {
        return [
            'category' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(faq_categories.category, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'status' => 'faq_categories.status',
            'created_at' => 'faq_categories.created_at'
        ];
    }

    public function columns(): array
    {
        return [
            'checkbox' => '<input class="select_all checkbox" type="checkbox" />',
            'sno' => __('#'),
            'category' => __(self::CATEGORY_TRANS),
            'status'=> __(self::STATUS),
            'action' => __('faqcategory::admin.datagrid.actions'),
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'sno',
            'category',
        ];
    }

    // public function downloadableColumns(): array
    // {
    //     return [
    //         'sno' => "#",
    //         'category' => __(self::CATEGORY_TRANS),
    //     ];
    // }

    // public function getAdvanceSearchOptions(): array
    // {
    //     return [
    //         [
    //             'id' => lcfirst(trans("faqcategory::admin.datagrid.category")),
    //             'label' => __(self::CATEGORY_TRANS),
    //             'type' => 'string',
    //         ],
    //     ];
    // }

    public function filters(): array
    {
        return [];
    }

    public function getindex(): array
    {
        return [];
    }

    public function gloablSearchableColumns(): array
    {
        return [
            'category->en' => "string",
            'category->ar' => "string",
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'category->en' => "string",
            'category->ar' => "string",
        ];
    }

    public function getBulkAction(): array
    {
        return [
            'Active'    => [
                'class' =>'success',
                'title' =>__('faqcategory::admin.form_label.active'),
                'url'   =>route('admin.faq-category.bulkAction') ,
                'module'=>'FAQCategory'],
            'Inactive'  => [
                'class' =>'warning',
                'title' =>__('faqcategory::admin.form_label.in_active'),
                'url'   =>route('admin.faq-category.bulkAction') ,
                'module'=>'FAQCategory'],
        ];
    }

    public function getColumnAction(array $data): string
    {
        $return = '';
        $user = Auth::user();

        if ($user->hasPermission('update-faq-category')) {
            $return .= '<a class="me-3" href="' . route('admin.faq-category.edit', encrypt($data['id'])) . '">
            <span class="inic inic-edit fs-18" data-bs-toggle="tooltip" title="Edit"></span></a>';
        }

        if ($user->hasPermission('delete-faq-category')) {
            $return .= '<a class="delete-grid-row" href="javascript:void(0)" data-grid="inic_grid_' . $this->uniqueID . '"
            data-url="' . route('admin.faq-category.destroy', encrypt($data['id'])) . '">
            <span class="inic inic-bin fs-18" data-bs-toggle="tooltip" title="Delete"></span></a>';
        }

        return $return;
    }

    public function getColumnCheckbox(array $data): string
    {
        return '<input type="checkbox" value="' . ($data['id']) . '" class="check_approval" />';
    }

    public function getColumnSno(): string
    {
        return $this->sno++;
    }

    public function getColumnCategory(array $data): string
    {
        $category = $data['category'] ?? '';
        $category = trim($category, '"');
        return '<span class="badge bg-primary">' . htmlspecialchars($category) . '</span>';
    }

    public function getDownloadColumnCategory(array $data): string
    {
        $category = $data['category'] ?? '';
        $category = trim($category, '"');
        return $category;
    }

    public function getDownloadColumnTitle(array $data): string
    {
        return $data['title'] ?? '';
    }

    public function getColumnStatus($data): string
    {
        $isActive = (bool) $data['status'];
        return '<input type="checkbox" class="my-switch change-status"
        data-id = "' . $data['id'] . '"
        id="switchCheckbox' . $data['id'] . '" ' . ($isActive ? 'checked' : '') . '
        data-bs-toggle="tooltip" title="' . ($isActive ? 'Active' : 'Inactive') . ' FAQ Category">';
    }

}
