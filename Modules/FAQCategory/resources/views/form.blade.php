<form method="POST" action="{{ $action }}" enctype="multipart/form-data">
    @csrf
    @if (isset($model))
        @method('PUT')
    @endif

    <!-- Language Switcher -->
    @include('faqcategory::partials.language-switcher')

    <div class="row">
        <!-- Card for translatable fields -->
        <div class="col-md-12 mb-4">
            <div class="card border-primary" style="border-width: 2px;">
                <div class="card-header bg-primary bg-opacity-10">
                    <h5 class="mb-0"><i class="fa fa-language"></i> {{ __('admin.common.translatable_fields') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Category (translatable) -->
                        <div class="col-md-12 mb-3">
                            <label for="category_en" class="form-label">{{ __('faqcategory::admin.form_label.category') }}</label>
                            <div class="form-group {{ $errors->has('category.en') ? 'has-error' : '' }} translatable-field" data-language="en">
                                <input type="text" name="category[en]" id="category_en" class="form-control"
                                    value="{{ old('category.en', isset($model) ? $model->getTranslation('category', 'en', false) : '') }}"
                                    placeholder="{{ __('faqcategory::faqcategory.name_placeholder') }}">
                                @if ($errors->has('category.en'))
                                    <span class="help-block">{{ $errors->first('category.en') }}</span>
                                @endif
                            </div>
                            <div class="form-group {{ $errors->has('category.ar') ? 'has-error' : '' }} translatable-field" data-language="ar" style="display: none;">
                                <input type="text" name="category[ar]" id="category_ar" class="form-control" dir="rtl"
                                    value="{{ old('category.ar', isset($model) ? $model->getTranslation('category', 'ar', false) : '') }}"
                                    placeholder="{{ __('faqcategory::faqcategory.name_placeholder') }}">
                                @if ($errors->has('category.ar'))
                                    <span class="help-block">{{ $errors->first('category.ar') }}</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card for non-translatable fields -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fa fa-cog"></i> {{ __('admin.common.general_settings') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Status -->
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">{{ __('faqcategory::admin.status') }}</label>
                            <select name="status" id="status" class="form-select">
                                <option value="1" {{ old('status', $model->status ?? true) == 1 ? 'selected' : '' }}>
                                    {{ __('faqcategory::admin.messages.active') }}
                                </option>
                                <option value="0" {{ old('status', $model->status ?? '') == 0 ? 'selected' : '' }}>
                                    {{ __('faqcategory::admin.messages.in_active') }}
                                </option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form buttons -->
        <div class="col-md-12">
            <div class="d-flex align-items-center justify-content-end gap-3">
                <button type="submit" class="btn btn-primary">{{ __('admin.common.submit') }}</button>
                <a href="{{ route('admin.faq-category.index') }}" class="btn btn-secondary">{{ __('admin.common.cancel') }}</a>
            </div>
        </div>
    </div>
</form>

@include('plugins.ckeditor')

@push('scripts')
    @php
        $validator = JsValidator::formRequest('Modules\FAQCategory\app\Http\Requests\FAQCategoryRequest');
        $validator->ignore('');
    @endphp
    {!! $validator !!}
@endpush
