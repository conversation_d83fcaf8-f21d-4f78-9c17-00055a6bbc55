@extends('admin.layouts.page')
@section('title', __('faqcategory::admin.faq_title'))
@section('breadcrumb')
<ol class="breadcrumb-item"><a href="{{ Route::has('admin.dashboard') ? route('admin.dashboard') : '#' }}">{{
        __('admin.common.home') }}</a></ol>
<ol class="breadcrumb-item active">{{ __('faqcategory::admin.faq_title') }}</ol>
@endsection

@if(Auth::user()->hasPermission('create-faq-category') || Auth::user()->hasRole('super_admin'))
@section('action_buttons')
<a href="{{ Route::has('admin.faq-category.create') ? route('admin.faq-category.create') :'#' }}" class="btn btn-primary"><em
        class="inic inic-add"></em> {{ __('faqcategory::admin.faq_add') }}</a>
@endsection
@endif


@section('content')
{!! $dataGridHtml !!}
@endsection

@push('scripts')

<script type="module">
    $(document).on('change','.change-status', function(){
            var id =  $(this).attr('data-id');
            $.ajax({
                url: "{{ route('admin.faq-category.change_status')}}",
                type: "post",
                data: {"id": id},
                datatype: 'json',
                success: function (response) {
                   toastr.success(response.message);
                   window.inic_grid_FAQCategory.refresh();
                },
                error: function (response) {
                   toastr.error(response.message);
                }
            });
        });
    $(document).on('click', '.delete-grid-row', function (event) {
        event.preventDefault();
        let url = $(this).attr('data-url');
        SwalConfirm(url, function (url) {
            $.ajax({
                url: url,
                type: 'delete',
                datatype: 'json',
                success: function (response) {
                    toastr.success(response.message);
                    window.inic_grid_FAQCategory.refresh();
                },
                error: function (response) {
                    toastr.error(response.message);
                }
            });
        })
    })

    $(document).on('click', '#bulkActive,#bulkInactive', function(event) {
            event.preventDefault();
            var id = [];var error = 0;
            $('.check_approval').each(function(index){
                if($(this).prop('checked') == true){
                    id.push($(this).val());
                    error = 1;
                }
            });

            var url= "{{ route('admin.faq-category.bulkAction') }}";
            var status=$(this).data('status');

            if(error == 0){
                toastr.error("{{ __('faqcategory::admin.messages.select_one_cat') }}");
            }else if(error == 1){
                SwalConfirm(url, function(url) {
                    $.ajax({
                        url: url,
                        type: 'post',
                        data: {id: id,status:status},
                        success: function(response) {
                            if(response.status == 'success'){
                                toastr.success(response.message);
                            }else{
                                toastr.error(response.message);
                            }
                            window.inic_grid_FAQCategory.refresh();
                        },
                        error: function(response) {
                            toastr.error(response.message);
                        }
                    });
                });
            }
        });

</script>
@endpush
