<?php

namespace Modules\Author\app\DataGrids;

use Indianic\LaravelDataGrid\LaravelDataGrid;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Vite;
use Modules\Author\app\Models\Author;

class AuthorDataGrid extends LaravelDataGrid
{
    public $guard = 'admin';

    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'author';

    /**
     * Define how many rows you want to display on a page
     * @var int $recordsPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define row per page dropdown options
     * @var array $recordsPerPageOptions
     */
    public $recordsPerPageOptions;

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy = 'display_order';

    /**
     * Define default sorting direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection = 'DESC';

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'authors';

    // DataGrid constants
    public const NAME = 'author::author.name';
    public const STATUS = 'author::author.status';
    public const DISPLAY_ORDER = 'author::author.display_order';
    public const CREATED_AT = 'author::author.created_at';
    public const ACTION = 'author::author.action';

    public function resource()
    {
        return Author::select(
            'authors.id',
            'authors.status',
            'authors.created_at',
            'authors.display_order',
            'authors.created_at as author_created_at',
            DB::raw("JSON_EXTRACT(authors.name, '$.en') as name")
        )->with('media');
    }

    public function columns(): array
    {
        return [
            'checkbox' => '<input class="select_all checkbox" type="checkbox" />',
            'name' => __(self::NAME),
            'status' => __(self::STATUS),
            'display_order' => __(self::DISPLAY_ORDER),
            'author_created_at' => __(self::CREATED_AT),
            'action' => __(self::ACTION)
        ];
    }

    public function mapDBColumns(): array
    {
        return [
            'name' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(authors.name, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'status' => 'authors.status',
            'created_at' => 'authors.created_at'
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'name',
            'status',
            'display_order',
            'author_created_at'
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'name' => 'authors.name',
            'status' => 'authors.status'
        ];
    }

    public function gloablSearchableColumns(): array
    {
        return [
            'authors.name' => 'string',
            'authors.slug' => 'string'
        ];
    }

    public function getBulkAction(): array
    {
        return [
            'activate' => [
                'class' => 'success',
                'title' => __('author::author.activate'),
                'module' => 'Author',
                'type' => 'activate'
            ],
            'deactivate' => [
                'class' => 'warning',
                'title' => __('author::author.deactivate'),
                'module' => 'Author',
                'type' => 'deactivate'
            ],
            'delete' => [
                'class' => 'danger',
                'title' => __('author::author.delete'),
                'module' => 'Author',
                'type' => 'delete'
            ],
        ];
    }

    public function getColumnName(array $data): string
    {
        $author = Author::find($data['id']);

        $url = $author->getProfileImageUrl('thumbnail');

        $name = $data['name'] ?? '';
        $name = trim($name, '"');

        return '<div class="d-flex align-items-center">
                    <img class="me-2 rounded-circle" src="' . $url . '" alt="' . $name . '" width="40" height="40">
                    <a href="javascript:;" class="text-black">' . $name . '</a>
                </div>';
    }

    public function getColumnStatus(array $data): string
    {
        $isActive = (bool) $data['status'];
        return '<input type="checkbox" class="my-switch change-status" data-id = "' . $data['id'] . '"
        id="switchCheckbox' . $data['id'] . '" ' . ($isActive ? 'checked' : '') . '
        data-bs-toggle="tooltip" title="' . ($isActive ? 'Active' : 'Inactive') . '">';
    }

    public function getColumnDisplayOrder(array $data): string
    {
        return '<span class="badge bg-primary">' . $data['display_order'] . '</span>';
    }

    public function getColumnAuthorCreatedAt(array $data)
    {
        return adminDateTimeFormatShow($data['author_created_at']);
    }

    public function getColumnAction(array $data): string
    {
        $return = '';
        $user = Auth::user();

        if ($user->hasPermission('update-author')) {
            $return .= '<a class="me-3" href="' . route('admin.authors.edit', encrypt($data['id'])) . '">
            <span class="inic inic-edit fs-18" data-bs-toggle="tooltip" title="Edit"></span></a>';
        }

        if ($user->hasPermission('read-author')) {
            $return .= '<a class="me-3" href="' . route('admin.authors.show', encrypt($data['id'])) . '">
            <span class="inic inic-eye fs-18" data-bs-toggle="tooltip" title="View"></span></a>';
        }

        if ($user->hasPermission('delete-author')) {
            $return .= '<a class="delete-grid-row" href="javascript:void(0)" data-grid="inic_grid_' . $this->uniqueID . '"
            data-url="' . route('admin.authors.destroy', encrypt($data['id'])) . '">
            <span class="inic inic-bin fs-18" data-bs-toggle="tooltip" title="Delete"></span></a>';
        }

        return $return;
    }

    public function getColumnCheckbox(array $data): string
    {
        return '<input type="checkbox" value="' . $data['id'] . '" class="check_approval" />';
    }

    public function getDownloadColumnName(array $data): string
    {
        $name = $data['name'] ?? '';
        return trim($name, '"');
    }

    public function getDownloadColumnStatus(array $data): string
    {
        return (bool) $data['status'] ? 'Active' : 'Inactive';
    }
}
