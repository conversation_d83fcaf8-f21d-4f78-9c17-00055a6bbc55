<?php

namespace Modules\Author\app\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\Author\app\DataGrids\AuthorDataGrid;
use Modules\Author\app\Models\Author;
use Modules\Author\app\Http\Requests\AuthorRequest;
use Modules\Author\app\Http\Requests\AuthorImportRequest;
use Modules\Author\app\Repositories\AuthorRepository;
use Modules\Author\app\Services\AuthorImportService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;

class AuthorController extends Controller
{
    public const INDEX_ROUTE = "admin.authors.index";

    /**
     * AuthorController constructor.
     * @param AuthorRepository $authorRepository
     * @param AuthorImportService $authorImportService
     */
    public function __construct(
        protected AuthorRepository $authorRepository,
        protected AuthorImportService $authorImportService
    ) {
        $this->middleware('permission:create-author', ['only' => ['create', 'store', 'showImport', 'processImport']]);
        $this->middleware('permission:update-author', ['only' => ['edit', 'update', 'changeStatus']]);
        $this->middleware(
            'permission:read-author|create-author|update-author|delete-author',
            ['only' => ['index']]
        );
        $this->middleware('permission:delete-author', ['only' => ['destroy']]);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(): View|RedirectResponse
    {
        try {
            $dataGridHtml = AuthorDataGrid::getHTML();
            return view('author::index', compact('dataGridHtml'));
        } catch (\Exception $e) {
            return redirect()->back()->with(['type' => 'error', 'message' => __($e->getMessage())]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View|JsonResponse
    {
        $nextDisplayOrder = $this->authorRepository->getNextDisplayOrder();
        try {
            return view('author::create', [
                'method' => 'POST',
                'action' => route('admin.authors.store'),
                'nextDisplayOrder' => $nextDisplayOrder,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage()),
            ]);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(AuthorRequest $request): RedirectResponse
    {
        try {
            $data = $request->validated();

            // Set default display order if not provided
            if (!isset($data['display_order']) || $data['display_order'] === null) {
                $maxOrder = $this->authorRepository->getModel()->max('display_order');
                $data['display_order'] = $maxOrder ? $maxOrder + 1 : 1;
            }

            $author = $this->authorRepository->create($data);

            // Handle media upload from cropped image
            if (!empty($request['cropped_image'])) {
                $croppedImage = $request['cropped_image'];

                // Decode base64 image
                $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $croppedImage));

                // Create temporary file
                $tempFile = tempnam(sys_get_temp_dir(), 'author_image_');
                file_put_contents($tempFile, $imageData);

                try {
                    // Add media from file
                    $author->addMedia($tempFile)
                        ->usingName('Profile Image')
                        ->usingFileName('profile-image.jpg')
                        ->toMediaCollection('profile_image');
                } finally {
                    // Clean up temporary file only if it still exists
                    if (file_exists($tempFile)) {
                        unlink($tempFile);
                    }
                }
            }

            return redirect()->route(self::INDEX_ROUTE)->with([
                'type' => 'success',
                'message' => __('author::author.author_created_successfully')
            ]);
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => __('author::author.something_wrong')
            ]);
        }
    }

    /**
     * Show the specified resource.
     */
    public function show(Author $author): View
    {
        return view('author::show', compact('author'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Author $author): View|JsonResponse
    {
        try {
            return view('author::edit', [
                'method' => 'PUT',
                'action' => route('admin.authors.update', encrypt($author->id)),
                'model' => $author,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage()),
            ]);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(AuthorRequest $request, Author $author): RedirectResponse
    {
        try {
            $data = $request->validated();

            $this->authorRepository->update($data, $author->id);

            // Handle media upload from cropped image
            if (!empty($request['cropped_image'])) {
                // Clear existing media first
                $author->clearMediaCollection('profile_image');

                $croppedImage = $request['cropped_image'];

                // Decode base64 image
                $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $croppedImage));

                // Create temporary file
                $tempFile = tempnam(sys_get_temp_dir(), 'author_image_');
                file_put_contents($tempFile, $imageData);

                try {
                    // Add media from file
                    $author->addMedia($tempFile)
                        ->usingName('Profile Image')
                        ->usingFileName('profile-image.jpg')
                        ->toMediaCollection('profile_image');
                } finally {
                    // Clean up temporary file only if it still exists
                    if (file_exists($tempFile)) {
                        unlink($tempFile);
                    }
                }
            }

            return redirect()->route(self::INDEX_ROUTE)->with([
                'type' => 'success',
                'message' => __('author::author.author_updated_successfully')
            ]);
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => __('author::author.something_wrong')
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Author $author): JsonResponse
    {
        try {
            // Clear all media first
            $author->clearMediaCollection('profile_image');

            $this->authorRepository->delete($author->id);

            return response()->json([
                'type' => 'success',
                'message' => __('author::author.author_deleted_successfully')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'type' => 'error',
                'message' => __('author::author.something_wrong')
            ]);
        }
    }

    /**
     * Change status of author
     */
    public function changeStatus(Request $request): JsonResponse
    {
        try {
            $author = $this->authorRepository->show($request->id);
            $status = !$author->status; // Toggle boolean status

            // If trying to activate author, check if image exists
            if ($status === true && !$author->hasMedia('profile_image')) {
                return response()->json([
                    'type' => 'error',
                    'message' => __('author::author.cannot_activate_without_image')
                ], 422);
            }

            $this->authorRepository->update(['status' => $status], $author->id);

            return response()->json([
                'type' => 'success',
                'message' => __('author::author.status_updated_successfully')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'type' => 'error',
                'message' => __('author::author.something_wrong')
            ]);
        }
    }

    public function bulkAction(Request $request): JsonResponse
    {
        try {
            $ids = $request->id;
            $status = $request->status;
            $actionType = $request->type;
            $result = $this->authorRepository->bulkAction($ids, $status, $actionType);

            return response()->json([
                'status' => $result['type'],
                'message' => $result['message']
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage())
            ]);
        }
    }

    /**
     * Show the CSV import form
     */
    public function showImport(): View|JsonResponse
    {
        try {
            $expectedHeaders = $this->authorImportService->getExpectedHeaders();

            return view('author::import', [
                'action' => route('admin.authors.process-import'),
                'expectedHeaders' => $expectedHeaders
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage()),
            ]);
        }
    }

    /**
     * Process CSV import
     */
    public function processImport(AuthorImportRequest $request): RedirectResponse
    {
        try {
            $file = $request->file('csv_file');
            $results = $this->authorImportService->processImport($file);

            $messageType = $results['error_count'] > 0 ? 'warning' : 'success';
            $message = __('author::author.import_completed', [
                'success' => $results['success_count'],
                'errors' => $results['error_count'],
                'total' => $results['total_rows']
            ]);

            $sessionData = [
                'type' => $messageType,
                'message' => $message
            ];

            // Add detailed errors if any
            if (!empty($results['errors'])) {
                $sessionData['import_errors'] = $results['errors'];
            }

            return redirect()->route(self::INDEX_ROUTE)->with($sessionData);

        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => __('author::author.import_failed', ['error' => $e->getMessage()])
            ]);
        }
    }
}
