<?php

namespace Modules\Author\app\Http\Requests;

use App\Rules\StripTags;
use Illuminate\Foundation\Http\FormRequest;

class AuthorRequest extends FormRequest
{
    protected const SLUG_REGEX = 'regex:/^[^\s]+(\s*[^\s]+)*$/';
    protected const SLUG_UNIQUE_VALIDATION = 'unique:authors,slug';
    protected const STRING_REGEX = 'regex:/^[a-zA-Z0-9\s\-\.]+$/u';
    protected const STRING = 'string';

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $maxSize = 'max:255';
        $rules = [
            // Translatable fields
            'name' => ['required', 'array'],
            'name.en' => ['required', self::STRING, $maxSize],
            'name.ar' => ['nullable', self::STRING, $maxSize],
            'bio' => ['required', 'array'],
            'bio.en' => ['required', self::STRING],
            'bio.ar' => ['nullable', self::STRING],
            'author_type' => ['required', 'array'],
            'author_type.en' => ['required', self::STRING, $maxSize],
            'author_type.ar' => ['nullable', self::STRING, $maxSize],
            // Regular fields
            'status' => ['required', 'boolean'],
            'display_order' => ['nullable', 'integer', 'min:0'],
            'social_media' => ['nullable', 'array'],
            'social_media.facebook' => ['nullable', 'url'],
            'social_media.instagram' => ['nullable', 'url'],
            'social_media.twitter' => ['nullable', 'url'],
            'social_media.linkedin' => ['nullable', 'url'],
            // Image validation - required when status is active
            'cropped_image' => [
                function ($attribute, $value, $fail) {
                    $request = request();
                    $status = $request->input('status');

                    // If status is active (1 or true), image is required
                    if ($status == 1 || $status === true) {
                        // Check if creating new author
                        $routeName = explode('.', request()->route()->getName());
                        $route = array_pop($routeName);

                        if (in_array($route, ['create', 'store'])) {
                            // Creating new author - image is required
                            if (empty($value)) {
                                $fail(__('author::author.profile_image_required_when_active'));
                            }
                        } elseif (in_array($route, ['edit', 'update'])) {
                            // Updating existing author - check if image exists or being uploaded
                            $author = request()->route()->parameter('author');
                            $hasExistingImage = $author && $author->hasMedia('profile_image');

                            if (empty($value) && !$hasExistingImage) {
                                $fail(__('author::author.profile_image_required_when_active'));
                            }
                        }
                    }
                }
            ],
        ];

        $routeName = explode('.', request()->route()->getName());
        $route = array_pop($routeName);
        $slugRules = ['required'];

        switch ($route) {
            case 'create':
            case 'store':
                $rules = array_merge($rules, [
                    'slug' => array_merge($slugRules, [self::SLUG_UNIQUE_VALIDATION]),
                ]);
                break;
            case 'edit':
            case 'update':
                $author = request()->route()->parameter('author');
                $rules = array_merge($rules, [
                    'slug' => array_merge($slugRules, [self::SLUG_UNIQUE_VALIDATION . ',' . $author->id . ',id']),
                ]);
                break;
            default:
                return $rules;
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            // Name validation messages
            'name.array' => __('author::author.name_array'),
            'name.en.required' => __('author::author.name_en_required'),
            'name.en.string' => __('author::author.name_en_string'),
            'name.en.max' => __('author::author.name_en_max'),
            'name.ar.string' => __('author::author.name_ar_string'),
            'name.ar.max' => __('author::author.name_ar_max'),

            // Bio validation messages
            'bio.array' => __('author::author.bio_array'),
            'bio.en.required' => __('author::author.bio_en_required'),
            'bio.en.string' => __('author::author.bio_en_string'),
            'bio.ar.string' => __('author::author.bio_ar_string'),

            // Author type validation messages
            'author_type.array' => __('author::author.author_type_array'),
            'author_type.en.required' => __('author::author.author_type_en_required'),
            'author_type.en.string' => __('author::author.author_type_en_string'),
            'author_type.en.max' => __('author::author.author_type_en_max'),
            'author_type.ar.string' => __('author::author.author_type_ar_string'),
            'author_type.ar.max' => __('author::author.author_type_ar_max'),

            // Other validation messages
            'slug.required' => __('author::author.slug_required'),
            'slug.unique' => __('author::author.slug_unique'),
            'status.required' => __('author::author.status_required'),
            'status.boolean' => __('author::author.status_boolean'),
            'display_order.integer' => __('author::author.display_order_integer'),
            'display_order.min' => __('author::author.display_order_min'),
            'social_media.array' => __('author::author.social_media_array'),
            'social_media.facebook.url' => __('author::author.social_media_url'),
            'social_media.instagram.url' => __('author::author.social_media_url'),
            'social_media.twitter.url' => __('author::author.social_media_url'),
            'social_media.linkedin.url' => __('author::author.social_media_url'),
            'cropped_image.required_if' => __('author::author.profile_image_required_when_active'),
        ];
    }
}
