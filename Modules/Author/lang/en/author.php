<?php

return [
    // General
    'authors' => 'Authors',
    'author' => 'Author',
    'add_author' => 'Add Author',
    'create_author' => 'Create Author',
    'edit_author' => 'Edit Author',
    'update_author' => 'Update Author',
    'view_author' => 'View Author',
    'author_details' => 'Author Details',
    'cancel' => 'Cancel',

    // Form Fields
    'name' => 'Name',
    'name_placeholder' => 'Enter author name',
    'slug' => 'Slug',
    'slug_placeholder' => 'Enter URL slug',
    'bio' => 'Biography',
    'bio_placeholder' => 'Enter author biography',
    'profile_image' => 'Profile Image',
    'current_image' => 'Current Image',
    'social_media' => 'Social Media Links',
    'facebook' => 'Facebook',
    'instagram' => 'Instagram',
    'twitter' => 'Twitter',
    'linkedin' => 'LinkedIn',
    'status' => 'Status',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'display_order' => 'Display Order',
    'display_order_placeholder' => 'Enter display order',
    'created_at' => 'Created At',
    'action' => 'Action',
    'author_type' => 'Author Type',
    'author_type_placeholder' => 'Enter author type',

    // DataGrid Actions
    'activate' => 'Activate',
    'deactivate' => 'Deactivate',
    'delete' => 'Delete',

    // Validation Messages - Required
    'name_array' => 'The name field must be an array.',
    'name_en_required' => 'The name (English) field is required.',
    'name_ar_required' => 'The name (Arabic) field is required.',
    'slug_required' => 'The slug field is required.',
    'status_required' => 'The status field is required.',
    'author_type_en_required' => 'The author type (English) field is required.',
    'author_type_ar_required' => 'The author type (Arabic) field is required.',
    'author_type_array' => 'The author type field must be an array.',
    'bio_en_required' => 'The biography (English) field is required.',
    'bio_ar_required' => 'The biography (Arabic) field is required.',

    // Validation Messages - Format
    'name_en_string' => 'The name (English) must be a string.',
    'name_ar_string' => 'The name (Arabic) must be a string.',
    'name_en_max' => 'The name (English) may not be greater than 255 characters.',
    'name_ar_max' => 'The name (Arabic) may not be greater than 255 characters.',
    'bio_array' => 'The biography field must be an array.',
    'bio_en_string' => 'The biography (English) must be a string.',
    'bio_ar_string' => 'The biography (Arabic) must be a string.',
    'author_type_en_string' => 'The author type (English) must be a string.',
    'author_type_ar_string' => 'The author type (Arabic) must be a string.',
    'author_type_en_max' => 'The author type (English) may not be greater than 255 characters.',
    'author_type_ar_max' => 'The author type (Arabic) may not be greater than 255 characters.',

    // Validation Messages - Other
    'slug_unique' => 'The slug has already been taken.',
    'status_boolean' => 'The status field must be true or false.',
    'display_order_integer' => 'The display order must be an integer.',
    'display_order_min' => 'The display order must be at least 0.',
    'profile_image_mimes' => 'The profile image must be a file of type: jpeg, jpg, png, webp.',
    'profile_image_max' => 'The profile image may not be greater than 10MB.',
    'profile_image_required_when_active' => 'A profile image is required when the author status is set to active.',
    'cannot_activate_without_image' => 'Cannot activate author without a profile image. Please upload an image first.',
    'cannot_activate_authors_without_images' => 'Cannot activate authors without profile images. Please upload images first.',
    'partial_activation_completed' => ':activated authors activated successfully. Could not activate :failed authors without profile images.',
    'social_media_array' => 'The social media field must be an array.',
    'social_media_url' => 'The social media link must be a valid URL.',

    // Success Messages
    'author_created_successfully' => 'Author created successfully.',
    'author_updated_successfully' => 'Author updated successfully.',
    'author_deleted_successfully' => 'Author deleted successfully.',
    'status_updated_successfully' => 'Status updated successfully.',

    // Error Messages
    'something_wrong' => 'Something went wrong. Please try again.',
    'author_not_found' => 'Author not found.',
    'please_select_one_author' => 'Please select at least 1 author.',

    // Not Available
    'not_available' => 'N/A',

    // CSV Import
    'import_csv' => 'Import CSV',
    'import_authors' => 'Import Authors',
    'csv_file' => 'CSV File',
    'csv_file_required' => 'Please select a CSV file to upload.',
    'csv_file_invalid' => 'The uploaded file is not valid.',
    'csv_file_format' => 'The file must be a CSV file (.csv or .txt).',
    'csv_file_size' => 'The file size must not exceed 10MB.',
    'csv_file_help' => 'Upload a CSV file with author data. Maximum file size: 10MB.',
    'import_instructions' => 'Import Instructions',
    'import_instructions_text' => 'Upload a CSV file containing author data. The file must include all required headers in the exact format shown below.',
    'required_csv_headers' => 'Required CSV Headers',
    'import_note_required' => 'Note: name_en and author_type_en are required fields. Other fields are optional.',
    'sample_csv' => 'Sample CSV File',
    'sample_csv_text' => 'Download a sample CSV file with the correct format and example data.',
    'download_sample' => 'Download Sample',
    'importing' => 'Importing...',
    'invalid_file_type' => 'Please select a valid CSV file.',
    'file_too_large' => 'File size exceeds the 10MB limit.',
    'import_completed' => 'Import completed: :success successful, :errors errors out of :total total rows.',
    'import_failed' => 'Import failed: :error',
    'missing_csv_headers' => 'Missing required CSV headers: :headers',
    'import_errors_found' => 'Import completed with some errors',
    'csv_file_empty' => 'The CSV file is empty or could not be read.',
    'csv_no_data_rows' => 'The CSV file contains no data rows.',
    'image_upload_info' => 'Recommended size: 235x350px or higher. Supported formats: JPG, PNG, WebP',
];
