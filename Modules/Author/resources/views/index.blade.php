@extends('admin.layouts.page')
@section('title', __('author::author.authors'))

@section('breadcrumb')
    <ol class="breadcrumb-item active">{{ __('author::author.authors') }}</ol>
@endsection

@permission('create-author')
    @section('action_buttons')
    <div class="d-flex gap-2">
        <a href="{{ route('admin.authors.import') }}" class="btn btn-outline-primary">
            <em class="inic inic-upload"></em> {{ __('author::author.import_csv') }}
        </a>
        <a href="{{ route('admin.authors.create') }}" class="btn btn-primary">
            <em class="inic inic-add"></em> {{ __('author::author.add_author') }}
        </a>
    </div>
    @endsection
@endpermission

@section('content')
    <!-- Display import errors if any -->
    @if(session('import_errors'))
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <h5 class="alert-heading">{{ __('author::author.import_errors_found') }}</h5>
            <ul class="mb-0">
                @foreach(session('import_errors') as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    {!! $dataGridHtml !!}
@endsection

@include('plugins.sweetalert2')
@push('scripts')
<script type="module">
    $(document).on('change','.change-status', function(){
        var id =  $(this).attr('data-id');
        var $checkbox = $(this);

        $.ajax({
            url: "{{ route('admin.authors.change-status')}}",
            type: "post",
            data: {"id": id},
            datatype: 'json',
            success: function (response) {
                toastr.success(response.message);
                window.inic_grid_author.refresh();
            },
            error: function (xhr) {
                // Reset checkbox to previous state
                $checkbox.prop('checked', !$checkbox.prop('checked'));

                // Show error message
                var response = xhr.responseJSON;
                if (response && response.message) {
                    toastr.error(response.message);
                } else {
                    toastr.error('{{ __("author::author.something_wrong") }}');
                }
            }
        });
    });

    let csrfToken = $('meta[name="csrf-token"]').attr('content');
    $(document).on('click', '.delete-grid-row', function (event) {
        event.preventDefault();
        let url = $(this).attr('data-url');
        SwalConfirm(url, function (url) {
            $.ajax({
                url: url,
                type: 'delete',
                datatype: 'json',
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                },
                success: function (response) {
                    toastr.success(response.message);
                    window.inic_grid_author.refresh();
                },
                error: function (response) {
                    toastr.error(response.message);
                }
            });
        })
    })

    $(document).on('click', '.grid-bulk-action', function(event) {
        event.preventDefault();
        var id = [];var error = 0;
        $('.check_approval').each(function(index){
            if($(this).prop('checked') == true){
                id.push($(this).val());
                error = 1;
            }
        });

        var url= "{{ route('admin.authors.bulk-action') }}";
        var status=$(this).data('status');
        var type=$(this).data('action-type');

        if(error == 0){
            toastr.error("{{ __('author::author.please_select_one_author') }}");
        }else if(error == 1){
            SwalConfirm(url, function(url) {
                $.ajax({
                    url: url,
                    type: 'post',
                    data: {id: id,status:status,type:type},
                    success: function(response) {
                        console.log(response);
                        if(response.status == 'success'){
                            toastr.success(response.message);
                        }else if(response.status == 'warning'){
                            toastr.warning(response.message);
                        }else{
                            toastr.error(response.message);
                        }
                        window.inic_grid_author.refresh();
                    },
                    error: function(xhr) {
                        var response = xhr.responseJSON;
                        if (response && response.message) {
                            toastr.error(response.message);
                        } else {
                            toastr.error('{{ __("author::author.something_wrong") }}');
                        }
                        window.inic_grid_author.refresh();
                    }

                });
            });
        }
    });
</script>
@endpush
