@extends('admin.layouts.page')

@section('title', __('author::author.view_author'))

@push('css')
<style>
.author-bio-container {
    max-height: 300px;
    overflow-y: auto;
    word-wrap: break-word;
    line-height: 1.6;
    position: relative;
}

/* Custom scrollbar styling for WebKit browsers (Chrome, Safari, Edge) */
.author-bio-container::-webkit-scrollbar {
    width: 8px;
}

.author-bio-container::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 4px;
}

.author-bio-container::-webkit-scrollbar-thumb {
    background: #dee2e6;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.author-bio-container::-webkit-scrollbar-thumb:hover {
    background: #adb5bd;
}

/* For Firefox */
.author-bio-container {
    scrollbar-width: thin;
    scrollbar-color: #dee2e6 #f8f9fa;
}

/* Fade indicator when content is scrollable */
.author-bio-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(transparent, rgba(248, 249, 250, 0.8));
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.author-bio-container.has-scroll::after {
    opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .author-bio-container {
        max-height: 250px;
    }
}

@media (max-width: 576px) {
    .author-bio-container {
        max-height: 200px;
    }
}
</style>
@endpush

@section('breadcrumb')
    <ol class="breadcrumb-item"><a href="{{ route('admin.authors.index') }}">{{ __('author::author.authors') }}</a></ol>
    <ol class="breadcrumb-item active">{{ __('author::author.view_author') }}</ol>
@endsection

@section('action_buttons')
    {!! backButton(route('admin.authors.index'), 'Back','Author') !!}
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">{{ __('author::author.author_details') }}</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <img src="{{ $author->getProfileImageUrl('preview') }}" alt="{{ $author->getTranslation('name', 'en') }}" class="img-fluid" style="max-width: 300px; max-height: 300px; object-fit: cover;">
                                    @if($author->hasMedia('profile_image'))
                                        <div class="mt-2">
                                            <small class="text-muted">
                                                <i class="fa fa-calendar"></i>
                                                {{ __('admin.common.uploaded') }}: {{ $author->getFirstMedia('profile_image')->created_at->format('M d, Y') }}
                                            </small>
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <div class="col-md-9">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">{{ __('author::author.name') }} (English):</label>
                                        <p>{{ $author->getTranslation('name', 'en') }}</p>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">{{ __('author::author.name') }} (Arabic):</label>
                                        <p dir="rtl">{{ $author->getTranslation('name', 'ar', false) ?: __('author::author.not_available') }}</p>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">{{ __('author::author.author_type') }} (English):</label>
                                        <p>{{ $author->getTranslation('author_type', 'en') }}</p>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">{{ __('author::author.author_type') }} (Arabic):</label>
                                        <p dir="rtl">{{ $author->getTranslation('author_type', 'ar', false) ?: __('author::author.not_available') }}</p>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">{{ __('author::author.slug') }}:</label>
                                        <p>{{ $author->slug }}</p>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">{{ __('author::author.status') }}:</label>
                                        <p>
                                            <span class="badge bg-{{ $author->status ? 'success' : 'warning' }}">
                                                {{ $author->status ? __('author::author.active') : __('author::author.inactive') }}
                                            </span>
                                        </p>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">{{ __('author::author.display_order') }}:</label>
                                        <p>{{ $author->display_order }}</p>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">{{ __('author::author.created_at') }}:</label>
                                        <p>{{ adminDateTimeFormatShow($author->created_at) }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if($author->getTranslation('bio', 'en') || $author->getTranslation('bio', 'ar'))
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5>{{ __('author::author.bio') }}</h5>
                                <div class="row">
                                    @if($author->getTranslation('bio', 'en'))
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">{{ __('author::author.bio') }} (English):</label>
                                        <div class="border p-3 rounded author-bio-container">
                                            {!! $author->getTranslation('bio', 'en') !!}
                                        </div>
                                    </div>
                                    @endif
                                    @if($author->getTranslation('bio', 'ar'))
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">{{ __('author::author.bio') }} (Arabic):</label>
                                        <div class="border p-3 rounded author-bio-container" dir="rtl">
                                            {!! $author->getTranslation('bio', 'ar', false) ?: __('author::author.not_available') !!}
                                        </div>
                                    </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @endif

                        @if($author->social_media && array_filter($author->social_media))
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5>{{ __('author::author.social_media') }}</h5>
                                <div class="row">
                                    @foreach($author->social_media as $platform => $url)
                                        @if($url)
                                        <div class="col-md-3 mb-2">
                                            <a href="{{ $url }}" target="_blank" class="btn btn-outline-primary btn-sm w-100">
                                                <i class="fab fa-{{ $platform }}"></i> {{ ucfirst($platform) }}
                                            </a>
                                        </div>
                                        @endif
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script type="module">
$(document).ready(function() {
    // Check if bio containers need scroll indicators
    $('.author-bio-container').each(function() {
        var container = $(this);

        // Check if content is scrollable
        if (container[0].scrollHeight > container[0].clientHeight) {
            container.addClass('has-scroll');

            // Update fade indicator on scroll
            container.on('scroll', function() {
                var scrollTop = $(this).scrollTop();
                var scrollHeight = $(this)[0].scrollHeight;
                var clientHeight = $(this)[0].clientHeight;

                // Hide fade when scrolled to bottom
                if (scrollTop + clientHeight >= scrollHeight - 5) {
                    $(this).removeClass('has-scroll');
                } else {
                    $(this).addClass('has-scroll');
                }
            });
        }
    });
});
</script>
@endpush
