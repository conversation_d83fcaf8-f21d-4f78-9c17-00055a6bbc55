<form method="POST" action="{{ $action }}" enctype="multipart/form-data">
    @csrf
    @if (isset($model))
        @method('PUT')
    @endif

    <!-- Language Switcher -->
    @include('author::partials.language-switcher')

    <div class="row">
        <!-- Card for translatable fields -->
        <div class="col-md-12 mb-4">
            <div class="card border-primary">
                <div class="card-header bg-primary bg-opacity-10">
                    <h5 class="mb-0"><i class="fa fa-language"></i> {{ __('admin.common.translatable_fields') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Name (translatable) -->
                        <div class="col-md-6 mb-3">
                            <label for="name_en" class="form-label">{{ __('author::author.name') }} <span class="text-danger">*</span></label>
                            <div class="form-group {{ $errors->has('name.en') ? 'has-error' : '' }} translatable-field" data-language="en">
                                <input type="text" name="name[en]" id="name_en" class="form-control"
                                    value="{{ old('name.en', isset($model) ? $model->getTranslation('name', 'en', false) : '') }}"
                                    placeholder="{{ __('author::author.name_placeholder') }}">
                                @if ($errors->has('name.en'))
                                    <span class="help-block">{{ $errors->first('name.en') }}</span>
                                @endif
                            </div>
                            <div class="form-group {{ $errors->has('name.ar') ? 'has-error' : '' }} translatable-field" data-language="ar" style="display: none;">
                                <input type="text" name="name[ar]" id="name_ar" class="form-control" dir="rtl"
                                    value="{{ old('name.ar', isset($model) ? $model->getTranslation('name', 'ar', false) : '') }}"
                                    placeholder="{{ __('author::author.name_placeholder') }}">
                                @if ($errors->has('name.ar'))
                                    <span class="help-block">{{ $errors->first('name.ar') }}</span>
                                @endif
                            </div>
                        </div>

                        <!-- Author Type (translatable) -->
                        <div class="col-md-6 mb-3">
                            <label for="author_type_en" class="form-label">{{ __('author::author.author_type') }} <span class="text-danger">*</span></label>
                            <div class="form-group {{ $errors->has('author_type.en') ? 'has-error' : '' }} translatable-field" data-language="en">
                                <input type="text" name="author_type[en]" id="author_type_en" class="form-control"
                                    value="{{ old('author_type.en', isset($model) ? $model->getTranslation('author_type', 'en', false) : '') }}"
                                    placeholder="{{ __('author::author.author_type_placeholder') }}">
                                @if ($errors->has('author_type.en'))
                                    <span class="help-block">{{ $errors->first('author_type.en') }}</span>
                                @endif
                            </div>
                            <div class="form-group {{ $errors->has('author_type.ar') ? 'has-error' : '' }} translatable-field" data-language="ar" style="display: none;">
                                <input type="text" name="author_type[ar]" id="author_type_ar" class="form-control" dir="rtl"
                                    value="{{ old('author_type.ar', isset($model) ? $model->getTranslation('author_type', 'ar', false) : '') }}"
                                    placeholder="{{ __('author::author.author_type_placeholder') }}">
                                @if ($errors->has('author_type.ar'))
                                    <span class="help-block">{{ $errors->first('author_type.ar') }}</span>
                                @endif
                            </div>
                        </div>

                        <!-- Bio (translatable) -->
                        <div class="col-md-12 mb-3">
                            <label for="bio" class="form-label">{{ __('author::author.bio') }}</label>
                            <div class="translatable-field" data-language="en">
                                <textarea name="bio[en]" id="bio_en" class="form-control ck-editor">{{ old('bio.en', isset($model) ? $model->getTranslation('bio', 'en', false) : '') }}</textarea>
                            </div>
                            <div class="translatable-field" data-language="ar" style="display: none;">
                                <textarea name="bio[ar]" id="bio_ar" class="form-control ck-editor" dir="rtl">{{ old('bio.ar', isset($model) ? $model->getTranslation('bio', 'ar', false) : '') }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card for non-translatable fields -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fa fa-cog"></i> {{ __('admin.common.general_settings') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Slug -->
                        <div class="col-md-6 mb-3">
                            <label for="slug" class="form-label">{{ __('author::author.slug') }} <span class="text-danger">*</span></label>
                            <input type="text" name="slug" id="slug" class="form-control"
                                value="{{ old('slug', $model->slug ?? '') }}"
                                placeholder="{{ __('author::author.slug_placeholder') }}">
                        </div>

                        <!-- Status -->
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">{{ __('author::author.status') }} <span class="text-danger">*</span></label>
                            <select name="status" id="status" class="form-select">
                                <option value="1" {{ old('status', $model->status ?? true) == 1 ? 'selected' : '' }}>
                                    {{ __('author::author.active') }}
                                </option>
                                <option value="0" {{ old('status', $model->status ?? '') == 0 ? 'selected' : '' }}>
                                    {{ __('author::author.inactive') }}
                                </option>
                            </select>
                        </div>

                        <!-- Display Order -->
                        <div class="col-md-6 mb-3">
                            <label for="display_order" class="form-label">{{ __('author::author.display_order') }}</label>
                            <input type="number" name="display_order" id="display_order" class="form-control"
                                value="{{ old('display_order', $model->display_order ?? $nextDisplayOrder) }}"
                                placeholder="{{ __('author::author.display_order_placeholder') }}" min="0">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card for social media links -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fa fa-share-alt"></i> {{ __('author::author.social_media') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Facebook -->
                        <div class="col-md-6 mb-3">
                            <label for="facebook" class="form-label">{{ __('author::author.facebook') }}</label>
                            <input type="url" name="social_media[facebook]" id="facebook" class="form-control"
                                value="{{ old('social_media.facebook', $model->social_media['facebook'] ?? '') }}"
                                placeholder="https://facebook.com/username">
                        </div>

                        <!-- Instagram -->
                        <div class="col-md-6 mb-3">
                            <label for="instagram" class="form-label">{{ __('author::author.instagram') }}</label>
                            <input type="url" name="social_media[instagram]" id="instagram" class="form-control"
                                value="{{ old('social_media.instagram', $model->social_media['instagram'] ?? '') }}"
                                placeholder="https://instagram.com/username">
                        </div>

                        <!-- Twitter -->
                        <div class="col-md-6 mb-3">
                            <label for="twitter" class="form-label">{{ __('author::author.twitter') }}</label>
                            <input type="url" name="social_media[twitter]" id="twitter" class="form-control"
                                value="{{ old('social_media.twitter', $model->social_media['twitter'] ?? '') }}"
                                placeholder="https://twitter.com/username">
                        </div>

                        <!-- LinkedIn -->
                        <div class="col-md-6 mb-3">
                            <label for="linkedin" class="form-label">{{ __('author::author.linkedin') }}</label>
                            <input type="url" name="social_media[linkedin]" id="linkedin" class="form-control"
                                value="{{ old('social_media.linkedin', $model->social_media['linkedin'] ?? '') }}"
                                placeholder="https://linkedin.com/in/username">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card for profile image upload -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fa fa-image"></i> {{ __('admin.common.profile_image') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <!-- Image Preview Container -->
                            <div class="image-upload-container mb-3">
                                <div class="image-preview-wrapper">
                                    {{-- For optimal performance, you can use WebP with fallback:
                                    <picture>
                                        <source srcset="{{ isset($model) ? $model->getProfileImageWebpUrl('preview') : '' }}" type="image/webp">
                                        <img src="{{ isset($model) ? $model->getProfileImageUrl('preview') : asset('images/default-user-img.png') }}"
                                             class="imagePreview img-fluid" id="preview_cropped_image" alt="Profile Image">
                                    </picture>
                                    --}}

                                    <img src="{{ isset($model) ? $model->getProfileImageUrl('preview') : asset('images/default-user-img.png') }}"
                                         class="imagePreview img-fluid rounded" id="preview_cropped_image" alt="Profile Image">
                                </div>

                                <!-- Upload Controls -->
                                <div class="upload-controls mt-3">
                                    <div class="d-flex align-items-center justify-content-center gap-2">
                                        <input type="hidden" name="cropped_image" id="cropped_image">
                                        <input type="file" name="profile_image" id="profile_image" class="image-cropper d-none"
                                               accept="image/png,image/jpg,image/jpeg,image/webp"
                                               data-preview="cropped_image" data-width="235" data-height="350">
                                        <button type="button" class="btn btn-primary btn-sm" onclick="document.getElementById('profile_image').click()">
                                            <i class="fa fa-upload me-1"></i> {{ __('admin.common.upload') }}
                                        </button>
                                    </div>
                                    <div class="mt-2 text-center">
                                        <small class="text-muted">
                                            <i class="fa fa-info-circle"></i>
                                                {{ __('author::author.image_upload_info') }}
                                        </small>
                                    </div>
                                </div>
                            </div>

                            @if(isset($model) && $model->hasMedia('profile_image'))
                                <div class="alert alert-info alert-sm">
                                    <i class="fa fa-info-circle"></i>
                                    {{ __('admin.common.current_image_will_be_replaced') }}
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form buttons -->
        <div class="col-md-12">
            <div class="d-flex align-items-center justify-content-end gap-3">
                <button type="submit" class="btn btn-primary">{{ __('admin.common.submit') }}</button>
                <a href="{{ route('admin.authors.index') }}" class="btn btn-secondary">{{ __('admin.common.cancel') }}</a>
            </div>
        </div>
    </div>
</form>

@include('plugins.ckeditor')
@include('plugins.cropper')

{{-- Include shared image upload styles and scripts --}}
@push('css')
    <link rel="stylesheet" href="{{ asset('css/admin-image-upload.css') }}">
@endpush

@push('scripts')
    <script src="{{ asset('js/admin-image-upload.js') }}" defer></script>
@endpush

@push('scripts')
    @php
        $validator = JsValidator::formRequest('Modules\Author\app\Http\Requests\AuthorRequest');
        $validator->ignore('');
    @endphp
    {!! $validator !!}

    <script type="module">
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize CKEditors for both languages
            initializeCKEditor('bio_en', options);

            // Set up Arabic CKEditor with RTL direction when needed
            const arOptions = Object.assign({}, options, {
                contentsLangDirection: 'rtl'
            });
            initializeCKEditor('bio_ar', arOptions);

            // Hide the Arabic editor initially
            if (typeof CKEDITOR !== 'undefined') {
                if (CKEDITOR.instances.bio_ar && CKEDITOR.instances.bio_ar.container) {
                    CKEDITOR.instances.bio_ar.container.setStyle('display', 'none');
                }
            }

            // Initialize slug generation for author
            initializeSlugGeneration('#name_en', '#slug');

            // Image validation when status changes
            initializeImageValidation();
        });

            function initializeImageValidation() {
            const statusSelect = document.getElementById('status');
            const croppedImageInput = document.getElementById('cropped_image');
            const imageUploadContainer = $('.image-upload-container');
            const profileImageInput = document.getElementById('profile_image');

            // Check if editing existing author with image
            const hasExistingImage = @json(isset($model) && $model->hasMedia('profile_image'));

            // Track if user has interacted with status field
            let statusChanged = false;

            function validateImageRequirement(showMessage = true) {
                const status = statusSelect.value;
                const hasImage = croppedImageInput.value || hasExistingImage;

                // Remove any existing validation messages
                $('.image-validation-message').remove();
                imageUploadContainer.removeClass('border-danger');

                if (status == '1' && !hasImage) {
                    if (showMessage) {
                        // Show validation message
                        const message = '<div class="alert alert-danger alert-sm image-validation-message mt-2">' +
                            '<i class="fa fa-exclamation-triangle"></i> ' +
                            '{{ __("author::author.profile_image_required_when_active") }}' +
                            '</div>';
                        imageUploadContainer.after(message);
                        imageUploadContainer.addClass('border-danger');
                    }
                    return false;
                }

                return true;
            }

            // Validate on status change
            if (statusSelect) {
                statusSelect.addEventListener('change', function() {
                    statusChanged = true;
                    validateImageRequirement(true);
                });
            }

            // Validate when image is uploaded/cropped (only if status was changed)
            if (croppedImageInput) {
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                            // Only show validation if user has interacted with status
                            validateImageRequirement(statusChanged);
                        }
                    });
                });

                observer.observe(croppedImageInput, {
                    attributes: true,
                    attributeFilter: ['value']
                });

                // Also listen for input event
                croppedImageInput.addEventListener('input', function() {
                    // Only show validation if user has interacted with status
                    validateImageRequirement(statusChanged);
                });
            }

            // Validate on form submission (always validate on submit)
            $('form').on('submit', function(e) {
                if (!validateImageRequirement(true)) {
                    e.preventDefault();

                    // Scroll to validation message
                    $('html, body').animate({
                        scrollTop: $('.image-validation-message').offset().top - 100
                    }, 500);

                    return false;
                }
            });
        }
    </script>
@endpush
