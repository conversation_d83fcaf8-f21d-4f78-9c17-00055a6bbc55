<?php

namespace Modules\FAQ\app\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Modules\FAQ\Database\factories\FAQFactory;
use Spatie\Translatable\HasTranslations;

class FAQ extends Model
{
    use HasFactory, HasTranslations;

    /**
     * The attributes that are translatable.
     *
     * @var array
     */
    public $translatable = ['question', 'answer'];

    /**
     * The default locale to use for translations.
     *
     * @return string
     */
    public function getDefaultLocale(): string
    {
        return 'en';
    }

    /**
     * The table associated with the model.
     *
     * @var string
     */

    protected $table = "faqs";

    protected $fillable = [
        'question',
        'answer',
        'category',
        'tags',
        'priority',
        'status',
    ];

    /**
     * Get the category that owns the FAQ.
     */
    public function category()
    {
        return $this->belongsTo(\Modules\FAQCategory\app\Models\FAQCategory::class, 'category');
    }

    public function resolveRouteBinding($value, $field = null): self
    {
        return $this->findOrFail(decrypt($value));
    }
}
