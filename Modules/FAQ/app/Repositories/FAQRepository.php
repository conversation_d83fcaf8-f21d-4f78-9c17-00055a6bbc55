<?php

namespace Modules\FAQ\app\Repositories;

use App\Repositories\Admin\Repository;
use Modules\FAQ\app\Models\FAQ;

class FAQRepository extends Repository
{
    /**
     * FAQRepository constructor.
     * @param FAQ $model
     */
    public function __construct(FAQ $model)
    {
        $this->model = $model;
    }

    public function bulkAction($ids): array
    {
        $type = 'success';

        foreach ($ids as $id) {
            $this->model->where('id', $id)->delete();
        }
        $message = __('FAQ::admin.messages.delete');
        
        return [
            'type' => $type,
            'message' => $message
        ];
    }
    
}
