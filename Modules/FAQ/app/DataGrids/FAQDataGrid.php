<?php

namespace Modules\FAQ\app\DataGrids;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Indianic\LaravelDataGrid\LaravelDataGrid;
use Modules\FAQCategory\app\Models\FAQCategory;
use Modules\FAQ\app\Models\FAQ;

class FAQDataGrid extends LaravelDataGrid
{
    public $guard = 'admin';
    public $sno = 1;
    public const QUESTION_TRANS = "FAQ::admin.datagrid.question";
    public const ANSWER_TRANS = "FAQ::admin.datagrid.answer";
    public const CATEGORY_TRANS = "FAQ::admin.datagrid.category";

    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'FAQ';

    /**
     * Define how many rows you want to display on a page
     * @var int $rowPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define row per page dropdown options
     * @var array $recordsPerPageOptions
     */
    public $recordsPerPageOptions = [10, 25, 50];

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy;

    /**
     * Define default soring direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection;

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'faq-lits';

    /**
     * Get Resource of Query Builder
     */
    public function resource()
    {
        return FAQ::select(
            'faqs.id',
            'faq_categories.category',
            DB::raw("JSON_EXTRACT(faq_categories.category, '$.en') as category"),
            DB::raw("JSON_EXTRACT(faqs.question, '$.en') as question"),
            DB::raw("JSON_EXTRACT(faqs.answer, '$.en') as answer")
        )
        ->join('faq_categories', 'faq_categories.id', '=', 'faqs.category');
    }

    public function columns(): array
    {
        return [
            'checkbox' => '<input class="select_all checkbox" type="checkbox" />',
            'sno' => __('#'),
            'question' => __(self::QUESTION_TRANS),
            'answer' => __(self::ANSWER_TRANS),
            'category' => __(self::CATEGORY_TRANS),
            'action' => __('FAQ::admin.datagrid.actions'),
        ];
    }

    public function mapDBColumns(): array
    {
        return [
            'question' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(faqs.question, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'answer' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(faqs.answer, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'category' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(faq_categories.category, '$.en')) COLLATE utf8mb4_unicode_ci")
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'sno',
            'question',
            'answer',
            'category',
        ];
    }

    // public function downloadableColumns(): array
    // {
    //     return [
    //         'question' => __(self::QUESTION_TRANS),
    //         'answer' => __(self::ANSWER_TRANS),
    //         'category' => __(self::CATEGORY_TRANS),
    //     ];
    // }

    // public function getAdvanceSearchOptions(): array
    // {
    //     return [
    //         [
    //             'id' => lcfirst(trans(self::QUESTION_TRANS)),
    //             'label' => __(self::QUESTION_TRANS),
    //             'type' => 'string',
    //         ],
    //         [
    //             'id' => lcfirst(trans(self::ANSWER_TRANS)),
    //             'label' => __(self::ANSWER_TRANS),
    //             'type' => 'string',
    //         ],
    //         [
    //             'id' => lcfirst(trans(self::CATEGORY_TRANS)),
    //             'label' => __(self::CATEGORY_TRANS),
    //             'type' => 'string',
    //         ],
    //     ];
    // }

    // public function filters(): array
    // {
    //     return [
    //         'category' => [
    //             'title' => __(self::CATEGORY_TRANS),
    //             'data' => FAQCategory::pluck('category')->all(),
    //             'width' => '250',
    //             'type' => 'array',
    //         ],
    //     ];
    // }

    public function getindex(): array
    {
        return [];
    }

    public function gloablSearchableColumns(): array
    {
        return [
            'faqs.question->en' => 'string',
            'faqs.question->ar' => 'string',
            'faqs.answer->en' => 'string',
            'faqs.answer->ar' => 'string',
            'faq_categories.category->en' => 'string',
            'faq_categories.category->ar' => 'string',
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'faqs.question->en' => 'string',
            'faqs.question->ar' => 'string',
            'faqs.answer->en' => 'string',
            'faqs.answer->ar' => 'string',
            'faq_categories.category->en' => 'string',
            'faq_categories.category->ar' => 'string',
        ];
    }

    public function getBulkAction(): array
    {
        return [
            'Delete' => [
                'class' => 'danger',
                'title' => trans("FAQ::admin.form_label.delete"),
                'module' => 'FAQ',
                'type' => 'delete'
            ],
        ];
    }

    public function getColumnAction(array $data): string
    {
        $return = '';
        $user = Auth::user();

        if ($user->hasPermission('update-faqs')) {
            $return = '<a class="me-3" href="' . route('admin.faqs.edit', encrypt($data['id'])) . '">
                <span class="inic inic-edit fs-18" data-bs-toggle="tooltip" title="Edit"></span>
            </a>';
        }

        if ($user->hasPermission('delete-faqs')) {
            $return .= '<a class="delete-grid-row" href="javascript:void(0)"
                data-grid="inic_grid_' . $this->uniqueID . '"
                data-url="' . route('admin.faqs.destroy', encrypt($data['id'])) . '">
                    <span class="inic inic-bin fs-18" data-bs-toggle="tooltip" title="Delete"></span>
            </a>';
        }
        if (empty($return)) {
            $return = '<a class="me-3" href="#"><span class="inic inic-edit fs-18"></span></a>';
            $return .= '<a class="me-3" href="#"><span class="inic inic-bin fs-18"></span></a>';
        }

        return $return;
    }

    public function getColumnCheckbox(array $data): string
    {
        return '<input type="checkbox" value="' . $data['id'] . '" class="check_approval" />';
    }

    public function getColumnQuestion(array $data): string
    {
        $question = $data['question'] ?? '';
        $question = trim($question, '"');
        $question = strlen($question) > 50 ? substr($question, 0, 50) . '...' : $question;
        return $question;
    }

    public function getColumnAnswer(array $data): string
    {
        $answer = $data['answer'] ?? '';
        $answer = trim($answer, '"');
        $answer = strlen($answer) > 50 ? substr($answer, 0, 50) . '...' : $answer;
        return $answer;
    }

    public function getColumnSno(): string
    {
        return $this->sno++;
    }

    public function getColumnCategory(array $data): string
    {
        $category = $data['category'] ?? '';
        $category = trim($category, '"');
        return $category;
    }

    public function getDownloadColumnTitle(array $data): string
    {
        return $data['title'];
    }
}
