@extends('admin.layouts.page')
@section('title', __('FAQ::admin.faq_title'))
@section('breadcrumb')
<ol class="breadcrumb-item"><a href="{{ Route::has('admin.dashboard') ? route('admin.dashboard') : '#' }}">{{
        __('admin.common.home') }}</a></ol>
<ol class="breadcrumb-item active">{{ __('FAQ::admin.faq_title') }}</ol>
@endsection

@permission('create-faqs')
@section('action_buttons')
<a href="{{ Route::has('admin.faqs.create') ? route('admin.faqs.create') :'#' }}" class="btn btn-primary"><em
        class="inic inic-add"></em>  {{ __('FAQ::admin.faq_create') }}</a>
@endsection
@endpermission

@section('content')
{!! $dataGridHtml !!}
@endsection

@push('scripts')

<script type="module">
    $(document).on('click', '.delete-grid-row', function (event) {
        event.preventDefault();
        let url = $(this).attr('data-url');
        SwalConfirm(url, function (url) {
            $.ajax({
                url: url,
                type: 'delete',
                datatype: 'json',
                success: function (response) {
                    toastr.success(response.message);
                    window.inic_grid_FAQ.refresh();
                },
                error: function (response) {
                    toastr.error(response.message);
                }
            });
        })
    })

    $(document).on('click', '#bulkDelete', function (event) {
        event.preventDefault();
        var id = []; var error = 0;
        $('.check_approval').each(function (index) {
            if ($(this).prop('checked') == true) {
                id.push($(this).val());
                error = 1;
            }
        });

        var url = "{{ route('admin.faqs.bulkAction')}}";

        if (error == 0) {
            toastr.error("{{ __('FAQ::admin.messages.select_one_faq') }}");
        } else if (error == 1) {
            SwalConfirm(url, function (url) {
                $.ajax({
                    url: url,
                    type: 'post',
                    data: {id: id},
                    success: function (response) {
                        window.inic_grid_FAQ.refresh();
                        toastr.success(response.message);
                    },
                    error: function (response) {
                        toastr.error(response.message);
                    }
                });
            });
        }
    });
</script>
@endpush
