<form method="POST" action="{{ $action }}" enctype="multipart/form-data">
    @csrf
    @if (isset($model))
        @method('PUT')
    @endif

    <!-- Language Switcher -->
    @include('FAQ::partials.language-switcher')

    <div class="row">
        <!-- Card for translatable fields -->
        <div class="col-md-12 mb-4">
            <div class="card border-primary" style="border-width: 2px;">
                <div class="card-header bg-primary bg-opacity-10">
                    <h5 class="mb-0"><i class="fa fa-language"></i> {{ __('admin.common.translatable_fields') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Question (translatable) -->
                        <div class="col-md-12 mb-3">
                            <label for="question_en" class="form-label">{{ __('FAQ::admin.form_label.question') }}</label>
                            <div class="form-group {{ $errors->has('question.en') ? 'has-error' : '' }} translatable-field" data-language="en">
                                <input type="text" name="question[en]" id="question_en" class="form-control"
                                    value="{{ old('question.en', isset($model) ? $model->getTranslation('question', 'en', false) : '') }}"
                                    placeholder="{{ __('FAQ::admin.placeholder.question') }}">
                                @if ($errors->has('question.en'))
                                    <span class="help-block">{{ $errors->first('question.en') }}</span>
                                @endif
                            </div>
                            <div class="form-group {{ $errors->has('question.ar') ? 'has-error' : '' }} translatable-field" data-language="ar" style="display: none;">
                                <input type="text" name="question[ar]" id="question_ar" class="form-control" dir="rtl"
                                    value="{{ old('question.ar', isset($model) ? $model->getTranslation('question', 'ar', false) : '') }}"
                                    placeholder="{{ __('FAQ::admin.placeholder.question') }}">
                                @if ($errors->has('question.ar'))
                                    <span class="help-block">{{ $errors->first('question.ar') }}</span>
                                @endif
                            </div>
                        </div>

                        <!-- Answer (translatable) -->
                        <div class="col-md-12 mb-3">
                            <label for="answer" class="form-label">{{ __('FAQ::admin.form_label.answer') }}</label>
                            <div class="translatable-field" data-language="en">
                                <textarea name="answer[en]" id="answer_en" class="form-control ck-editor">{{ old('answer.en', isset($model) ? $model->getTranslation('answer', 'en', false) : '') }}</textarea>
                            </div>
                            <div class="translatable-field" data-language="ar" style="display: none;">
                                <textarea name="answer[ar]" id="answer_ar" class="form-control ck-editor" dir="rtl">{{ old('answer.ar', isset($model) ? $model->getTranslation('answer', 'ar', false) : '') }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card for non-translatable fields -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fa fa-cog"></i> {{ __('admin.common.general_settings') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Category -->
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label">{{ __('FAQ::admin.form_label.category') }}</label>
                            <select name="category" id="category" class="form-select">
                                <option value="">{{ __('FAQ::admin.placeholder.category') }}</option>
                                @foreach($faqCategory as $key => $category)
                                    <option value="{{ $key}}" {{ old('category', $model->category ?? '') == $key ? 'selected' : '' }}>
                                        {{ $category }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form buttons -->
        <div class="col-md-12">
            <div class="d-flex align-items-center justify-content-end gap-3">
                <button type="submit" class="btn btn-primary">{{ __('admin.common.submit') }}</button>
                <a href="{{ route('admin.faqs.index') }}" class="btn btn-secondary">{{ __('admin.common.cancel') }}</a>
            </div>
        </div>
    </div>
</form>

@include('plugins.ckeditor')

@push('scripts')
    @php
        $validator = JsValidator::formRequest('Modules\FAQ\app\Http\Requests\FAQRequest');
    @endphp
    {!! $validator !!}

    <script type="module">
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize CKEditors for both languages
            initializeCKEditor('answer_en', options);

            // Set up Arabic CKEditor with RTL direction when needed
            const arOptions = Object.assign({}, options, {
                contentsLangDirection: 'rtl'
            });
            initializeCKEditor('answer_ar', arOptions);

            // Hide the Arabic editor initially
            if (typeof CKEDITOR !== 'undefined') {
                if (CKEDITOR.instances.answer_ar && CKEDITOR.instances.answer_ar.container) {
                    CKEDITOR.instances.answer_ar.container.setStyle('display', 'none');
                }
            }
        });
    </script>
@endpush
