<?php

namespace Modules\CriticalAcclaim\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\Author\app\Repositories\AuthorRepository;
use Modules\CriticalAcclaim\app\DataGrids\CriticalAcclaimDataGrid;
use Modules\CriticalAcclaim\app\Http\Requests\CriticalAcclaimRequest;
use Modules\CriticalAcclaim\app\Http\Requests\CriticalAcclaimImportRequest;
use Modules\CriticalAcclaim\app\Services\CriticalAcclaimImportService;
use Modules\CriticalAcclaim\app\Models\CriticalAcclaim;
use Modules\CriticalAcclaim\app\Repositories\CriticalAcclaimRepository;

class CriticalAcclaimController extends Controller
{
    public const INDEX_ROUTE = "admin.critical-acclaims.index";

    /**
     * CriticalAcclaimController constructor.
     * @param CriticalAcclaimRepository $criticalAcclaimRepository
     * @param AuthorRepository $authorRepository
     * @param CriticalAcclaimImportService $criticalAcclaimImportService
     */
    public function __construct(
        protected CriticalAcclaimRepository $criticalAcclaimRepository,
        protected AuthorRepository $authorRepository,
        protected CriticalAcclaimImportService $criticalAcclaimImportService
    ) {
        $this->middleware('permission:create-critical-acclaim', ['only' => ['create', 'store', 'showImport', 'processImport']]);
        $this->middleware('permission:update-critical-acclaim', ['only' => ['edit', 'update']]);
        $this->middleware(
            'permission:read-critical-acclaim|create-critical-acclaim|update-critical-acclaim|delete-critical-acclaim',
            ['only' => ['index']]
        );
        $this->middleware('permission:delete-critical-acclaim', ['only' => ['destroy']]);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(): View|RedirectResponse
    {
        try {
            $dataGridHtml = CriticalAcclaimDataGrid::getHTML();
            return view('criticalacclaim::index', compact('dataGridHtml'));
        } catch (\Exception $e) {
            return redirect()->back()->with(['type' => 'error', 'message' => __($e->getMessage())]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View|JsonResponse
    {
        try {
            $authors = $this->authorRepository->getAuthors();

            return view('criticalacclaim::create', [
                'method' => 'POST',
                'authors' => $authors,
                'action' => route('admin.critical-acclaims.store'),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage()),
            ]);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(CriticalAcclaimRequest $request): RedirectResponse
    {
        try {
            $data = $request->validated();

            $this->criticalAcclaimRepository->create($data);

            return redirect()
                ->route(self::INDEX_ROUTE)
                ->with([
                    'type' => 'success',
                    'message' => __('criticalacclaim::criticalacclaim.critical_acclaim_add_successfully'),
                ]);
        } catch (\Exception $e) {
            return redirect()->back()->with([
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Show the specified resource.
     */
    public function show(CriticalAcclaim $criticalAcclaim): View
    {
        $criticalAcclaim->load('author');
        return view('criticalacclaim::show', ['model' => $criticalAcclaim]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CriticalAcclaim $criticalAcclaim): View|RedirectResponse
    {
        try {
            $authors = $this->authorRepository->getAuthors();

            return view('criticalacclaim::edit', [
                'is_update' => true,
                'model' => $criticalAcclaim,
                'authors' => $authors,
                'method' => 'PUT',
                'action' => route('admin.critical-acclaims.update', encrypt($criticalAcclaim->id)),
            ]);
        } catch (\Exception $e) {
            return redirect()->back()->with([
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(CriticalAcclaimRequest $request, CriticalAcclaim $criticalAcclaim): RedirectResponse
    {
        try {
            $data = $request->validated();

            $this->criticalAcclaimRepository->update($data, $criticalAcclaim->id);

            return redirect()
                ->route(self::INDEX_ROUTE)
                ->with([
                    'type' => 'success',
                    'message' => __('criticalacclaim::criticalacclaim.critical_acclaim_update_successfully'),
                ]);
        } catch (\Exception $e) {
            return redirect()->back()->with([
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CriticalAcclaim $criticalAcclaim): JsonResponse
    {
        try {
            $this->criticalAcclaimRepository->delete($criticalAcclaim->id);

            return response()->json([
                'type' => 'success',
                'message' => __('criticalacclaim::criticalacclaim.critical_acclaim_delete_successfully')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle bulk actions
     */
    public function bulkAction(Request $request): JsonResponse
    {
        try {
            $ids = $request->id;
            $status = $request->status;
            $actionType = $request->type;

            if (empty($ids)) {
                return response()->json([
                    'type' => 'error',
                    'message' => __('criticalacclaim::criticalacclaim.please_select_one_critical_acclaim')
                ]);
            }

            $result = $this->criticalAcclaimRepository->bulkAction($ids, $status, $actionType);

            return response()->json([
                'status' => $result['type'],
                'message' => $result['message']
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * Show the CSV import form
     */
    public function showImport(): View|JsonResponse
    {
        try {
            $expectedHeaders = $this->criticalAcclaimImportService->getExpectedHeaders();

            return view('criticalacclaim::import', [
                'action' => route('admin.critical-acclaims.process-import'),
                'expectedHeaders' => $expectedHeaders
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage()),
            ]);
        }
    }

    /**
     * Process CSV import
     */
    public function processImport(CriticalAcclaimImportRequest $request): RedirectResponse
    {
        try {
            $file = $request->file('csv_file');
            $results = $this->criticalAcclaimImportService->processImport($file);

            $messageType = $results['error_count'] > 0 ? 'warning' : 'success';
            $message = __('criticalacclaim::criticalacclaim.import_completed', [
                'success' => $results['success_count'],
                'errors' => $results['error_count'],
                'total' => $results['total_rows']
            ]);

            $sessionData = [
                'type' => $messageType,
                'message' => $message
            ];

            // Add detailed errors if any
            if (!empty($results['errors'])) {
                $sessionData['import_errors'] = $results['errors'];
            }

            return redirect()->route(self::INDEX_ROUTE)->with($sessionData);

        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => __('criticalacclaim::criticalacclaim.import_failed', ['error' => $e->getMessage()])
            ]);
        }
    }
}
