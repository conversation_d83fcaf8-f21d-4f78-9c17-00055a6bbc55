<form action="{{ $action }}" method="POST">
    @csrf
    @if (isset($model))
        @method('PUT')
    @endif

    @include('criticalacclaim::partials.language-switcher')

    <!-- Translatable Fields Card -->
    <div class="card border-primary" style="border-width: 2px;">
        <div class="card-header bg-primary bg-opacity-10">
            <h5 class="card-title mb-0">
                <i class="fa fa-language me-2"></i>{{ __('admin.common.translatable_fields') }}
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- Heading -->
                <div class="col-md-6 mb-3">
                    <label for="heading_en" class="form-label">{{ __('criticalacclaim::criticalacclaim.heading') }}</label>
                    <div class="form-group translatable-field" data-language="en">
                        <input type="text" id="heading_en" name="heading[en]"
                               value="{{ old('heading.en', isset($model) ? $model->getTranslation('heading', 'en', false) : '') }}"
                               class="form-control @error('heading.en') is-invalid @enderror"
                               placeholder="{{ __('criticalacclaim::criticalacclaim.heading_placeholder') }}">
                        @error('heading.en')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group translatable-field" data-language="ar" style="display: none;">
                        <input type="text" id="heading_ar" name="heading[ar]" dir="rtl"
                               value="{{ old('heading.ar', isset($model) ? $model->getTranslation('heading', 'ar', false) : '') }}"
                               class="form-control @error('heading.ar') is-invalid @enderror"
                               placeholder="{{ __('criticalacclaim::criticalacclaim.heading_placeholder') }}">
                        @error('heading.ar')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Sub Heading -->
                <div class="col-md-6 mb-3">
                    <label for="sub_heading_en" class="form-label">{{ __('criticalacclaim::criticalacclaim.sub_heading') }}</label>
                    <div class="form-group translatable-field" data-language="en">
                        <input type="text" id="sub_heading_en" name="sub_heading[en]"
                               value="{{ old('sub_heading.en', isset($model) ? $model->getTranslation('sub_heading', 'en', false) : '') }}"
                               class="form-control @error('sub_heading.en') is-invalid @enderror"
                               placeholder="{{ __('criticalacclaim::criticalacclaim.sub_heading_placeholder') }}">
                        @error('sub_heading.en')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group translatable-field" data-language="ar" style="display: none;">
                        <input type="text" id="sub_heading_ar" name="sub_heading[ar]" dir="rtl"
                               value="{{ old('sub_heading.ar', isset($model) ? $model->getTranslation('sub_heading', 'ar', false) : '') }}"
                               class="form-control @error('sub_heading.ar') is-invalid @enderror"
                               placeholder="{{ __('criticalacclaim::criticalacclaim.sub_heading_placeholder') }}">
                        @error('sub_heading.ar')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Summary -->
                <div class="col-md-12 mb-3">
                    <label for="summary_en" class="form-label">{{ __('criticalacclaim::criticalacclaim.summary') }}</label>
                    <div class="form-group translatable-field ck-editor" data-language="en">
                        <textarea id="summary_en" name="summary[en]" rows="4"
                                  class="form-control @error('summary.en') is-invalid @enderror"
                                  placeholder="{{ __('criticalacclaim::criticalacclaim.summary_placeholder') }}">{{ old('summary.en', isset($model) ? $model->getTranslation('summary', 'en', false) : '') }}</textarea>
                        @error('summary.en')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group translatable-field ck-editor" data-language="ar" style="display: none;">
                        <textarea id="summary_ar" name="summary[ar]" dir="rtl" rows="4"
                                  class="form-control @error('summary.ar') is-invalid @enderror"
                                  placeholder="{{ __('criticalacclaim::criticalacclaim.summary_placeholder') }}">{{ old('summary.ar', isset($model) ? $model->getTranslation('summary', 'ar', false) : '') }}</textarea>
                        @error('summary.ar')
                            <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- General Settings Card -->
    <div class="card mt-3">
        <div class="card-header bg-light">
            <h5 class="card-title mb-0">
                <i class="fa fa-cog me-2"></i>{{ __('admin.common.general_settings') }}
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- Author -->
                <div class="col-md-12 mb-3">
                    <label for="author_id" class="form-label">{{ __('criticalacclaim::criticalacclaim.author') }}</label>
                    <select id="author_id" name="author_id" class="form-select @error('author_id') is-invalid @enderror">
                        <option value="">{{ __('criticalacclaim::criticalacclaim.select_author') }}</option>
                        @foreach($authors as $author)
                            <option value="{{ $author->id }}" {{ old('author_id', $model->author_id ?? '') == $author->id ? 'selected' : '' }}>
                                {{ $author->getTranslation('name', 'en') }}
                            </option>
                        @endforeach
                    </select>
                    @error('author_id')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>
        </div>
    </div>

    <!-- Form Actions -->
    <div class="d-flex align-items-center justify-content-end gap-3 mt-3">
        <button type="submit" class="btn btn-primary">{{ __('admin.common.submit') }}</button>
        <a href="{{ route('admin.critical-acclaims.index') }}" class="btn btn-secondary">{{ __('admin.common.cancel') }}</a>
    </div>
</form>

@include('plugins.ckeditor')

@push('scripts')
@php
$validator = JsValidator::formRequest('Modules\CriticalAcclaim\app\Http\Requests\CriticalAcclaimRequest');
$validator->ignore('');
@endphp
{!! $validator !!}

<script type="module">
     document.addEventListener('DOMContentLoaded', () => {
        // Initialize CKEditors for both languages
        initializeCKEditor('summary_en', options);

        // Set up Arabic CKEditor with RTL direction when needed
        const arOptions = { ...options, contentsLangDirection: 'rtl' };
        initializeCKEditor('summary_ar', arOptions);

        // Hide the Arabic editor initially
        if (typeof CKEDITOR !== 'undefined') {
            if (CKEDITOR.instances.summary_ar && CKEDITOR.instances.summary_ar.container) {
                CKEDITOR.instances.summary_ar.container.setStyle('display', 'none');
            }
        }
    });
</script>

@endpush

