<form method="POST" action="{{ $action }}" enctype="multipart/form-data">
    @csrf
    @if (isset($model))
        @method('PUT')
    @endif

    <!-- Language Switcher -->
    @include('book::partials.language-switcher')

    <div class="row">
        <!-- Card for translatable fields -->
        <div class="col-md-12 mb-4">
            <div class="card border-primary">
                <div class="card-header bg-primary bg-opacity-10">
                    <h5 class="mb-0"><i class="fa fa-language"></i> {{ __('admin.common.translatable_fields') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Title (translatable) -->
                        <div class="col-md-6 mb-3">
                            <label for="title_en" class="form-label">{{ __('book::book.title') }} <span class="text-danger">*</span></label>
                            <div class="form-group {{ $errors->has('title.en') ? 'has-error' : '' }} translatable-field" data-language="en">
                                <input type="text" name="title[en]" id="title_en" class="form-control"
                                    value="{{ old('title.en', isset($model) ? $model->getTranslation('title', 'en', false) : '') }}"
                                    placeholder="{{ __('book::book.title_placeholder') }}">
                                @if ($errors->has('title.en'))
                                    <span class="help-block">{{ $errors->first('title.en') }}</span>
                                @endif
                            </div>
                            <div class="form-group {{ $errors->has('title.ar') ? 'has-error' : '' }} translatable-field" data-language="ar" style="display: none;">
                                <input type="text" name="title[ar]" id="title_ar" class="form-control" dir="rtl"
                                    value="{{ old('title.ar', isset($model) ? $model->getTranslation('title', 'ar', false) : '') }}"
                                    placeholder="{{ __('book::book.title_placeholder') }}">
                                @if ($errors->has('title.ar'))
                                    <span class="help-block">{{ $errors->first('title.ar') }}</span>
                                @endif
                            </div>
                        </div>

                        <!-- Subtitle (translatable) -->
                        <div class="col-md-6 mb-3">
                            <label for="subtitle_en" class="form-label">{{ __('book::book.subtitle') }}</label>
                            <div class="form-group {{ $errors->has('subtitle.en') ? 'has-error' : '' }} translatable-field" data-language="en">
                                <input type="text" name="subtitle[en]" id="subtitle_en" class="form-control"
                                    value="{{ old('subtitle.en', isset($model) ? $model->getTranslation('subtitle', 'en', false) : '') }}"
                                    placeholder="{{ __('book::book.subtitle_placeholder') }}">
                                @if ($errors->has('subtitle.en'))
                                    <span class="help-block">{{ $errors->first('subtitle.en') }}</span>
                                @endif
                            </div>
                            <div class="form-group {{ $errors->has('subtitle.ar') ? 'has-error' : '' }} translatable-field" data-language="ar" style="display: none;">
                                <input type="text" name="subtitle[ar]" id="subtitle_ar" class="form-control" dir="rtl"
                                    value="{{ old('subtitle.ar', isset($model) ? $model->getTranslation('subtitle', 'ar', false) : '') }}"
                                    placeholder="{{ __('book::book.subtitle_placeholder') }}">
                                @if ($errors->has('subtitle.ar'))
                                    <span class="help-block">{{ $errors->first('subtitle.ar') }}</span>
                                @endif
                            </div>
                        </div>

                        <!-- Genre (translatable) -->
                        <div class="col-md-6 mb-3">
                            <label for="genre_en" class="form-label">{{ __('book::book.genre') }}</label>
                            <div class="form-group {{ $errors->has('genre.en') ? 'has-error' : '' }} translatable-field" data-language="en">
                                <input type="text" name="genre[en]" id="genre_en" class="form-control"
                                    value="{{ old('genre.en', isset($model) ? $model->getTranslation('genre', 'en', false) : '') }}"
                                    placeholder="{{ __('book::book.genre_placeholder') }}">
                                @if ($errors->has('genre.en'))
                                    <span class="help-block">{{ $errors->first('genre.en') }}</span>
                                @endif
                            </div>
                            <div class="form-group {{ $errors->has('genre.ar') ? 'has-error' : '' }} translatable-field" data-language="ar" style="display: none;">
                                <input type="text" name="genre[ar]" id="genre_ar" class="form-control" dir="rtl"
                                    value="{{ old('genre.ar', isset($model) ? $model->getTranslation('genre', 'ar', false) : '') }}"
                                    placeholder="{{ __('book::book.genre_placeholder') }}">
                                @if ($errors->has('genre.ar'))
                                    <span class="help-block">{{ $errors->first('genre.ar') }}</span>
                                @endif
                            </div>
                        </div>

                        <!-- Key Theme Title (translatable) -->
                        <div class="col-md-6 mb-3">
                            <label for="key_theme_title_en" class="form-label">{{ __('book::book.key_theme_title') }}</label>
                            <div class="form-group {{ $errors->has('key_theme_title.en') ? 'has-error' : '' }} translatable-field" data-language="en">
                                <input type="text" name="key_theme_title[en]" id="key_theme_title_en" class="form-control"
                                    value="{{ old('key_theme_title.en', isset($model) ? $model->getTranslation('key_theme_title', 'en', false) : '') }}"
                                    placeholder="{{ __('book::book.key_theme_title_placeholder') }}">
                                @if ($errors->has('key_theme_title.en'))
                                    <span class="help-block">{{ $errors->first('key_theme_title.en') }}</span>
                                @endif
                            </div>
                            <div class="form-group {{ $errors->has('key_theme_title.ar') ? 'has-error' : '' }} translatable-field" data-language="ar" style="display: none;">
                                <input type="text" name="key_theme_title[ar]" id="key_theme_title_ar" class="form-control" dir="rtl"
                                    value="{{ old('key_theme_title.ar', isset($model) ? $model->getTranslation('key_theme_title', 'ar', false) : '') }}"
                                    placeholder="{{ __('book::book.key_theme_title_placeholder') }}">
                                @if ($errors->has('key_theme_title.ar'))
                                    <span class="help-block">{{ $errors->first('key_theme_title.ar') }}</span>
                                @endif
                            </div>
                        </div>

                        <!-- Description (translatable) -->
                        <div class="col-md-12 mb-3">
                            <label for="description" class="form-label">{{ __('book::book.description') }}</label>
                            <div class="translatable-field" data-language="en">
                                <textarea name="description[en]" id="description_en" class="form-control ck-editor">{{ old('description.en', isset($model) ? $model->getTranslation('description', 'en', false) : '') }}</textarea>
                            </div>
                            <div class="translatable-field" data-language="ar" style="display: none;">
                                <textarea name="description[ar]" id="description_ar" class="form-control ck-editor" dir="rtl">{{ old('description.ar', isset($model) ? $model->getTranslation('description', 'ar', false) : '') }}</textarea>
                            </div>
                        </div>

                        <!-- Why This Book (translatable) -->
                        <div class="col-md-12 mb-3">
                            <label for="why_this_book" class="form-label">{{ __('book::book.why_this_book') }}</label>
                            <div class="translatable-field" data-language="en">
                                <textarea name="why_this_book[en]" id="why_this_book_en" class="form-control ck-editor">{{ old('why_this_book.en', isset($model) ? $model->getTranslation('why_this_book', 'en', false) : '') }}</textarea>
                            </div>
                            <div class="translatable-field" data-language="ar" style="display: none;">
                                <textarea name="why_this_book[ar]" id="why_this_book_ar" class="form-control ck-editor" dir="rtl">{{ old('why_this_book.ar', isset($model) ? $model->getTranslation('why_this_book', 'ar', false) : '') }}</textarea>
                            </div>
                        </div>

                        <!-- Key Theme Description (translatable) -->
                        <div class="col-md-12 mb-3">
                            <label for="key_theme_description" class="form-label">{{ __('book::book.key_theme_description') }}</label>
                            <div class="translatable-field" data-language="en">
                                <textarea name="key_theme_description[en]" id="key_theme_description_en" class="form-control ck-editor">{{ old('key_theme_description.en', isset($model) ? $model->getTranslation('key_theme_description', 'en', false) : '') }}</textarea>
                            </div>
                            <div class="translatable-field" data-language="ar" style="display: none;">
                                <textarea name="key_theme_description[ar]" id="key_theme_description_ar" class="form-control ck-editor" dir="rtl">{{ old('key_theme_description.ar', isset($model) ? $model->getTranslation('key_theme_description', 'ar', false) : '') }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card for non-translatable fields -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fa fa-cog"></i> {{ __('admin.common.general_settings') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Slug -->
                        <div class="col-md-6 mb-3">
                            <label for="slug" class="form-label">{{ __('book::book.slug') }} <span class="text-danger">*</span></label>
                            <input type="text" name="slug" id="slug" class="form-control"
                                value="{{ old('slug', $model->slug ?? '') }}"
                                placeholder="{{ __('book::book.slug_placeholder') }}">
                        </div>

                        <!-- Status -->
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">{{ __('book::book.status') }} <span class="text-danger">*</span></label>
                            <select name="status" id="status" class="form-select">
                                <option value="published" {{ old('status', $model->status ?? 'published') == 'published' ? 'selected' : '' }}>
                                    {{ __('book::book.published') }}
                                </option>
                                <option value="upcoming" {{ old('status', $model->status ?? '') == 'upcoming' ? 'selected' : '' }}>
                                    {{ __('book::book.upcoming') }}
                                </option>
                                <option value="archived" {{ old('status', $model->status ?? '') == 'archived' ? 'selected' : '' }}>
                                    {{ __('book::book.archived') }}
                                </option>
                                <option value="draft" {{ old('status', $model->status ?? '') == 'draft' ? 'selected' : '' }}>
                                    {{ __('book::book.draft') }}
                                </option>
                            </select>
                        </div>

                        <!-- Author -->
                        <div class="col-md-6 mb-3">
                            <label for="author_id" class="form-label">{{ __('book::book.author') }} <span class="text-danger">*</span></label>
                            <select name="author_id" id="author_id" class="form-select">
                                <option value="">{{ __('book::book.select_author') }}</option>
                                @foreach($authors as $author)
                                    <option value="{{ $author->id }}" {{ old('author_id', $model->author_id ?? '') == $author->id ? 'selected' : '' }}>
                                        {{ $author->getTranslation('name', 'en') }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Publisher -->
                        <div class="col-md-6 mb-3">
                            <label for="publisher_id" class="form-label">{{ __('book::book.publisher') }}</label>
                            <select name="publisher_id" id="publisher_id" class="form-select">
                                <option value="">{{ __('book::book.select_publisher') }}</option>
                                @foreach($publishers as $publisher)
                                    <option value="{{ $publisher->id }}" {{ old('publisher_id', $model->publisher_id ?? '') == $publisher->id ? 'selected' : '' }}>
                                        {{ $publisher->getTranslation('name', 'en') }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Illustrator -->
                        <div class="col-md-6 mb-3">
                            <label for="illustrator_id" class="form-label">{{ __('book::book.illustrator') }}</label>
                            <select name="illustrator_id" id="illustrator_id" class="form-select">
                                <option value="">{{ __('book::book.select_illustrator') }}</option>
                                @foreach($illustrators as $illustrator)
                                    <option value="{{ $illustrator->id }}" {{ old('illustrator_id', $model->illustrator_id ?? '') == $illustrator->id ? 'selected' : '' }}>
                                        {{ $illustrator->getTranslation('name', 'en') }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Pages -->
                        <div class="col-md-6 mb-3">
                            <label for="pages" class="form-label">{{ __('book::book.pages') }}</label>
                            <input type="number" name="pages" id="pages" class="form-control"
                                value="{{ old('pages', $model->pages ?? '') }}"
                                placeholder="{{ __('book::book.pages_placeholder') }}" min="1">
                        </div>

                        <!-- Year Published -->
                        <div class="col-md-6 mb-3">
                            <label for="year_published" class="form-label">{{ __('book::book.year_published') }}</label>
                            <input type="text" name="year_published" id="year_published" class="form-control"
                                value="{{ old('year_published', $model->year_published ?? '') }}"
                                placeholder="{{ __('book::book.year_published_placeholder') }}" maxlength="4">
                        </div>

                        <!-- Display Order -->
                        <div class="col-md-6 mb-3">
                            <label for="display_order" class="form-label">{{ __('book::book.display_order') }}</label>
                            <input type="number" name="display_order" id="display_order" class="form-control"
                                value="{{ old('display_order', $model->display_order ?? $nextDisplayOrder) }}"
                                placeholder="{{ __('book::book.display_order_placeholder') }}" min="0">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card for cover image upload -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fa fa-image"></i> {{ __('admin.common.cover_image') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <!-- Image Preview Container -->
                            <div class="image-upload-container mb-3">
                                <div class="image-preview-wrapper">
                                    {{-- For optimal performance, you can use WebP with fallback:
                                    <picture>
                                        <source srcset="{{ isset($model) ? $model->getCoverImageWebpUrl('preview') : '' }}" type="image/webp">
                                        <img src="{{ isset($model) ? $model->getCoverImageUrl('preview') : asset('images/banner-img.png') }}"
                                             class="imagePreview img-fluid" id="preview_cropped_image" alt="Book Cover">
                                    </picture>
                                    --}}

                                    <img src="{{ isset($model) ? $model->getCoverImageUrl('preview') : asset('images/banner-img.png') }}"
                                         class="imagePreview img-fluid rounded" id="preview_cropped_image" alt="Book Cover">
                                </div>

                                <!-- Upload Controls -->
                                <div class="upload-controls mt-3">
                                    <div class="d-flex align-items-center justify-content-center gap-2">
                                        <input type="hidden" name="cropped_image" id="cropped_image">
                                        <input type="file" name="cover_image" id="cover_image" class="image-cropper d-none"
                                               accept="image/png,image/jpg,image/jpeg,image/webp"
                                               data-preview="cropped_image" data-width="235" data-height="350">
                                        <button type="button" class="btn btn-primary btn-sm" onclick="document.getElementById('cover_image').click()">
                                            <i class="fa fa-upload me-1"></i> {{ __('admin.common.upload') }}
                                        </button>
                                    </div>
                                    <div class="mt-2 text-center">
                                        <small class="text-muted">
                                            <i class="fa fa-info-circle"></i>
                                            {{ __('book::book.image_upload_info') }}
                                        </small>
                                    </div>
                                </div>
                            </div>

                            @if(isset($model) && $model->hasMedia('cover_image'))
                                <div class="alert alert-info alert-sm">
                                    <i class="fa fa-info-circle"></i>
                                    {{ __('admin.common.current_image_will_be_replaced') }}
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form buttons -->
        <div class="col-md-12">
            <div class="d-flex align-items-center justify-content-end gap-3">
                <button type="submit" class="btn btn-primary">{{ __('admin.common.submit') }}</button>
                <a href="{{ route('admin.books.index') }}" class="btn btn-secondary">{{ __('admin.common.cancel') }}</a>
            </div>
        </div>
    </div>
</form>

@include('plugins.ckeditor')
@include('plugins.cropper')

{{-- Include shared image upload styles and scripts --}}
@push('css')
    <link rel="stylesheet" href="{{ asset('css/admin-image-upload.css') }}">
@endpush

@push('scripts')
    <script src="{{ asset('js/admin-image-upload.js') }}" defer></script>
@endpush

@push('scripts')
    @php
        $validator = JsValidator::formRequest('Modules\Book\app\Http\Requests\BookRequest');
        $validator->ignore('');
    @endphp
    {!! $validator !!}

    <script type="module">
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize CKEditors for both languages
            initializeCKEditor('description_en', options);
            initializeCKEditor('why_this_book_en', options);
            initializeCKEditor('key_theme_description_en', options);

            // Set up Arabic CKEditor with RTL direction when needed
            const arOptions = Object.assign({}, options, {
                contentsLangDirection: 'rtl'
            });
            initializeCKEditor('description_ar', arOptions);
            initializeCKEditor('why_this_book_ar', arOptions);
            initializeCKEditor('key_theme_description_ar', arOptions);

            // Hide the Arabic editor initially
            if (typeof CKEDITOR !== 'undefined') {
                if (CKEDITOR.instances.description_ar && CKEDITOR.instances.description_ar.container &&
                    CKEDITOR.instances.why_this_book_ar && CKEDITOR.instances.why_this_book_ar.container &&
                    CKEDITOR.instances.key_theme_description_ar && CKEDITOR.instances.key_theme_description_ar.container
                ) {
                    CKEDITOR.instances.description_ar.container.setStyle('display', 'none');
                    CKEDITOR.instances.why_this_book_ar.container.setStyle('display', 'none');
                    CKEDITOR.instances.key_theme_description_ar.container.setStyle('display', 'none');
                }
            }

            // Initialize slug generation for book
            initializeSlugGeneration('#title_en', '#slug');

            // Image validation when status changes
            initializeImageValidation();
        });

        function initializeImageValidation() {
            const statusSelect = document.getElementById('status');
            const croppedImageInput = document.getElementById('cropped_image');
            const imageUploadContainer = $('.image-upload-container');
            const coverImageInput = document.getElementById('cover_image');

            // Check if editing existing book with image
            const hasExistingImage = @json(isset($model) && $model->hasMedia('cover_image'));

            // Track if user has interacted with status field
            let statusChanged = false;

            function validateImageRequirement(showMessage = true) {
                const status = statusSelect.value;
                const hasImage = croppedImageInput.value || hasExistingImage;

                // Remove any existing validation messages
                $('.image-validation-message').remove();
                imageUploadContainer.removeClass('border-danger');

                if (status == 'published' && !hasImage) {
                    if (showMessage) {
                        // Show validation message
                        const message = '<div class="alert alert-danger alert-sm image-validation-message mt-2">' +
                            '<i class="fa fa-exclamation-triangle"></i> ' +
                            '{{ __("book::book.cover_image_required_when_published") }}' +
                            '</div>';
                        imageUploadContainer.after(message);
                        imageUploadContainer.addClass('border-danger');
                    }
                    return false;
                }

                return true;
            }

            // Validate on status change
            if (statusSelect) {
                statusSelect.addEventListener('change', function() {
                    statusChanged = true;
                    validateImageRequirement(true);
                });
            }

            // Validate when image is uploaded/cropped (only if status was changed)
            if (croppedImageInput) {
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                            // Only show validation if user has interacted with status
                            validateImageRequirement(statusChanged);
                        }
                    });
                });

                observer.observe(croppedImageInput, {
                    attributes: true,
                    attributeFilter: ['value']
                });

                // Also listen for input event
                croppedImageInput.addEventListener('input', function() {
                    // Only show validation if user has interacted with status
                    validateImageRequirement(statusChanged);
                });
            }

            // Validate on form submission (always validate on submit)
            $('form').on('submit', function(e) {
                if (!validateImageRequirement(true)) {
                    e.preventDefault();

                    // Scroll to validation message
                    $('html, body').animate({
                        scrollTop: $('.image-validation-message').offset().top - 100
                    }, 500);

                    return false;
                }
            });
        }
    </script>
@endpush
