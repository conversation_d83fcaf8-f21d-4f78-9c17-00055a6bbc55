<?php

namespace Modules\Book\app\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Modules\Book\app\Http\Requests\BookRequest;
use Modules\Book\app\Http\Requests\BookImportRequest;
use Modules\Book\app\Repositories\BookRepository;
use Modules\Book\app\Services\BookImportService;
use Modules\Book\app\DataGrids\BookDataGrid;
use Modules\Author\app\Repositories\AuthorRepository;
use Modules\Book\Models\Book;
use Modules\Publisher\app\Repositories\PublisherRepository;
use Modules\Illustrator\app\Repositories\IllustratorRepository;

class BookController extends Controller
{
    protected const INDEX_ROUTE = 'admin.books.index';

    protected $bookRepository;
    protected $authorRepository;
    protected $publisherRepository;
    protected $illustratorRepository;
    protected $bookImportService;

    public function __construct(
        BookRepository $bookRepository,
        AuthorRepository $authorRepository,
        PublisherRepository $publisherRepository,
        IllustratorRepository $illustratorRepository,
        BookImportService $bookImportService
    ) {
        $this->bookRepository = $bookRepository;
        $this->authorRepository = $authorRepository;
        $this->publisherRepository = $publisherRepository;
        $this->illustratorRepository = $illustratorRepository;
        $this->bookImportService = $bookImportService;
        $this->middleware('permission:create-book', ['only' => ['create', 'store', 'showImport', 'processImport']]);
        $this->middleware('permission:update-book', ['only' => ['edit', 'update', 'changeStatus']]);
        $this->middleware(
            'permission:read-book|create-book|update-book|delete-book',
            ['only' => ['index']]
        );
        $this->middleware('permission:delete-book', ['only' => ['destroy']]);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(): View|RedirectResponse
    {
        try {
            $dataGridHtml = BookDataGrid::getHTML();
            return view('book::index', compact('dataGridHtml'));
        } catch (\Exception $e) {
            return redirect()->back()->with(['type' => 'error', 'message' => __($e->getMessage())]);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View|JsonResponse
    {
        try {
            $authors = $this->authorRepository->getAuthors();
            $publishers = $this->publisherRepository->getActivePublishers();
            $illustrators = $this->illustratorRepository->getActiveIllustrators();
            $nextDisplayOrder = $this->bookRepository->getNextDisplayOrder();
            return view('book::create', [
                'method' => 'POST',
                'action' => route('admin.books.store'),
                'authors' => $authors,
                'publishers' => $publishers,
                'illustrators' => $illustrators,
                'nextDisplayOrder' => $nextDisplayOrder,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage()),
            ]);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(BookRequest $request): RedirectResponse
    {
        try {
            $data = $request->validated();

            // Set default display order if not provided
            if (!isset($data['display_order']) || $data['display_order'] === null) {
                $maxOrder = $this->bookRepository->getModel()->max('display_order');
                $data['display_order'] = $maxOrder ? $maxOrder + 1 : 1;
            }

            $book = $this->bookRepository->create($data);

            // Handle media upload from cropped image
            if (!empty($request['cropped_image'])) {
                $croppedImage = $request['cropped_image'];

                // Decode base64 image
                $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $croppedImage));

                // Create temporary file
                $tempFile = tempnam(sys_get_temp_dir(), 'book_image_');
                file_put_contents($tempFile, $imageData);

                try {
                    // Add media from file
                    $book->addMedia($tempFile)
                        ->usingName('Cover Image')
                        ->usingFileName('cover-image.jpg')
                        ->toMediaCollection('cover_image');
                } finally {
                    // Clean up temporary file only if it still exists
                    if (file_exists($tempFile)) {
                        unlink($tempFile);
                    }
                }
            }

            return redirect()->route(self::INDEX_ROUTE)->with([
                'type' => 'success',
                'message' => __('book::book.book_created_successfully')
            ]);
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => __('book::book.something_wrong')
            ]);
        }
    }

    /**
     * Show the specified resource.
     */
    public function show($id): View
    {
        $book = $this->bookRepository->show(decrypt($id));
        $book->load('author');
        return view('book::show', compact('book'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id): View|JsonResponse
    {
        try {
            $book = $this->bookRepository->show(decrypt($id));
            $authors = $this->authorRepository->getAuthors();
            $publishers = $this->publisherRepository->getActivePublishers();
            $illustrators = $this->illustratorRepository->getActiveIllustrators();
            return view('book::edit', [
                'method' => 'PUT',
                'action' => route('admin.books.update', encrypt($book->id)),
                'model' => $book,
                'authors' => $authors,
                'publishers' => $publishers,
                'illustrators' => $illustrators,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage()),
            ]);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(BookRequest $request, $id): RedirectResponse
    {
        try {
            $book = $this->bookRepository->show(decrypt($id));
            $data = $request->validated();

            $this->bookRepository->update($data, $book->id);

            // Handle media upload from cropped image
            if (!empty($request['cropped_image'])) {
                // Clear existing media first
                $book->clearMediaCollection('cover_image');

                $croppedImage = $request['cropped_image'];

                // Decode base64 image
                $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $croppedImage));

                // Create temporary file
                $tempFile = tempnam(sys_get_temp_dir(), 'book_image_');
                file_put_contents($tempFile, $imageData);

                try {
                    // Add media from file
                    $book->addMedia($tempFile)
                        ->usingName('Cover Image')
                        ->usingFileName('cover-image.jpg')
                        ->toMediaCollection('cover_image');
                } finally {
                    // Clean up temporary file only if it still exists
                    if (file_exists($tempFile)) {
                        unlink($tempFile);
                    }
                }
            }

            return redirect()->route(self::INDEX_ROUTE)->with([
                'type' => 'success',
                'message' => __('book::book.book_updated_successfully')
            ]);
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => __('book::book.something_wrong')
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Book $book): JsonResponse
    {
        try {
            // Clear all media first
            $book->clearMediaCollection('cover_image');

            $this->bookRepository->delete($book->id);

            return response()->json([
                'type' => 'success',
                'message' => __('book::book.book_deleted_successfully')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'type' => 'error',
                'message' => __('book::book.something_wrong')
            ]);
        }
    }

    /**
     * Handle bulk actions
     */
    public function bulkAction(Request $request): JsonResponse
    {
        try {
            $ids = $request->id;
            $status = $request->status;
            $actionType = $request->type;
            $result = $this->bookRepository->bulkAction($ids, $status, $actionType);

            return response()->json([
                'status' => $result['type'],
                'message' => $result['message']
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage())
            ]);
        }
    }

    /**
     * Change status of a book
     */
    public function changeStatus(Request $request): JsonResponse
    {
        try {
            $id = decrypt($request->input('id'));
            $status = $request->input('status');

            // If trying to publish book, check if image exists
            if ($status === 'published') {
                $book = $this->bookRepository->show($id);
                if (!$book->hasMedia('cover_image')) {
                    return response()->json([
                        'type' => 'error',
                        'message' => __('book::book.cannot_publish_without_cover_image')
                    ], 422);
                }
            }

            $this->bookRepository->update(['status' => $status], $id);

            return response()->json([
                'type' => 'success',
                'message' => __('book::book.status_updated_successfully')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'type' => 'error',
                'message' => __('book::book.something_wrong')
            ]);
        }
    }

    /**
     * Show the CSV import form
     */
    public function showImport(): View|JsonResponse
    {
        try {
            $expectedHeaders = $this->bookImportService->getExpectedHeaders();

            return view('book::import', [
                'action' => route('admin.books.process-import'),
                'expectedHeaders' => $expectedHeaders
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage()),
            ]);
        }
    }

    /**
     * Process CSV import
     */
    public function processImport(BookImportRequest $request): RedirectResponse
    {
        try {
            $file = $request->file('csv_file');
            $results = $this->bookImportService->processImport($file);

            $messageType = $results['error_count'] > 0 ? 'warning' : 'success';
            $message = __('book::book.import_completed', [
                'success' => $results['success_count'],
                'errors' => $results['error_count'],
                'total' => $results['total_rows']
            ]);

            $sessionData = [
                'type' => $messageType,
                'message' => $message
            ];

            // Add detailed errors if any
            if (!empty($results['errors'])) {
                $sessionData['import_errors'] = $results['errors'];
            }

            return redirect()->route(self::INDEX_ROUTE)->with($sessionData);

        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => __('book::book.import_failed', ['error' => $e->getMessage()])
            ]);
        }
    }
}
