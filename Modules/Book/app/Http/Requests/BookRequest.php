<?php

namespace Modules\Book\app\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class BookRequest extends FormRequest
{
    protected const SLUG_REGEX = 'regex:/^[^\s]+(\s*[^\s]+)*$/';
    protected const SLUG_UNIQUE_VALIDATION = 'unique:books,slug';
    protected const STRING_REGEX = 'regex:/^[a-zA-Z0-9\s\-\.]+$/u';
    protected const STRING = 'string';

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $maxSize = 'max:255';
        $rules = [
            // Translatable fields
            'title' => ['required', 'array'],
            'title.en' => ['required', self::STRING, $maxSize],
            'title.ar' => ['nullable', self::STRING, $maxSize],
            'subtitle' => ['required', 'array'],
            'subtitle.en' => ['required', self::STRING, $maxSize],
            'subtitle.ar' => ['nullable', self::STRING, $maxSize],
            'description' => ['required', 'array'],
            'description.en' => ['required', self::STRING],
            'description.ar' => ['nullable', self::STRING],
            'genre' => ['required', 'array'],
            'genre.en' => ['required', self::STRING, $maxSize],
            'genre.ar' => ['nullable', self::STRING, $maxSize],
            'why_this_book' => ['required', 'array'],
            'why_this_book.en' => ['required', self::STRING],
            'why_this_book.ar' => ['nullable', self::STRING],
            'key_theme_title' => ['required', 'array'],
            'key_theme_title.en' => ['required', self::STRING],
            'key_theme_title.ar' => ['nullable', self::STRING],
            'key_theme_description' => ['nullable', 'array'],
            'key_theme_description.en' => ['required', self::STRING],
            'key_theme_description.ar' => ['nullable', self::STRING],

            // Regular fields
            'author_id' => ['required', 'integer', 'exists:authors,id'],
            'publisher_id' => ['nullable', 'integer', 'exists:publishers,id'],
            'illustrator_id' => ['nullable', 'integer', 'exists:illustrators,id'],
            'pages' => ['nullable', 'integer', 'min:1'],
            'year_published' => ['nullable', 'string', 'max:10'],
            'status' => ['required', 'in:published,upcoming,archived,draft'],
            'display_order' => ['nullable', 'integer', 'min:0'],
            // Image validation - required when status is published
            'cropped_image' => [
                function ($attribute, $value, $fail) {
                    $request = request();
                    $status = $request->input('status');

                    // If status is published, image is required
                    if ($status === 'published') {
                        // Check if creating new book
                        $routeName = explode('.', request()->route()->getName());
                        $route = array_pop($routeName);

                        if (in_array($route, ['create', 'store'])) {
                            // Creating new book - image is required
                            if (empty($value)) {
                                $fail(__('book::book.cover_image_required_when_published'));
                            }
                        } elseif (in_array($route, ['edit', 'update'])) {
                            // Updating existing book - check if image exists or being uploaded
                            $bookId = decrypt(request()->route()->parameter('book'));
                            $book = \Modules\Book\Models\Book::find($bookId);
                            $hasExistingImage = $book && $book->hasMedia('cover_image');

                            if (empty($value) && !$hasExistingImage) {
                                $fail(__('book::book.cover_image_required_when_published'));
                            }
                        }
                    }
                }
            ],
        ];

        $routeName = explode('.', request()->route()->getName());
        $route = array_pop($routeName);
        $slugRules = ['required'];

        switch ($route) {
            case 'create':
            case 'store':
                $rules = array_merge($rules, [
                    'slug' => array_merge($slugRules, [self::SLUG_UNIQUE_VALIDATION]),
                ]);
                break;
            case 'edit':
            case 'update':
                $book = request()->route()->parameter('book');
                $bookId = decrypt($book);
                $rules = array_merge($rules, [
                    'slug' => array_merge($slugRules, [self::SLUG_UNIQUE_VALIDATION . ',' . $bookId . ',id']),
                ]);
                break;
            default:
                return $rules;
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            // Title validation messages
            'title.array' => __('book::book.title_array'),
            'title.en.required' => __('book::book.title_en_required'),
            'title.en.string' => __('book::book.title_string'),
            'title.en.max' => __('book::book.title_max'),
            'title.ar.string' => __('book::book.title_string'),
            'title.ar.max' => __('book::book.title_max'),

            // Subtitle validation messages
            'subtitle.array' => __('book::book.subtitle_array'),
            'subtitle.en.required' => __('book::book.subtitle_en_required'),
            'subtitle.en.string' => __('book::book.subtitle_string'),
            'subtitle.en.max' => __('book::book.subtitle_max'),
            'subtitle.ar.string' => __('book::book.subtitle_string'),
            'subtitle.ar.max' => __('book::book.subtitle_max'),

            // Description validation messages
            'description.array' => __('book::book.description_array'),
            'description.en.required' => __('book::book.description_en_required'),
            'description.en.string' => __('book::book.description_string'),
            'description.ar.string' => __('book::book.description_string'),

            // Genre validation messages
            'genre.array' => __('book::book.genre_array'),
            'genre.en.required' => __('book::book.genre_en_required'),
            'genre.en.string' => __('book::book.genre_string'),
            'genre.en.max' => __('book::book.genre_max'),
            'genre.ar.string' => __('book::book.genre_string'),
            'genre.ar.max' => __('book::book.genre_max'),

            // Why this book validation messages
            'why_this_book.array' => __('book::book.why_this_book_array'),
            'why_this_book.en.required' => __('book::book.why_this_book_en_required'),
            'why_this_book.en.string' => __('book::book.why_this_book_string'),
            'why_this_book.ar.string' => __('book::book.why_this_book_string'),

            // Key theme validation messages
            'key_theme_title.array' => __('book::book.key_theme_title_array'),
            'key_theme_title.en.required' => __('book::book.key_theme_title_en_required'),
            'key_theme_title.en.string' => __('book::book.key_theme_title_string'),
            'key_theme_title.ar.string' => __('book::book.key_theme_title_string'),
            'key_theme_description.array' => __('book::book.key_theme_description_array'),
            'key_theme_description.en.required' => __('book::book.key_theme_description_en_required'),
            'key_theme_description.en.string' => __('book::book.key_theme_description_string'),
            'key_theme_description.ar.string' => __('book::book.key_theme_description_string'),

            // Other validation messages
            'slug.required' => __('book::book.slug_required'),
            'slug.unique' => __('book::book.slug_unique'),
            'author_id.required' => __('book::book.author_required'),
            'author_id.integer' => __('book::book.author_integer'),
            'author_id.exists' => __('book::book.author_exists'),
            'publisher_id.integer' => __('book::book.publisher_integer'),
            'publisher_id.exists' => __('book::book.publisher_exists'),
            'illustrator_id.integer' => __('book::book.illustrator_integer'),
            'illustrator_id.exists' => __('book::book.illustrator_exists'),
            'status.required' => __('book::book.status_required'),
            'status.in' => __('book::book.status_invalid'),
            'pages.integer' => __('book::book.pages_integer'),
            'pages.min' => __('book::book.pages_min'),
            'display_order.integer' => __('book::book.display_order_integer'),
            'display_order.min' => __('book::book.display_order_min'),
            'year_published.string' => __('book::book.year_published_string'),
            'year_published.max' => __('book::book.year_published_max'),
            'cropped_image.required_if' => __('book::book.cover_image_required_when_published'),
        ];
    }
}
