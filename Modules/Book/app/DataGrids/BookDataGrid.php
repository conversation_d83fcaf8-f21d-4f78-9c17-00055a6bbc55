<?php

namespace Modules\Book\app\DataGrids;

use Indianic\LaravelDataGrid\LaravelDataGrid;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Vite;
use Modules\Book\Models\Book;

class BookDataGrid extends LaravelDataGrid
{
    public $guard = 'admin';

    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'books';

    /**
     * Define how many rows you want to display on a page
     * @var int $recordsPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define row per page dropdown options
     * @var array $recordsPerPageOptions
     */
    public $recordsPerPageOptions;

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy = 'display_order';

    /**
     * Define default sorting direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection = 'DESC';

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'books';

    // DataGrid constants
    public const TITLE = 'book::book.title';
    public const AUTHOR = 'book::book.author';
    public const STATUS = 'book::book.status';
    public const DISPLAY_ORDER = 'book::book.display_order';
    public const CREATED_AT = 'book::book.created_at';
    public const ACTION = 'book::book.action';

    public function resource()
    {
        return Book::select(
            'books.id',
            'books.slug',
            'books.status',
            'books.display_order',
            'books.created_at as book_created_at',
            DB::raw("JSON_EXTRACT(books.title, '$.en') as title"),
            DB::raw("JSON_EXTRACT(authors.name, '$.en') as author_name")
        )->leftJoin('authors', 'books.author_id', '=', 'authors.id')->with('media');
    }

    public function columns(): array
    {
        return [
            'checkbox' => '<input class="select_all checkbox" type="checkbox" />',
            'title' => __(self::TITLE),
            'author_name' => __(self::AUTHOR),
            'status' => __(self::STATUS),
            'display_order' => __(self::DISPLAY_ORDER),
            'book_created_at' => __(self::CREATED_AT),
            'action' => __(self::ACTION)
        ];
    }

    public function mapDBColumns(): array
    {
        return [
            'title' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(books.title, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'author_name' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(authors.name, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'status' => 'books.status',
            'book_created_at' => 'books.created_at'
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'title',
            'author_name',
            'status',
            'display_order',
            'book_created_at'
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'title' => 'string',
            'author_name' => 'string',
            'status' => 'string',
            'book_created_at' => 'string'
        ];
    }

    public function gloablSearchableColumns(): array
    {
        return [
            'title' => 'string',
            'author_name' => 'string',
            'status' => 'string',
            'book_created_at' => 'string'
        ];
    }

    public function getBulkAction(): array
    {
        return [
            'Published' => [
                'class' => 'success',
                'title' => __('book::book.published'),
                'module' => 'Book',
                'type' => 'publish'
            ],
            'Archived' => [
                'class' => 'warning',
                'title' => __('book::book.archived'),
                'module' => 'Book',
                'type' => 'archive'
            ],
            'Draft' => [
                'class' => 'primary',
                'title' => __('book::book.draft'),
                'module' => 'Book',
                'type' => 'draft'
            ],
            'Delete' => [
                'class' => 'danger',
                'title' => __('book::book.delete'),
                'module' => 'Book',
                'type' => 'delete'
            ],
        ];
    }

    public function getColumnTitle(array $data): string
    {
        $book = Book::find($data['id']);

        $url = $book->getCoverImageUrl('thumbnail');

        $title = $data['title'] ?? '';
        $title = trim($title, '"');
        $title = strlen($title) > 40 ? substr($title, 0, 40) . '...' : $title;

        return '<div class="d-flex align-items-center">
                    <img class="me-2 rounded" src="' . $url . '" alt="' . $title . '" width="40" height="40" style="object-fit: cover;">
                    <div>
                        <a href="javascript:;" class="text-black fw-bold">' . $title . '</a>
                        <br><small class="text-muted">' . $data['slug'] . '</small>
                    </div>
                </div>';
    }

    public function getColumnAuthorName(array $data): string
    {
        // Get the author name, stripping JSON quotes if present
        $authorName = $data['author_name'] ?? '';
        $authorName = trim($authorName, '"');

        return $authorName ?? __('book::book.no_author');
    }

    public function getColumnStatus(array $data): string
    {
        $statusClass = match($data['status']) {
            'published' => 'success',
            'upcoming' => 'warning',
            'archived' => 'secondary',
            'draft' => 'info',
            default => 'secondary'
        };

        $statusLabel = match($data['status']) {
            'published' => __('book::book.published'),
            'upcoming' => __('book::book.upcoming'),
            'archived' => __('book::book.archived'),
            'draft' => __('book::book.draft'),
            default => ucfirst($data['status'])
        };

        return '<span class="badge bg-' . $statusClass . '">' . $statusLabel . '</span>';
    }

    public function getColumnDisplayOrder(array $data): string
    {
        return '<span class="badge bg-primary">' . $data['display_order'] . '</span>';
    }

    public function getColumnBookCreatedAt(array $data)
    {
        return adminDateTimeFormatShow($data['book_created_at']);
    }

    public function getColumnAction(array $data): string
    {
        $return = '';
        $user = Auth::user();

        if ($user->hasPermission('update-book')) {
            $return .= '<a class="me-3" href="' . route('admin.books.edit', encrypt($data['id'])) . '">
            <span class="inic inic-edit fs-18" data-bs-toggle="tooltip" title="Edit"></span></a>';
        }

        if ($user->hasPermission('read-book')) {
            $return .= '<a class="me-3" href="' . route('admin.books.show', encrypt($data['id'])) . '">
            <span class="inic inic-eye fs-18" data-bs-toggle="tooltip" title="View"></span></a>';
        }

        if ($user->hasPermission('delete-book')) {
            $return .= '<a class="delete-grid-row" href="javascript:void(0)" data-grid="inic_grid_' . $this->uniqueID . '"
            data-url="' . route('admin.books.destroy', encrypt($data['id'])) . '">
            <span class="inic inic-bin fs-18" data-bs-toggle="tooltip" title="Delete"></span></a>';
        }

        return $return;
    }

    public function getColumnCheckbox(array $data): string
    {
        return '<input type="checkbox" value="' . $data['id'] . '" class="check_approval" />';
    }

    public function getDownloadColumnTitle(array $data): string
    {
        // Get the title, stripping JSON quotes if present
        $title = $data['title'] ?? '';
        $title = trim($title, '"');

        return $title;
    }

    public function getDownloadColumnStatus(array $data): string
    {
        return $data['status'];
    }

    public function getDownloadColumnAuthorName(array $data): string
    {
        return $data['author_name'] ?? '';
    }
}
