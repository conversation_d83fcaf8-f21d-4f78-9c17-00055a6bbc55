<?php

namespace Modules\Book\app\Repositories;

use Modules\Book\Models\Book;
use App\Repositories\Admin\Repository;

class BookRepository extends Repository
{
    /**
     * BookRepository constructor.
     * @param Book $model
     */
    public function __construct(Book $model)
    {
        $this->model = $model;
    }

    /**
     * Handle bulk actions for books
     *
     * @param array $ids
     * @param string $status
     * @param string $actionType
     * @return array
     */
    public function bulkAction($ids, $status, $actionType): array
    {
        $type = 'success';
        switch ($actionType) {
            case 'delete':
                foreach ($ids as $id) {
                    $book = $this->model->findOrFail($id);

                    // Clear all media before deleting
                    $book->clearMediaCollection('cover_image');

                    $book->delete();
                }
                $message = __('book::book.book_delete_successfully');
                $type = 'success';
                break;
            case 'publish':
                $statusValue = $status == "Published" ? 'published' : 'upcoming';

                // If publishing books, check for books without images
                if ($statusValue === 'published') {
                    // Get books without images
                    $booksWithoutImages = $this->model->whereIn('id', $ids)
                        ->whereDoesntHave('media', function ($query) {
                            $query->where('collection_name', 'cover_image');
                        })
                        ->get(['id', 'title']);

                    // Get books with images (can be published)
                    $booksWithImages = $this->model->whereIn('id', $ids)
                        ->whereHas('media', function ($query) {
                            $query->where('collection_name', 'cover_image');
                        })
                        ->pluck('id')
                        ->toArray();

                    // Publish books that have images
                    if (!empty($booksWithImages)) {
                        $this->model->whereIn('id', $booksWithImages)->update(['status' => 'published']);
                    }

                    // Prepare response message
                    if ($booksWithoutImages->isNotEmpty()) {
                        if (!empty($booksWithImages)) {
                            // Partial success - some published, some couldn't be
                            $message = __('book::book.partial_publish_completed', [
                                'published' => count($booksWithImages),
                                'failed' => count($booksWithoutImages)
                            ]);
                            $type = 'warning';
                        } else {
                            // No books could be published
                            $message = __('book::book.cannot_publish_books_without_cover_images');
                            $type = 'error';
                        }
                    } else {
                        // All books published successfully
                        $message = __('book::book.status_updated_successfully');
                        $type = 'success';
                    }
                } else {
                    // Not publishing - normal status update
                    $this->model->whereIn('id', $ids)->update(['status' => $statusValue]);
                    $message = __('book::book.status_updated_successfully');
                    $type = 'success';
                }
                break;
            case 'archive':
                $statusValue = $status == "Archived" ? 'archived' : 'published';
                $this->model->whereIn('id', $ids)->update(['status' => $statusValue]);
                $message = __('book::book.status_updated_successfully');
                $type = 'success';
                break;
            case 'draft':
                $this->model->whereIn('id', $ids)->update(['status' => 'draft']);
                $message = __('book::book.status_updated_successfully');
                $type = 'success';
                break;

            default:
                $type = 'error';
                $message = __('book::book.something_wrong');
                break;
        }
        return [
            'type' => $type,
            'message' => $message
        ];
    }

    /**
     * Get books ordered by display_order
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getOrderedBooks()
    {
        return $this->model->with('author')->orderBy('display_order', 'asc')->orderBy('created_at', 'desc')->get();
    }

    /**
     * Get published books
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getPublishedBooks()
    {
        return $this->model->where('status', 'published')->orderBy('display_order', 'asc')->get();
    }

    /**
     * Get books by author
     *
     * @param int $authorId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getBooksByAuthor($authorId)
    {
        return $this->model->where('author_id', $authorId)->orderBy('display_order', 'asc')->get();
    }

    /**
     * Find book by slug
     *
     * @param string $slug
     * @return Book|null
     */
    public function findBySlug($slug)
    {
        return $this->model->with('author')->where('slug', $slug)->first();
    }

    /**
     * Get books with author relationship
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getBooksWithAuthor()
    {
        return $this->model->with('author');
    }

    /**
     * Search books by title or author name
     *
     * @param string $search
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function searchBooks($search)
    {
        return $this->model->with('author')
            ->where(function ($query) use ($search) {
                $query->where('title', 'like', '%' . $search . '%')
                      ->orWhere('slug', 'like', '%' . $search . '%')
                      ->orWhereHas('author', function ($authorQuery) use ($search) {
                          $authorQuery->where('name', 'like', '%' . $search . '%');
                      });
            })
            ->orderBy('display_order', 'asc')
            ->get();
    }

    /**
     * Get next display order
     *
     * @return int
     */
    public function getNextDisplayOrder()
    {
        $maxOrder = $this->model->max('display_order');
        return $maxOrder ? $maxOrder + 1 : 1;
    }
}
