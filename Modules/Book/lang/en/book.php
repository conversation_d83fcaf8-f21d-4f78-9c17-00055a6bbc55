<?php

return [
    // General
    'book' => 'Book',
    'books' => 'Books',
    'title' => 'Title',
    'subtitle' => 'Subtitle',
    'description' => 'Description',
    'author' => 'Author',
    'slug' => 'Slug',
    'genre' => 'Genre',
    'pages' => 'Pages',
    'cover_image' => 'Cover Image',
    'why_this_book' => 'Why This Book',
    'key_theme_title' => 'Key Theme Title',
    'key_theme_description' => 'Key Theme Description',
    'publisher' => 'Publisher',
    'illustrator' => 'Illustrator',
    'year_published' => 'Year Published',
    'status' => 'Status',
    'display_order' => 'Display Order',
    'created_at' => 'Created At',
    'action' => 'Action',
    'no_author' => 'No Author',
    'select_publisher' => 'Select Publisher',
    'select_illustrator' => 'Select Illustrator',

    // Status
    'published' => 'Published',
    'upcoming' => 'Upcoming',
    'archived' => 'Archived',
    'draft' => 'Draft',

    // Placeholders
    'title_placeholder' => 'Enter book title',
    'subtitle_placeholder' => 'Enter book subtitle',
    'description_placeholder' => 'Enter book description',
    'slug_placeholder' => 'Enter book slug',
    'genre_placeholder' => 'Enter book genre',
    'pages_placeholder' => 'Enter number of pages',
    'why_this_book_placeholder' => 'Why should readers choose this book?',
    'key_theme_title_placeholder' => 'Enter key theme title',
    'key_theme_description_placeholder' => 'Describe the key theme',
    'publisher_placeholder' => 'Enter publisher name',
    'year_published_placeholder' => 'Enter publication year',
    'display_order_placeholder' => 'Enter display order',

    // Validation Messages - Required
    'title_array' => 'The title field must be an array.',
    'title_en_required' => 'The title (English) field is required.',
    'title_ar_required' => 'The title (Arabic) field is required.',
    'slug_required' => 'The slug field is required.',
    'author_required' => 'The author field is required.',
    'status_required' => 'The status field is required.',

    // Validation Messages - Format
    'title_string' => 'The title must be a string.',
    'title_max' => 'The title may not be greater than 255 characters.',
    'subtitle_array' => 'The subtitle field must be an array.',
    'subtitle_en_required' => 'The subtitle (English) field is required.',
    'subtitle_ar_required' => 'The subtitle (Arabic) field is required.',
    'subtitle_string' => 'The subtitle must be a string.',
    'subtitle_max' => 'The subtitle may not be greater than 255 characters.',
    'description_array' => 'The description field must be an array.',
    'description_en_required' => 'The description (English) field is required.',
    'description_ar_required' => 'The description (Arabic) field is required.',
    'description_string' => 'The description must be a string.',
    'genre_array' => 'The genre field must be an array.',
    'genre_en_required' => 'The genre (English) field is required.',
    'genre_ar_required' => 'The genre (Arabic) field is required.',
    'genre_string' => 'The genre must be a string.',
    'genre_max' => 'The genre may not be greater than 255 characters.',
    'why_this_book_array' => 'The why this book field must be an array.',
    'why_this_book_en_required' => 'The why this book (English) field is required.',
    'why_this_book_ar_required' => 'The why this book (Arabic) field is required.',
    'why_this_book_string' => 'The why this book field must be a string.',
    'key_theme_title_array' => 'The key theme title field must be an array.',
    'key_theme_title_string' => 'The key theme title field must be a string.',
    'key_theme_title_en_required' => 'The key theme title (English) field is required.',
    'key_theme_title_ar_required' => 'The key theme title (Arabic) field is required.',
    'key_theme_description_array' => 'The key theme description field must be an array.',
    'key_theme_description_en_required' => 'The key theme description (English) field is required.',
    'key_theme_description_ar_required' => 'The key theme description (Arabic) field is required.',
    'key_theme_description_string' => 'The key theme description field must be a string.',
    'year_published_string' => 'The year published must be a string.',
    'year_published_max' => 'The year published may not be greater than 10 characters.',

    // Validation Messages - Other
    'slug_unique' => 'The slug has already been taken.',
    'author_integer' => 'The author field must be an integer.',
    'author_exists' => 'The selected author does not exist.',
    'publisher_integer' => 'The publisher field must be an integer.',
    'publisher_exists' => 'The selected publisher does not exist.',
    'illustrator_integer' => 'The illustrator field must be an integer.',
    'illustrator_exists' => 'The selected illustrator does not exist.',
    'status_invalid' => 'The selected status is invalid.',
    'pages_integer' => 'The pages field must be an integer.',
    'pages_min' => 'The pages field must be at least 1.',
    'display_order_integer' => 'The display order field must be an integer.',
    'display_order_min' => 'The display order field must be at least 0.',
    'cover_image_mimes' => 'The cover image must be a file of type: jpeg, jpg, png, webp.',
    'cover_image_max' => 'The cover image may not be greater than 10MB.',
    'cover_image_required_when_published' => 'A cover image is required when the book status is set to published.',
    'cannot_publish_without_cover_image' => 'Cannot publish book without a cover image. Please upload an image first.',
    'cannot_publish_books_without_cover_images' => 'Cannot publish books without cover images. Please upload images first.',
    'partial_publish_completed' => ':published books published successfully. Could not publish :failed books without cover images.',

    // Success Messages
    'book_created_successfully' => 'Book created successfully.',
    'book_updated_successfully' => 'Book updated successfully.',
    'book_deleted_successfully' => 'Book deleted successfully.',
    'book_delete_successfully' => 'Books deleted successfully.',
    'status_updated_successfully' => 'Status updated successfully.',

    // Error Messages
    'something_wrong' => 'Something went wrong.',
    'no_records_selected' => 'No records selected.',

    // Form Labels
    'add_book' => 'Add Book',
    'edit_book' => 'Edit Book',
    'view_book' => 'View Book',
    'book_details' => 'Book Details',
    'book_information' => 'Book Information',
    'book_content' => 'Book Content',
    'book_metadata' => 'Book Metadata',

    // Buttons
    'create_book' => 'Create Book',
    'update_book' => 'Update Book',
    'save_book' => 'Save Book',
    'cancel' => 'Cancel',
    'back_to_books' => 'Back to Books',

    // Table Headers
    'book_list' => 'Book List',
    'manage_books' => 'Manage Books',
    'total_books' => 'Total Books',

    // Additional
    'select_author' => 'Select Author',
    'select_status' => 'Select Status',
    'book_cover' => 'Book Cover',
    'upload_cover' => 'Upload Cover',
    'change_cover' => 'Change Cover',
    'remove_cover' => 'Remove Cover',
    'no_publisher' => 'No Publisher',
    'no_illustrator' => 'No Illustrator',

    // Bulk Actions
    'activate' => 'Activate',
    'deactivate' => 'Deactivate',
    'delete' => 'Delete',

    // CSV Import
    'import_csv' => 'Import CSV',
    'import_books' => 'Import Books',
    'csv_file' => 'CSV File',
    'csv_file_required' => 'Please select a CSV file to upload.',
    'csv_file_invalid' => 'The uploaded file is not valid.',
    'csv_file_format' => 'The file must be a CSV file (.csv or .txt).',
    'csv_file_size' => 'The file size must not exceed 10MB.',
    'csv_file_help' => 'Upload a CSV file with book data. Maximum file size: 10MB.',
    'import_instructions' => 'Import Instructions',
    'import_instructions_text' => 'Upload a CSV file containing book data. The file must include all required headers in the exact format shown below.',
    'required_csv_headers' => 'Required CSV Headers',
    'import_note_required' => 'Note: author_name and title_en are required fields. The author must exist in the system.',
    'sample_csv' => 'Sample CSV File',
    'sample_csv_text' => 'Download a sample CSV file with the correct format and example data.',
    'download_sample' => 'Download Sample',
    'importing' => 'Importing...',
    'invalid_file_type' => 'Please select a valid CSV file.',
    'file_too_large' => 'File size exceeds the 10MB limit.',
    'import_completed' => 'Import completed: :success successful, :errors errors out of :total total rows.',
    'import_failed' => 'Import failed: :error',
    'missing_csv_headers' => 'Missing required CSV headers: :headers',
    'import_errors_found' => 'Import completed with some errors',
    'csv_file_empty' => 'The CSV file is empty or could not be read.',
    'csv_no_data_rows' => 'The CSV file contains no data rows.',
    'please_select_at_least_one_book' => 'Please select at least one book.',
    'image_upload_info' => 'Recommended size: 235x350px or higher. Supported formats: JPG, PNG, WebP',
];
