@extends('admin.layouts.page')
@section('title', __('award::award.awards'))

@section('breadcrumb')
    <ol class="breadcrumb-item active">{{ __('award::award.awards') }}</ol>
@endsection

@permission('create-award')
    @section('action_buttons')
    <div class="d-flex gap-2">
        <a href="{{ route('admin.awards.import') }}" class="btn btn-outline-primary">
            <em class="inic inic-upload"></em> {{ __('award::award.import_csv') }}
        </a>
        <a href="{{ route('admin.awards.create') }}" class="btn btn-primary">
            <em class="inic inic-add"></em> {{ __('award::award.add_award') }}
        </a>
    </div>
    @endsection
@endpermission

@section('content')
    <!-- Display import errors if any -->
    @if(session('import_errors'))
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <h5 class="alert-heading">{{ __('award::award.import_errors_found') }}</h5>
            <ul class="mb-0">
                @foreach(session('import_errors') as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    {!! $dataGrid !!}
@endsection

@include('plugins.sweetalert2')
@push('scripts')
<script type="module">
    let csrfToken = $('meta[name="csrf-token"]').attr('content');
    $(document).on('click', '.delete-grid-row', function (event) {
        event.preventDefault();
        let url = $(this).attr('data-url');
        SwalConfirm(url, function (url) {
            $.ajax({
                url: url,
                type: 'delete',
                datatype: 'json',
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                },
                success: function (response) {
                    toastr.success(response.message);
                    window.inic_grid_award.refresh();
                },
                error: function (response) {
                    toastr.error(response.message);
                }
            });
        })
    })

    $(document).on('click', '.grid-bulk-action', function(event) {
            event.preventDefault();
            var id = [];var error = 0;
            $('.check_approval').each(function(index){
                if($(this).prop('checked') == true){
                    id.push($(this).val());
                    error = 1;
                }
            });

            var url= "{{ route('admin.awards.bulk-action') }}";
            var status=$(this).data('status');
            var type=$(this).data('action-type');

            if(error == 0){
                toastr.error("{{ __('award::award.please_select_one_award') }}");
            }else if(error == 1){
                SwalConfirm(url, function(url) {
                    $.ajax({
                        url: url,
                        type: 'post',
                        data: {id: id,status:status,type:type},
                        success: function(response) {
                            if(response.status == 'success'){
                                toastr.success(response.message);
                            }else{
                                toastr.error(response.message);
                            }
                            window.inic_grid_award.refresh();
                        },
                        error: function(response) {
                            toastr.error(response.message);
                        }

                    });
                });
            }
        });
</script>
@endpush
