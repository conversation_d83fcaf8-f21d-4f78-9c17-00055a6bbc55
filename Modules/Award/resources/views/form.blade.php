<form method="POST" action="{{ $action }}" enctype="multipart/form-data" id="award-form">
    @csrf
    @if(isset($is_update) && $is_update)
        @method('PUT')
    @endif

    <!-- Language Switcher -->
    @include('award::partials.language-switcher')

    <div class="row">
        <!-- Card for translatable fields -->
        <div class="col-md-12 mb-4">
            <div class="card border-primary" style="border-width: 2px;">
                <div class="card-header bg-primary bg-opacity-10">
                    <h5 class="mb-0"><i class="fa fa-language"></i> {{ __('admin.common.translatable_fields') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Award Title (translatable) -->
                        <div class="col-md-6 mb-3">
                            <label for="award_title_en" class="form-label">{{ __('award::award.award_title') }} <span class="text-danger">*</span></label>
                            <div class="form-group {{ $errors->has('award_title.en') ? 'has-error' : '' }} translatable-field" data-language="en">
                                <input type="text" name="award_title[en]" id="award_title_en" class="form-control"
                                    value="{{ old('award_title.en', isset($model) ? $model->getTranslation('award_title', 'en', false) : '') }}"
                                    placeholder="{{ __('award::award.enter_award_title') }}">
                                @if ($errors->has('award_title.en'))
                                    <span class="help-block">{{ $errors->first('award_title.en') }}</span>
                                @endif
                            </div>
                            <div class="form-group {{ $errors->has('award_title.ar') ? 'has-error' : '' }} translatable-field" data-language="ar" style="display: none;">
                                <input type="text" name="award_title[ar]" id="award_title_ar" class="form-control" dir="rtl"
                                    value="{{ old('award_title.ar', isset($model) ? $model->getTranslation('award_title', 'ar', false) : '') }}"
                                    placeholder="{{ __('award::award.enter_award_title') }}">
                                @if ($errors->has('award_title.ar'))
                                    <span class="help-block">{{ $errors->first('award_title.ar') }}</span>
                                @endif
                            </div>
                        </div>

                        <!-- Award From (translatable) -->
                        <div class="col-md-6 mb-3">
                            <label for="award_from_en" class="form-label">{{ __('award::award.award_from') }}</label>
                            <div class="form-group {{ $errors->has('award_from.en') ? 'has-error' : '' }} translatable-field" data-language="en">
                                <input type="text" name="award_from[en]" id="award_from_en" class="form-control"
                                    value="{{ old('award_from.en', isset($model) ? $model->getTranslation('award_from', 'en', false) : '') }}"
                                    placeholder="{{ __('award::award.enter_award_from') }}">
                                @if ($errors->has('award_from.en'))
                                    <span class="help-block">{{ $errors->first('award_from.en') }}</span>
                                @endif
                            </div>
                            <div class="form-group {{ $errors->has('award_from.ar') ? 'has-error' : '' }} translatable-field" data-language="ar" style="display: none;">
                                <input type="text" name="award_from[ar]" id="award_from_ar" class="form-control" dir="rtl"
                                    value="{{ old('award_from.ar', isset($model) ? $model->getTranslation('award_from', 'ar', false) : '') }}"
                                    placeholder="{{ __('award::award.enter_award_from') }}">
                                @if ($errors->has('award_from.ar'))
                                    <span class="help-block">{{ $errors->first('award_from.ar') }}</span>
                                @endif
                            </div>
                        </div>

                        <!-- Award Summary (translatable) -->
                        <div class="col-md-12 mb-3">
                            <label for="award_summary_en" class="form-label">{{ __('award::award.award_summary') }}</label>
                            <div class="form-group {{ $errors->has('award_summary.en') ? 'has-error' : '' }} translatable-field" data-language="en">
                                <textarea name="award_summary[en]" id="award_summary_en" class="form-control ck-editor" rows="3"
                                    placeholder="{{ __('award::award.enter_award_summary') }}">{{ old('award_summary.en', isset($model) ? $model->getTranslation('award_summary', 'en', false) : '') }}</textarea>
                                @if ($errors->has('award_summary.en'))
                                    <span class="help-block">{{ $errors->first('award_summary.en') }}</span>
                                @endif
                            </div>
                            <div class="form-group {{ $errors->has('award_summary.ar') ? 'has-error' : '' }} translatable-field" data-language="ar" style="display: none;">
                                <textarea name="award_summary[ar]" id="award_summary_ar" class="form-control ck-editor" dir="rtl" rows="3"
                                    placeholder="{{ __('award::award.enter_award_summary') }}">{{ old('award_summary.ar', isset($model) ? $model->getTranslation('award_summary', 'ar', false) : '') }}</textarea>
                                @if ($errors->has('award_summary.ar'))
                                    <span class="help-block">{{ $errors->first('award_summary.ar') }}</span>
                                @endif
                            </div>
                        </div>

                        <!-- Award Description (translatable) -->
                        <div class="col-md-12 mb-3">
                            <label for="award_description" class="form-label">{{ __('award::award.award_description') }}</label>
                            <div class="translatable-field" data-language="en">
                                <textarea name="award_description[en]" id="award_description_en" class="form-control ck-editor">{{ old('award_description.en', isset($model) ? $model->getTranslation('award_description', 'en', false) : '') }}</textarea>
                            </div>
                            <div class="translatable-field" data-language="ar" style="display: none;">
                                <textarea name="award_description[ar]" id="award_description_ar" class="form-control ck-editor" dir="rtl">{{ old('award_description.ar', isset($model) ? $model->getTranslation('award_description', 'ar', false) : '') }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card for non-translatable fields -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fa fa-cog"></i> {{ __('admin.common.general_settings') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Author -->
                        <div class="col-md-6 mb-3">
                            <label for="author_id" class="form-label">{{ __('award::award.author') }} <span class="text-danger">*</span></label>
                            <select name="author_id" id="author_id" class="form-select">
                                <option value="">{{ __('award::award.select_author') }}</option>
                                @foreach($authors as $author)
                                    <option value="{{ $author->id }}" {{ old('author_id', $model->author_id ?? '') == $author->id ? 'selected' : '' }}>
                                        {{ $author->getTranslation('name', 'en') }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Award Year -->
                        <div class="col-md-6 mb-3">
                            <label for="award_year" class="form-label">{{ __('award::award.award_year') }}</label>
                            <select name="award_year" id="award_year" class="form-select">
                                <option value="">{{ __('award::award.select_award_year') }}</option>
                                @for($year = date('Y') + 10; $year >= 1900; $year--)
                                    <option value="{{ $year }}" {{ old('award_year', isset($model) ? $model->award_year : '') == $year ? 'selected' : '' }}>
                                        {{ $year }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form buttons -->
        <div class="col-md-12">
            <div class="d-flex align-items-center justify-content-end gap-3">
                <button type="submit" class="btn btn-primary">{{ __('admin.common.submit') }}</button>
                <a href="{{ route('admin.awards.index') }}" class="btn btn-secondary">{{ __('admin.common.cancel') }}</a>
            </div>
        </div>
    </div>
</form>

@include('plugins.ckeditor')

@push('scripts')
    @php
        $validator = JsValidator::formRequest('Modules\Award\app\Http\Requests\AwardRequest');
        $validator->ignore('');
    @endphp
    {!! $validator !!}

    <script type="module">
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize CKEditors for both languages
            initializeCKEditor('award_summary_en', options);
            initializeCKEditor('award_description_en', options);

            // Set up Arabic CKEditor with RTL direction when needed
            const arOptions = Object.assign({}, options, {
                contentsLangDirection: 'rtl'
            });
            initializeCKEditor('award_summary_ar', arOptions);
            initializeCKEditor('award_description_ar', arOptions);

            // Hide the Arabic editors initially
            if (typeof CKEDITOR !== 'undefined') {
                if (CKEDITOR.instances.award_summary_ar && CKEDITOR.instances.award_summary_ar.container) {
                    CKEDITOR.instances.award_summary_ar.container.setStyle('display', 'none');
                }
                if (CKEDITOR.instances.award_description_ar && CKEDITOR.instances.award_description_ar.container) {
                    CKEDITOR.instances.award_description_ar.container.setStyle('display', 'none');
                }
            }
        });
    </script>
@endpush
