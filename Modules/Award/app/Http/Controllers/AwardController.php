<?php

namespace Modules\Award\app\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

use Modules\Award\app\Http\Requests\AwardRequest;
use Modules\Award\app\Http\Requests\AwardImportRequest;
use Modules\Award\app\Repositories\AwardRepository;
use Modules\Award\app\Services\AwardImportService;
use Modules\Award\app\DataGrids\AwardDataGrid;
use Modules\Award\Models\Award;
use Modules\Author\app\Repositories\AuthorRepository;

class AwardController extends Controller
{
    protected $awardRepository;
    protected $authorRepository;
    protected $awardImportService;

    public function __construct(
        AwardRepository $awardRepository,
        AuthorRepository $authorRepository,
        AwardImportService $awardImportService
    ) {
        $this->awardRepository = $awardRepository;
        $this->authorRepository = $authorRepository;
        $this->awardImportService = $awardImportService;

        $this->middleware('permission:create-award', ['only' => ['create', 'store', 'showImport', 'processImport']]);
        $this->middleware('permission:update-award', ['only' => ['edit', 'update']]);
        $this->middleware(
            'permission:read-award|create-award|update-award|delete-award',
            ['only' => ['index']]
        );
        $this->middleware('permission:delete-award', ['only' => ['destroy']]);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(): View
    {
        $dataGrid = AwardDataGrid::getHTML();
        return view('award::index', compact('dataGrid'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View|JsonResponse
    {
        try {
            $authors = $this->authorRepository->getAuthors();

            return view('award::create', [
                'method' => 'POST',
                'authors' => $authors,
                'action' => route('admin.awards.store'),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage()),
            ]);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(AwardRequest $request): RedirectResponse
    {
        try {
            $data = $request->validated();

            $this->awardRepository->create($data);

            return redirect()->route('admin.awards.index')
                ->with('success', __('award::award.award_created_successfully'));
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', __('award::award.something_wrong'));
        }
    }

    /**
     * Show the specified resource.
     */
    public function show(Award $award): View
    {
        $award->load('author');
        return view('award::show', compact('award'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Award $award): View|RedirectResponse
    {
        try {
            $authors = $this->authorRepository->getAuthors();

            return view('award::edit', [
                'is_update' => true,
                'model' => $award,
                'authors' => $authors,
                'method' => 'PUT',
                'action' => route('admin.awards.update', encrypt($award->id)),
            ]);
        } catch (\Exception $e) {
            return redirect()->route('admin.awards.index')
                ->with('error', __('award::award.something_wrong'));
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(AwardRequest $request, Award $award): RedirectResponse
    {
        try {
            $data = $request->validated();

            $this->awardRepository->update($data, $award->id);

            return redirect()->route('admin.awards.index')
                ->with('success', __('award::award.award_updated_successfully'));
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->with('error', __('award::award.something_wrong'));
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Award $award): JsonResponse
    {
        try {
            $this->awardRepository->delete($award->id);

            return response()->json([
                'status' => 'success',
                'message' => __('award::award.award_deleted_successfully')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __('award::award.something_wrong')
            ]);
        }
    }

    /**
     * Handle bulk actions
     */
    public function bulkAction(Request $request): JsonResponse
    {
        try {
            $ids = $request->input('id', []);
            $action = $request->input('type');
            $status = $request->input('status');

            if (empty($ids)) {
                return response()->json([
                    'status' => 'error',
                    'message' => __('award::award.no_records_selected')
                ]);
            }

            $result = $this->awardRepository->bulkAction($ids, $status, $action);

            return response()->json($result);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __('award::award.something_wrong')
            ]);
        }
    }

    /**
     * Show the CSV import form
     */
    public function showImport(): View|JsonResponse
    {
        try {
            $expectedHeaders = $this->awardImportService->getExpectedHeaders();

            return view('award::import', [
                'action' => route('admin.awards.process-import'),
                'expectedHeaders' => $expectedHeaders
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage()),
            ]);
        }
    }

    /**
     * Process CSV import
     */
    public function processImport(AwardImportRequest $request): RedirectResponse
    {
        try {
            $file = $request->file('csv_file');
            $results = $this->awardImportService->processImport($file);

            $messageType = $results['error_count'] > 0 ? 'warning' : 'success';
            $message = __('award::award.import_completed', [
                'success' => $results['success_count'],
                'errors' => $results['error_count'],
                'total' => $results['total_rows']
            ]);

            $sessionData = [
                'type' => $messageType,
                'message' => $message
            ];

            // Add detailed errors if any
            if (!empty($results['errors'])) {
                $sessionData['import_errors'] = $results['errors'];
            }

            return redirect()->route('admin.awards.index')->with($sessionData);

        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => __('award::award.import_failed', ['error' => $e->getMessage()])
            ]);
        }
    }
}
