<?php

namespace Modules\Award\app\DataGrids;

use Indianic\LaravelDataGrid\LaravelDataGrid;
use Modules\Award\Models\Award;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class AwardDataGrid extends LaravelDataGrid
{
    public $guard = 'admin';

    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'award';

    /**
     * Define how many rows you want to display on a page
     * @var int $rowPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define row per page dropdown options
     * @var array $recordsPerPageOptions
     */
    public $recordsPerPageOptions;

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy = 'id';

    /**
     * Define default soring direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection = 'DESC';

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'award';

    /**
     * Define constants for translation keys
     */
    protected const AWARD_TITLE = 'award::award.award_title';
    protected const AUTHOR = 'award::award.author';
    protected const AWARD_FROM = 'award::award.award_from';
    protected const AWARD_YEAR = 'award::award.award_year';
    protected const CREATED_AT = 'award::award.created_at';
    protected const ACTION = 'award::award.action';

    public function resource()
    {
        return Award::select(
            'awards.id',
            'awards.award_year',
            'awards.created_at as award_created_at',
            DB::raw("JSON_EXTRACT(awards.award_title, '$.en') as award_title"),
            DB::raw("JSON_EXTRACT(awards.award_from, '$.en') as award_from"),
            DB::raw("JSON_EXTRACT(authors.name, '$.en') as author_name")
        )->leftJoin('authors', 'authors.id', '=', 'awards.author_id');
    }

    public function columns(): array
    {
        return [
            'checkbox'  => '<input class="select_all checkbox" type="checkbox" />',
            'award_title'     => __(self::AWARD_TITLE),
            'author_name'  => __(self::AUTHOR),
            'award_from'    => __(self::AWARD_FROM),
            'award_year'    => __(self::AWARD_YEAR),
            'award_created_at'=> __(self::CREATED_AT),
            'action'    => __(self::ACTION)
        ];
    }

    public function mapDBColumns(): array
    {
        return [
            'award_title' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(awards.award_title, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'author_name' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(authors.name, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'award_from' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(awards.award_from, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'award_year' => 'awards.award_year',
            'award_created_at' => 'awards.created_at'
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'award_title',
            'author_name',
            'award_from',
            'award_year',
            'award_created_at',
        ];
    }

    // public function downloadableColumns(): array
    // {
    //     return [
    //         'award_title'     => __(self::AWARD_TITLE),
    //         'author_name'  => __(self::AUTHOR),
    //         'award_from'    => __(self::AWARD_FROM),
    //         'award_year'    => __(self::AWARD_YEAR),
    //         'award_created_at'=> __(self::CREATED_AT)
    //     ];
    // }

    public function gloablSearchableColumns(): array
    {
        return [
            'award_title'     => 'string',
            'author_name'  => 'string',
            'award_from'    => 'string',
            'award_year'    => 'string',
            'award_created_at' => 'string'
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'award_title'     => 'string',
            'author_name'  => 'string',
            'award_from'    => 'string',
            'award_year'    => 'string',
            'award_created_at'=> 'string'
        ];
    }

    // public function filters(): array
    // {
    //     return [
    //         'award_year' => [
    //             'title' => __(self::AWARD_YEAR),
    //             'width' => '250',
    //             'type' => 'string'
    //         ],
    //         'award_created_at' => [
    //             'title' => __(self::CREATED_AT),
    //             'width' => '250',
    //             'type' => 'daterange'
    //         ],
    //     ];
    // }

    public function getBulkAction(): array
    {
        return [
            'Delete'=>[
                'class'=>'danger',
                'title'=>__('award::award.delete'),
                'module'=>'Award',
                'type' => 'delete'
            ]
        ];
    }

    public function getColumnAwardTitle(array $data): string
    {
        $title = $data['award_title'] ?? '';
        $title = trim($title, '"');
        $title = strlen($title) > 50 ? substr($title, 0, 50) . '...' : $title;
        return '<span class="fw-bold">' . htmlspecialchars($title) . '</span>';
    }

    public function getColumnAuthorName(array $data): string
    {
        $authorName = $data['author_name'] ?? '';
        $authorName = trim($authorName, '"');
        return '<span class="badge bg-info">' . htmlspecialchars($authorName) . '</span>';
    }

    public function getColumnAwardFrom(array $data): string
    {
        $awardFrom = $data['award_from'] ?? '';
        $awardFrom = trim($awardFrom, '"');
        return '<span class="badge bg-secondary">' . htmlspecialchars($awardFrom) . '</span>';
    }

    public function getColumnAwardYear(array $data): string
    {
        return $data['award_year'] ?? '';
    }

    public function getColumnAwardCreatedAt(array $data)
    {
        return adminDateTimeFormatShow($data['award_created_at']);
    }

    public function getColumnAction(array $data): string
    {
        $return = '';
        $user = Auth::user();

        if ($user->hasPermission('update-award')) {
            $return .= '<a class="me-3" href="' . route('admin.awards.edit', encrypt($data['id'])) . '">
            <span class="inic inic-edit fs-18" data-bs-toggle="tooltip" title="Edit"></span></a>';
        }

        if ($user->hasPermission('read-award')) {
            $return .= '<a class="me-3" href="' . route('admin.awards.show', encrypt($data['id'])) . '">
            <span class="inic inic-eye fs-18" data-bs-toggle="tooltip" title="View"></span></a>';
        }

        if ($user->hasPermission('delete-award')) {
            $return .= '<a class="delete-grid-row" href="javascript:void(0)" data-grid="inic_grid_'.$this->uniqueID.'"
            data-url="' . route('admin.awards.destroy', encrypt($data['id'])) . '">
            <span class="inic inic-bin fs-18"  data-bs-toggle="tooltip" title="Delete"></span></a>';
        }

        return $return;
    }

   public function getColumnCheckbox(array $data): string
   {
        return '<input type="checkbox" value="' . $data['id'] . '" class="check_approval" />';
   }

    public function getDownloadColumnAwardTitle(array $data): string
    {
        $title = $data['award_title'] ?? '';
        return trim($title, '"');
    }

    public function getDownloadColumnAuthorName(array $data): string
    {
        $authorName = $data['author_name'] ?? '';
        return trim($authorName, '"');
    }

    public function getDownloadColumnAwardFrom(array $data): string
    {
        $awardFrom = $data['award_from'] ?? '';
        return trim($awardFrom, '"');
    }
}
