@extends('admin.layouts.page')

@section('title', __('emailtemplate::emailtemplate.email_template'))

@section('breadcrumb')
    <ol class="breadcrumb-item active" aria-current="page">{{ __('emailtemplate::emailtemplate.email_template') }}</ol>
@endsection

@permission('create-email-templates')
    @section('action_buttons')
    <a href="{{ route('admin.email-templates.create') }}" class="btn btn-primary">
        <em class="inic inic-add"></em> {{ __('emailtemplate::emailtemplate.add_email_template') }}</a>
    @endsection
@endpermission

@section('content')
    {!! $dataGridHtml !!}
@endsection

@push('scripts')
    <script type="module">
        $(document).on('change','.change-status', function(){
            var id =  $(this).attr('data-id');
            $.ajax({
                url: "{{ route('admin.email-templates.change_status')}}",
                type: "post",
                data: {"id": id},
                datatype: 'json',
                success: function (response) {
                    toastr.success(response.message);
                    window.inic_grid_email_templates.refresh();
                },
                error: function (response) {
                    toastr.error(response.message);
                }
            });
        });

        $(document).on('click', '.delete-grid-row', function(event) {
            event.preventDefault();
            let url = $(this).attr('data-url');
            SwalConfirm(url, function(url) {
                $.ajax({
                    url: url,
                    type: 'delete',
                    datatype: 'json',
                    success: function(response) {
                        toastr.success(response.message);
                        window.inic_grid_email_templates.refresh();
                    },
                    error: function(response) {
                        toastr.error(response.message);
                    }
                });
            })
        });

        $(document).on('click', '.grid-bulk-action', function(event) {
            event.preventDefault();
            var id = [];var error = 0;
            $('.check_approval').each(function(index){
                if($(this).prop('checked') == true){
                    id.push($(this).val());
                    error = 1;
                }
            });

            var url= "{{ route('admin.email-templates.bulkAction') }}";
            var status=$(this).data('status');
            var type=$(this).data('action-type');

            if(error == 0){
                toastr.error("{{ __('emailtemplate::emailtemplate.please_select_email_template') }}");
            }else if(error == 1){
                SwalConfirm(url, function(url) {
                    $.ajax({
                        url: url,
                        type: 'post',
                        data: {id: id,status:status,type:type},
                        success: function(response) {
                            if(response.status == 'success'){
                                toastr.success(response.message);
                            }else{
                                toastr.error(response.message);
                            }
                            window.inic_grid_email_templates.refresh();
                        },
                        error: function(response) {
                            toastr.error(response.message);
                        }

                    });
                });
            }
        });
    </script>
@endpush
