<?php

namespace Modules\EmailTemplate\app\DataGrids;

use Indianic\LaravelDataGrid\LaravelDataGrid;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class EmailTemplateDataGrid extends LaravelDataGrid
{
    public $guard = 'admin';
    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'email_templates';

    /**
     * Define how many rows you want to display on a page
     * @var int $rowPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define row per page dropdown options
     * @var array $recordsPerPageOptions
     */
    public $recordsPerPageOptions;

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy;

    /**
     * Define default soring direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection;

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'email_templates';

    protected const EMAILTEMP = "emailtemplate::emailtemplate";
    protected const EMAILTEMP_TITLE = "emailtemplate::emailtemplate.title";
    protected const EMAILTEMP_SUBJECT = "emailtemplate::emailtemplate.subject";
    protected const EMAILTEMP_STATUS = "emailtemplate::emailtemplate.status";
    protected const EMAILTEMP_ACTIVE = "emailtemplate::emailtemplate.active";

    /**
     * Get Resource of Query Builder
     */
    public function resource()
    {
        return DB::table('email_templates');
    }

    public function columns(): array
    {
        return [
            'checkbox' => '<input class="select_all checkbox" type="checkbox" />',
            'title' => __(self::EMAILTEMP_TITLE),
            'subject' => __(self::EMAILTEMP_SUBJECT),
            'status' => __(self::EMAILTEMP_STATUS),
            'action' => __(self::EMAILTEMP.'.action')
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'title',
            'subject',
            'status',
        ];
    }

    // public function downloadableColumns(): array
    // {
    //     return [
    //         'title' => __(self::EMAILTEMP.'.title'),
    //         'subject' => __(self::EMAILTEMP.'.subject'),
    //         ];
    // }

    public function gloablSearchableColumns(): array
    {
        return [
            'title' => 'string',
            'subject' => 'string',
            'status' => 'string',
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'title' => 'string',
            'subject' => 'string',
            'status' => 'string',
        ];
    }

    // public function getAdvanceSearchOptions(): array
    // {
    //     return [
    //         ['id' => 'title', 'label' => __(self::EMAILTEMP.'.title'), 'type' => 'string'],
    //         ['id' => 'subject', 'label' =>__(self::EMAILTEMP.'.subject'), 'type' => 'string'],
    //         ['id' => 'status', 'label' =>__(self::EMAILTEMP.'.status'), 'type' => 'string'],
    //     ];
    // }


    // public function filters(): array
    // {
    //     return [
    //         'status' => [
    //             'title' => __('admin.common.status'),
    //             'data' => [
    //                 'active' => __(self::EMAILTEMP_ACTIVE),
    //                 'inactive' => __('emailtemplate::emailtemplate.in_active')
    //             ],
    //             'width' => '250',
    //             'type' => 'string',
    //             'auto_filter_val' => __(self::EMAILTEMP_ACTIVE)
    //         ],
    //     ];
    // }

    public function getColumnStatus(array $data): string
    {
        return '<input type="checkbox" class="my-switch change-status"
        data-id = "' . $data['id'] . '" id="switchCheckbox' . $data['id'] . '"
        ' . (($data['status'] == 'Active') ? 'checked' : '') . '
         data-bs-toggle="tooltip" title="' . $data['status'] . ' Template">';
    }

     public function getColumnAction(array $data): string
    {
        $return = '';
        $user = Auth::user();
            $return = '<a class="me-3" href="' . route('admin.email-templates.show', encrypt($data['id'])) . '">
            <span class="inic inic-visibility fs-18"
            data-bs-toggle="tooltip" title="Show"></span></a>';

        if ($user->hasPermission('update-email-templates')) {
            $return .= '<a class="me-3" href="' . route('admin.email-templates.edit', encrypt($data['id'])) . '">
            <span class="inic inic-edit fs-18"  data-bs-toggle="tooltip" title="Edit"></span></a>';
        }

        if ($user->hasPermission('delete-email-templates')) {
            $return .= '<a class="delete-grid-row" href="javascript:void(0)"
            data-grid="inic_grid_'.$this->uniqueID.'"
            data-url="' . route('admin.email-templates.destroy',encrypt($data['id'])) . '">
            <span class="inic inic-bin fs-18"  data-bs-toggle="tooltip" title="Delete"></span></a>';
        }

        return $return;
    }

    public function getColumnCheckbox(array $data): string
    {
        return '<input type="checkbox" value="' . $data['id'] . '" class="check_approval" />';
    }

    public function getBulkAction(): array
    {
        return [
            'Active'=>[
                'class'=>'success',
                'title'=>__(self::EMAILTEMP_ACTIVE),
                'module'=>'EmailTemplate',
                'type' => 'active'
            ],
            'Inactive'=>[
                'class'=>'warning',
                'title'=>__('emailtemplate::emailtemplate.in_active'),
                'module'=>'EmailTemplate',
                'type' => 'inactive'
            ],
            'Delete'=>[
                'class'=>'danger',
                'title'=>__('emailtemplate::emailtemplate.delete'),
                'module'=>'EmailTemplate',
                'type' => 'delete'
            ]
        ];
    }
}
