<?php

namespace Modules\News\app\DataGrids;

use Indianic\LaravelDataGrid\LaravelDataGrid;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Vite;
use Modules\News\app\Models\News;

class NewsDataGrid extends LaravelDataGrid
{
    public $guard = 'admin';
    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'news';

    /**
     * Define how many rows you want to display on a page
     * @var int $rowPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define row per page dropdown options
     * @var array $recordsPerPageOptions
     */
    public $recordsPerPageOptions;

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy = 'id';

    /**
     * Define default soring direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection = 'DESC';

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'news';

    protected const NAME = 'news::news.news_name';
    protected const CATEGORY = 'news::news.category';
    protected const AUTHOR = 'news::news.author';
    protected const CREATED_AT = 'news::news.created_at';
    protected const CREATED_BY = 'news::news.created_by';
    protected const PUBLISH_DATAGRIDNAME = 'news::news.publish_datagridname';
    protected const ACTION = 'news::news.action';

    public function resource()
    {
        return News::select(
                    'news.id',
                    'news.slug',
                    'news.publish',
                    'news.author_id',
                    'news.created_at as news_created_at',
                    DB::raw("JSON_EXTRACT(news.title, '$.en') as title"),
                    DB::raw("concat(admins.first_name, ' ', admins.last_name) as created_name"),
                    DB::raw("JSON_EXTRACT(news_category.name, '$.en') as category"),
                    DB::raw("JSON_EXTRACT(authors.name, '$.en') as author_name")
                )
                ->leftjoin('news_category', 'news_category.id', 'news.category_id')
                ->leftjoin('authors', 'authors.id', 'news.author_id')
                ->leftjoin('admins', 'admins.id', 'news.created_by')
                ->with('media');
    }

    public function columns(): array
    {
        return [
            'checkbox'  => '<input class="select_all checkbox" type="checkbox" />',
            'title'     => __(self::NAME),
            'category'  => __(self::CATEGORY),
            'author'    => __(self::AUTHOR),
            'created_name'=> __(self::CREATED_BY),
            'news_created_at'=> __(self::CREATED_AT),
            'publish'   => __(self::PUBLISH_DATAGRIDNAME),
            'action'    => __(self::ACTION)
        ];
    }

    public function mapDBColumns(): array
    {
        return [
            'title' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(news.title, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'category' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(news_category.name, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'author' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(authors.name, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'news_created_at' =>'news.created_at',
            'created_name' => DB::raw("concat(admins.first_name, ' ', admins.last_name) COLLATE utf8mb4_unicode_ci")
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'title',
            'category',
            'author',
            'news_created_at',
            'created_name',
            'publish'
        ];
    }

    // public function downloadableColumns(): array
    // {
    //     return [
    //         'title'     => __(self::NAME),
    //         'category'  => __(self::CATEGORY),
    //         'author'    => __(self::AUTHOR),
    //         'news_created_at'=> __(self::CREATED_AT),
    //         'created_name'=> __(self::CREATED_BY)
    //     ];
    // }

    public function gloablSearchableColumns(): array
    {
        return [
            'title'     => 'string',
            'category'  => 'string',
            'author'    => 'string',
            'news_created_at'=> 'string',
            'created_name'=> 'string'
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'title'     => 'string',
            'category'  => 'string',
            'author'    => 'string',
            'created_name'=> 'string',
            'news_created_at'=> 'date'
        ];
    }

    // public function getAdvanceSearchOptions(): array
    // {
    //     return [
    //         ['id' => 'title', 'label' => __(self::NAME), 'type' => 'string'],
    //         ['id' => 'category', 'label' => __(self::CATEGORY), 'type' => 'string'],
    //         ['id' => 'author', 'label' => __(self::AUTHOR), 'type' => 'string'],
    //         ['id' => 'created_name', 'label' => __(self::CREATED_BY), 'type' => 'string'],
    //         ['id' => 'publish', 'label' => 'Publish', 'type' => 'string'],
    //     ];
    // }

    public function getBulkAction(): array
    {
        return [
            'Publish' => [
                'class' => 'success',
                'title' => __('news::news.publish'),
                'module' => 'News',
                'type' => 'publish'
            ],
            'Unpublish' => [
                'class' => 'warning',
                'title' => __('news::news.unpublish'),
                'module' => 'News',
                'type' => 'unpublish'
            ],
            'Delete' => [
                'class' => 'danger',
                'title' => __('news::news.delete'),
                'module' => 'News',
                'type' => 'delete'
            ],
        ];
    }

    public function getColumnTitle(array $data): string
    {
        $news = News::find($data['id']);

        $url = $news->getFeaturedImageUrl('thumbnail');

        return '<div class="d-flex align-items-center">
                    <img class=" me-1" src="' .$url . '" alt="'.$data['title'].'" width="62" height="40" style="object-fit: cover;">
                    <a href="javascript:;" class="text-black">' . $data['title'] . '</a>
                </div>';
    }

    public function getColumnCategory(array $data): string
    {
        $category = $data['category'] ?? '';
        $category = trim($category, '"');
        return '<span class="badge rounded-pill bg-light text-dark">'.$category.'</span>';
    }

    public function getColumnAuthor(array $data): string
    {
        $authorName = $data['author_name'] ?? '';
        $authorName = trim($authorName, '"');
        return !empty($authorName) ? $authorName : '-';
    }

    public function getColumnNewsCreatedAt(array $data)
    {
        return adminDateTimeFormatShow($data['news_created_at']);
    }

    public function getColumnCreatedName(array $data): string
    {
        return $data['created_name'];
    }

    public function getColumnPublish(array $data): string
    {
        return '<input type="checkbox" class="my-switch change-status" data-id = "' . $data['id'] . '"
        id="switchCheckbox' . $data['id'] . '" ' . (($data['publish'] == '1') ? 'checked' : '') . '
        data-bs-toggle="tooltip" title="' . (($data['publish'] == '1') ? 'Active' : 'InActive') . '">';
    }

    public function getColumnAction(array $data): string
    {
        $return = '';
        $user = Auth::user();

        if ($user->hasPermission('read-news')) {
            $return .= '<a class="me-3" href="'. route('admin.news.show', encrypt($data['id'])).'"
            data-grid="inic_grid_'.$this->uniqueID. '">
            <span class="inic inic-visibility fs-18"
            data-bs-toggle="tooltip" title="Show"></span></a>';
        }

        if ($user->hasPermission('update-news')) {
            $return .= '<a class="me-3" href="' . route('admin.news.edit', encrypt($data['id'])) . '">
            <span class="inic inic-edit fs-18"  data-bs-toggle="tooltip" title="Edit"></span></a>';
        }

        if ($user->hasPermission('delete-news')) {
            $return .= '<a class="delete-grid-row" href="javascript:void(0)" data-grid="inic_grid_'.$this->uniqueID.'"
            data-url="' . route('admin.news.destroy', encrypt($data['id'])) . '">
            <span class="inic inic-bin fs-18"  data-bs-toggle="tooltip" title="Delete"></span></a>';
        }

        return $return;
    }

   public function getColumnCheckbox(array $data): string
   {
        return '<input type="checkbox" value="' . $data['id'] . '" class="check_approval" />';
   }

    public function getDownloadColumnTitle(array $data): string
    {
        // Get the title, stripping JSON quotes if present
        $title = $data['title'] ?? '';
        $title = trim($title, '"');

        return $title;
    }
}
