<?php

namespace Modules\News\app\Repositories;

use Modules\News\app\Models\News;
use App\Repositories\Admin\Repository;

class NewsRepository extends Repository
{
   /**
     * NewsRepository constructor.
     * @param News $model
     */
    public function __construct(News $model)
    {
        $this->model = $model;
    }

    public function bulkAction($ids, $status, $actionType): array
    {
        $type = 'success';
        switch ($actionType) {
            case 'delete':
                foreach ($ids as $id) {
                    $news = $this->model->findOrFail($id);

                    // Clear all media before deleting
                    $news->clearMediaCollection('featured_image');

                    $news->delete();
                }
                $message = __('news::news.news_delete_successfully');
                $type = 'success';
                break;
            case 'publish':
                // Check for news without images before publishing
                $newsWithoutImages = $this->model->whereIn('id', $ids)
                    ->whereDoesntHave('media', function ($query) {
                        $query->where('collection_name', 'featured_image');
                    })
                    ->get(['id', 'title']);

                // Get news with images (can be published)
                $newsWithImages = $this->model->whereIn('id', $ids)
                    ->whereHas('media', function ($query) {
                        $query->where('collection_name', 'featured_image');
                    })
                    ->pluck('id')
                    ->toArray();

                // Publish news that have images
                if (!empty($newsWithImages)) {
                    $this->model->whereIn('id', $newsWithImages)->update(['publish' => 1]);
                }

                // Prepare response message
                if ($newsWithoutImages->isNotEmpty()) {
                    if (!empty($newsWithImages)) {
                        // Partial success - some published, some couldn't be
                        $message = __('news::news.partial_publish_completed', [
                            'published' => count($newsWithImages),
                            'failed' => count($newsWithoutImages)
                        ]);
                        $type = 'warning';
                    } else {
                        // No news could be published
                        $message = __('news::news.cannot_publish_news_without_featured_images');
                        $type = 'error';
                    }
                } else {
                    // All news published successfully
                    $message = __('news::news.status_updated_successfully');
                    $type = 'success';
                }
                break;
            case 'unpublish':
                $this->model->whereIn('id', $ids)->update(['publish' => 0]);
                $message = __('news::news.status_updated_successfully');
                $type = 'success';
                break;

            default:
                $type = 'error';
                $message = __('news::news.something_wrong');
                break;
        }
        return [
            'type' => $type,
            'message' => $message
        ];
    }

    /**
     * Get latest published news with author and category.
     *
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getLatestNews(int $limit = 4)
    {
        return News::with(['author', 'category'])
            ->where('publish', true)
            ->latest()
            ->take($limit)
            ->get();
    }

    /**
     * Get paginated published news with author and category for frontend.
     *
     * @param int $perPage
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getPaginatedPublishedNews(int $perPage = 9)
    {
        return News::with(['author', 'category'])
            ->where('publish', true)
            ->latest()
            ->paginate($perPage);
    }

    /**
     * Get a published news article by slug with author and category.
     *
     * @param string $slug
     * @return News|null
     */
    public function getPublishedNewsBySlug(string $slug): ?News
    {
        return News::with(['author', 'category'])
            ->where('publish', true)
            ->where('slug', $slug)
            ->first();
    }
}
