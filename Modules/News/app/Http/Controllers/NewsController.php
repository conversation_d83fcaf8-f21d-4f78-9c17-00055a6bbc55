<?php

namespace Modules\News\app\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Modules\News\app\DataGrids\NewsDataGrid;
use Modules\News\app\Models\News;
use App\Repositories\Admin\TagRepository;
use Modules\News\app\Http\Requests\NewsRequest;
use Modules\News\app\Repositories\NewsRepository;
use Modules\NewsCategory\app\Repositories\NewsCategoryRepository;
use Modules\Author\app\Repositories\AuthorRepository;
use App\Models\Tag;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;

class NewsController extends Controller
{
    public const INDEX_ROUTE = "admin.news.index";

    /**
     * NewsController constructor.
     * @param NewsRepository $newsRepository
     * @param NewsCategoryRepository $newsCategoryRepository
     * @param TagRepository $tagRepository
     * @param AuthorRepository $authorRepository
     */
    public function __construct(
        protected NewsRepository $newsRepository,
        protected NewsCategoryRepository $newsCategoryRepository,
        protected TagRepository $tagRepository,
        protected AuthorRepository $authorRepository
    ) {
        $this->middleware('permission:create-news', ['only' => ['create', 'store']]);
        $this->middleware('permission:update-news', ['only' => ['edit', 'update', 'changeStatus']]);
        $this->middleware(
            'permission:read-news|create-news|update-news|delete-news',
            ['only' => ['index']]
        );
        $this->middleware('permission:delete-news', ['only' => ['destroy']]);
    }

    public function index(): View|RedirectResponse
    {
        try {
            $dataGridHtml = NewsDataGrid::getHTML();
            return view('news::index', compact('dataGridHtml'));
        } catch (\Exception  $e) {
            return redirect()->back()->with(['type' => 'error', 'message' => __($e->getMessage())]);
        }
    }

    public function create(Request $request): View|JsonResponse
    {
        try {
            $categoryId         = isset($request->id) ? decrypt($request->id) : null;
            $newsCategory       = $this->newsCategoryRepository->where(["status" => true])->get()->pluck('name', 'id');
            $tags               = $this->tagRepository->pluck('name', 'name');
            $authors            = $this->authorRepository->getAuthors();
            $userNewsCategory   = null;

            if (isset($categoryId) && !empty($categoryId)) {
                $userNewsCategoryData   = $this->newsCategoryRepository->show($categoryId);
                $userNewsCategory       = $userNewsCategoryData->id;
            }

            return view('news::create', [
                'method'            => 'POST',
                'userNewsCategory'  => $userNewsCategory,
                'newsCategory'      => $newsCategory,
                'tags'              => $tags,
                'authors'           => $authors,
                'action'            => route('admin.news.store'),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage()),
            ]);
        }
    }

    public function store(NewsRequest $request): RedirectResponse
    {
        try {
            $data = $request->validated();

            // Handle boolean fields
            $data['publish'] = $request->publish == 'on' ? 1 : 0;
            $data['created_by'] = Auth::user()->id;

            $setNewsData = $this->newsRepository->create($data);

            // Handle media upload from cropped image
            if (!empty($request->input('cropped_image'))) {
                $croppedImage = $request->input('cropped_image');

                // Decode base64 image
                $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $croppedImage));

                // Create temporary file
                $tempFile = tempnam(sys_get_temp_dir(), 'news_image_');
                file_put_contents($tempFile, $imageData);

                try {
                    // Add media from file
                    $setNewsData->addMedia($tempFile)
                        ->usingName('Featured Image')
                        ->usingFileName('featured-image.jpg')
                        ->toMediaCollection('featured_image');
                } finally {
                    // Clean up temporary file only if it still exists
                    if (file_exists($tempFile)) {
                        unlink($tempFile);
                    }
                }
            }

            $tags = $data['tag'];

            // Create or get tags
            $tagIds = [];
            foreach ($tags as $tagName) {
                $tag = Tag::firstOrCreate(['name' => $tagName]);
                $tagIds[] = $tag->id;
            }

            // Sync tags with the news post
            $setNewsData->tags()->sync($tagIds);

            return redirect()
                ->route(self::INDEX_ROUTE)
                ->with([
                    'type' => 'success',
                    'message' => __('news::news.news_add_successfully'),
                ]);
        } catch (\Exception  $e) {
            return redirect()->back()->with([
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function show(News $news): View
    {
        return view('news::show', ['model' => $news]);
    }

    public function edit(News $news): View|RedirectResponse
    {
        try {
            $news->load('tags');

            $userNewsCategory = $this->newsCategoryRepository->show($news->category_id);
            $newsCategory = $this->newsCategoryRepository->where(["status" => true])->pluck('name', 'id');
            $tags = $this->tagRepository->pluck('name', 'name');
            $authors = $this->authorRepository->getAuthors();
            $selectedTags = $news->tags->pluck('name')->flatten()->all();

            return view('news::edit', [
                'is_update' => true,
                'model' => $news,
                'userNewsCategory' => $userNewsCategory->id,
                'newsCategory' => $newsCategory,
                'method' => 'PUT',
                'tags' => $tags,
                'authors' => $authors,
                'selectedTags' => $selectedTags,
                'action' => route('admin.news.update', encrypt($news->id)),
            ]);
        } catch (\Exception $e) {
            return redirect()->back()->with([
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function update(NewsRequest $request, News $news): RedirectResponse
    {
        try {
            $data = $request->validated();
            $tags = $data['tag'];

            // Handle boolean fields
            if ($request->has('publish') && $request->input('publish') === 'on') {
                $data['publish'] = true;
            } else {
                $data['publish'] = false;
            }

            $this->newsRepository->update($data, $news->id);

            // Handle media upload from cropped image
            if (!empty($request->input('cropped_image'))) {
                // Clear existing media first
                $news->clearMediaCollection('featured_image');

                $croppedImage = $request->input('cropped_image');

                // Decode base64 image
                $imageData = base64_decode(preg_replace('#^data:image/\w+;base64,#i', '', $croppedImage));

                // Create temporary file
                $tempFile = tempnam(sys_get_temp_dir(), 'news_image_');
                file_put_contents($tempFile, $imageData);

                try {
                    // Add media from file
                    $news->addMedia($tempFile)
                        ->usingName('Featured Image')
                        ->usingFileName('featured-image.jpg')
                        ->toMediaCollection('featured_image');
                } finally {
                    // Clean up temporary file only if it still exists
                    if (file_exists($tempFile)) {
                        unlink($tempFile);
                    }
                }
            }

            // Create or get tags
            $tagIds = [];
            foreach ($tags as $tagName) {
                $tag = Tag::firstOrCreate(['name' => $tagName]);
                $tagIds[] = $tag->id;
            }

            // Sync tags with the news post
            $news->tags()->sync($tagIds);

            return redirect()
                ->route(self::INDEX_ROUTE)
                ->with([
                    'type' => 'success',
                    'message' => __('news::news.news_update_successfully'),
                ]);
        } catch (\Exception  $e) {
            return redirect()->back()->with([
                'type' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }

    public function destroy(News $news): JsonResponse
    {
        try {
            if ($news) {
                // Clear all media first
                $news->clearMediaCollection('featured_image');

                $this->newsRepository->delete($news->id);
            }
            return response()->json([
                'status' => 'success',
                'message' => __('news::news.news_delete_successfully'),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage()),
            ]);
        }
    }

    public function changeStatus(Request $request): JsonResponse
    {
        try {
            $id = $request->input('id');
            if (!$id) {
                return response()->json(['status' => 'error', 'message' => __('news::news.something_wrong')]);
            }
            $model = $this->newsRepository->show($id);
            $newStatus = !$model->publish;

            // If trying to publish news, check if image exists
            if ($newStatus === true && !$model->hasMedia('featured_image')) {
                return response()->json([
                    'type' => 'error',
                    'message' => __('news::news.cannot_publish_without_featured_image')
                ], 422);
            }

            $data['publish'] = $newStatus;
            $status = $data['publish'] ? 'Published' : 'Unpublished';
            $this->newsRepository->update($data, $id);

            return response()->json(
                [
                    'status' => 'success',
                    'message' => __(
                        'news::news.status_updated',
                        ['status' => $status]
                    )
                ]
            );
        } catch (\Exception $e) {
            return response()->json(['status' => 'error', 'message' => __($e->getMessage())]);
        }
    }

    public function bulkAction(Request $request): JsonResponse
    {
        try {
            $ids = $request->id;
            $status = $request->status;
            $actionType = $request->type;
            $result = $this->newsRepository->bulkAction($ids, $status, $actionType);

            return response()->json([
                'status' => $result['type'],
                'message' => $result['message']
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => __($e->getMessage())
            ]);
        }
    }
}
