<?php

namespace Modules\News\app\Http\Requests;

use App\Rules\StripTags;
use Illuminate\Foundation\Http\FormRequest;

class NewsRequest extends FormRequest
{
    protected const SLUG_REGEX = 'regex:/^[^\s]+(\s*[^\s]+)*$/';
    protected const SLUG_UNIQUE_VALIDATION = 'unique:news,slug';
    protected const STRING_REGEX = 'regex:/^[a-zA-Z0-9\s]+$/';
    protected const STRING = 'string';

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $maxSize = 'max:255';
        $rules = [
            // Translatable fields
            'title' => ['required', 'array'],
            'title.en' => ['required', self::STRING, $maxSize],
            'title.ar' => ['nullable', self::STRING, $maxSize],
            'content' => ['required', 'array'],
            'content.en' => ['required'],
            'content.ar' => ['nullable'],
            // Regular fields
            'meta_keywords' => ['required', self::STRING, self::STRING_REGEX, $maxSize],
            'meta_description' => ['required', $maxSize, new StripTags],
            'category_id' => ['required'],
            'author_id' => ['nullable', 'exists:authors,id'],
            'tag' => ['required', $maxSize],
            // Image validation - required when publish is true
            'cropped_image' => [
                function ($attribute, $value, $fail) {
                    $request = request();
                    $publish = $request->input('publish');

                    // If publish is checked/true, image is required
                    if ($publish === 'on' || $publish === true || $publish == 1) {
                        // Check if creating new news
                        $routeName = explode('.', request()->route()->getName());
                        $route = array_pop($routeName);

                        if (in_array($route, ['create', 'store'])) {
                            // Creating new news - image is required
                            if (empty($value)) {
                                $fail(__('news::news.featured_image_required_when_published'));
                            }
                        } elseif (in_array($route, ['edit', 'update'])) {
                            // Updating existing news - check if image exists or being uploaded
                            $news = request()->route()->parameter('news');
                            $hasExistingImage = $news && $news->hasMedia('featured_image');

                            if (empty($value) && !$hasExistingImage) {
                                $fail(__('news::news.featured_image_required_when_published'));
                            }
                        }
                    }
                }
            ],
        ];

        $routeName = explode('.', request()->route()->getName());
        $route = array_pop($routeName);
        $slugRules = ['required'];

        switch ($route) {
            case 'create':
            case 'store':
                $rules = array_merge($rules, [
                    'slug' => array_merge($slugRules, [self::SLUG_UNIQUE_VALIDATION]),
                ]);
                break;
            case 'edit':
            case 'update':
                $news = request()->route()->parameter('news');
                $rules = array_merge($rules, [
                    'slug' => array_merge($slugRules, [self::SLUG_UNIQUE_VALIDATION . ',' . $news->id . ',id']),
                ]);
                break;
            default:
                return $rules;
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            // General required validations
            'category_id.required' => __('news::news.category_id_required'),
            'category_id.exists' => __('news::news.category_id_exists'),

            // Title validations
            'title.required' => __('news::news.title_required'),
            'title.array' => __('news::news.title_array'),
            'title.en.required' => __('news::news.title_en_required'),
            'title.ar.required' => __('news::news.title_ar_required'),
            'title.en.string' => __('news::news.title_en_string'),
            'title.ar.string' => __('news::news.title_ar_string'),
            'title.en.max' => __('news::news.title_en_max'),
            'title.ar.max' => __('news::news.title_ar_max'),

            // Content validations
            'content.required' => __('news::news.content_required'),
            'content.array' => __('news::news.content_array'),
            'content.en.required' => __('news::news.content_en_required'),
            'content.en.string' => __('news::news.content_en_string'),
            'content.ar.string' => __('news::news.content_ar_string'),

            // Slug validations
            'slug.required' => __('news::news.slug_required'),
            'slug.string' => __('news::news.slug_string'),
            'slug.max' => __('news::news.slug_max'),
            'slug.unique' => __('news::news.slug_unique'),
            'slug.regex' => __('news::news.slug_regex'),

            // Meta fields validations
            'meta_keywords.required' => __('news::news.meta_keywords_required'),
            'meta_keywords.string' => __('news::news.meta_keywords_string'),
            'meta_keywords.max' => __('news::news.meta_keywords_max'),
            'meta_keywords.regex' => __('news::news.meta_keywords_regex'),
            'meta_description.required' => __('news::news.meta_description_required'),
            'meta_description.string' => __('news::news.meta_description_string'),
            'meta_description.max' => __('news::news.meta_description_max'),

            // Tag validations
            'tag.required' => __('news::news.tag_required'),
            'tag.string' => __('news::news.tag_string'),
            'tag.max' => __('news::news.tag_max'),

            // Image validation
            'cropped_image.required_if' => __('news::news.featured_image_required_when_published'),
        ];
    }
}
