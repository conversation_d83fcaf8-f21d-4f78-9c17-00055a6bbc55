<form method="POST" action="{{ $action }}" enctype="multipart/form-data">
    @csrf
    @if (isset($model))
        @method('PUT')
    @endif

    <!-- Language Switcher -->
    @include('news::partials.language-switcher')

    @if($newsCategory->isEmpty())
        <div class="alert alert-danger">
            <strong>Note:</strong> Please check category is active or not before creating news post or updating news post details
        </div>
    @endif

    <div class="row">
        <!-- Card for translatable fields -->
        <div class="col-md-12 mb-4">
            <div class="card border-primary">
                <div class="card-header bg-primary bg-opacity-10">
                    <h5 class="mb-0"><i class="fa fa-language"></i> {{ __('admin.common.translatable_fields') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Title (translatable) -->
                        <div class="col-md-6 mb-3">
                            <label for="title_en" class="form-label">{{ __('news::news.title') }}</label>
                            <div class="form-group {{ $errors->has('title.en') ? 'has-error' : '' }} translatable-field" data-language="en">
                                <input type="text" name="title[en]" id="title_en" class="form-control"
                                    value="{{ old('title.en', isset($model) ? $model->getTranslation('title', 'en', false) : '') }}"
                                    placeholder="{{ __('news::news.title_placeholder') }}">
                                @if ($errors->has('title.en'))
                                    <span class="help-block">{{ $errors->first('title.en') }}</span>
                                @endif
                            </div>
                            <div class="form-group {{ $errors->has('title.ar') ? 'has-error' : '' }} translatable-field" data-language="ar" style="display: none;">
                                <input type="text" name="title[ar]" id="title_ar" class="form-control" dir="rtl"
                                    value="{{ old('title.ar', isset($model) ? $model->getTranslation('title', 'ar', false) : '') }}"
                                    placeholder="{{ __('news::news.title_placeholder') }}">
                                @if ($errors->has('title.ar'))
                                    <span class="help-block">{{ $errors->first('title.ar') }}</span>
                                @endif
                            </div>
                        </div>

                        <!-- Author -->
                        <div class="col-md-6 mb-3">
                            <label for="author_id" class="form-label">{{ __('news::news.author') }}</label>
                            <select name="author_id" id="author_id" class="form-select">
                                <option value="">Select Author</option>
                                @foreach($authors as $author)
                                    <option value="{{ $author->id }}" {{ old('author_id', $model->author_id ?? '') == $author->id ? 'selected' : '' }}>
                                        {{ $author->getTranslation('name', 'en') }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Content (translatable) -->
                        <div class="col-md-12 mb-3">
                            <label for="content" class="form-label">{{ __('news::news.content') }}</label>
                            <div class="translatable-field" data-language="en">
                                <textarea name="content[en]" id="content_en" class="form-control ck-editor">{{ old('content.en', isset($model) ? $model->getTranslation('content', 'en', false) : '') }}</textarea>
                            </div>
                            <div class="translatable-field" data-language="ar" style="display: none;">
                                <textarea name="content[ar]" id="content_ar" class="form-control ck-editor" dir="rtl">{{ old('content.ar', isset($model) ? $model->getTranslation('content', 'ar', false) : '') }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card for non-translatable fields -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fa fa-cog"></i> {{ __('admin.common.general_settings') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Slug -->
                        <div class="col-md-6 mb-3">
                            <label for="slug" class="form-label">{{ __('news::news.slug') }}</label>
                            <input type="text" name="slug" id="slug" class="form-control"
                                value="{{ old('slug', $model->slug ?? '') }}"
                                placeholder="{{ __('news::news.slug_placeholder') }}">
                        </div>

                        <!-- Category -->
                        <div class="col-md-6 mb-3">
                            <label for="category_id" class="form-label">{{ __('news::news.category') }}</label>
                            <select name="category_id" id="category_id" class="form-select">
                                @foreach($newsCategory as $key => $category)
                                    <option value="{{ $key }}" {{ old('category_id', $userNewsCategory ?? '') == $key ? 'selected' : '' }}>
                                        {{ $category }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Tags -->
                        <div class="col-md-6 mb-3">
                            <label for="tag" class="form-label">{{ __('news::news.tag') }}</label>
                            <select name="tag[]" id="tag" class="form-control select2-with-tags" multiple>
                                @foreach($tags as $key => $tag)
                                    <option value="{{ $key}}" {{ in_array($key, old('tag', $selectedTags ?? [])) ? 'selected' : '' }}>
                                        {{ $tag }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Publish -->
                        <div class="col-md-6 mb-3">
                            <label for="publish" class="form-label">{{ __('news::news.publish') }}</label>
                            <div class="form-check form-switch">
                                <input type="checkbox" name="publish" id="publish" class="form-check-input my-switch"
                                    {{ old('publish', $model->publish ?? false) ? 'checked' : '' }}>
                                <label class="form-check-label" for="publish">Published</label>
                            </div>
                        </div>

                        <!-- Meta Keywords -->
                        <div class="col-md-6 mb-3">
                            <label for="meta_keywords" class="form-label">{{ __('news::news.meta_keywords') }}</label>
                            <input type="text" name="meta_keywords" id="meta_keywords" class="form-control"
                                value="{{ old('meta_keywords', $model->meta_keywords ?? '') }}"
                                placeholder="{{ __('news::news.meta_keywords_placeholder') }}">
                        </div>

                        <!-- Meta Description -->
                        <div class="col-md-6 mb-3">
                            <label for="meta_description" class="form-label">{{ __('news::news.meta_description') }}</label>
                            <textarea name="meta_description" id="meta_description" class="form-control" rows="3">{{ old('meta_description', $model->meta_description ?? '') }}</textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Card for image upload -->
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fa fa-image"></i> {{ __('admin.common.featured_image') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <!-- Image Preview Container -->
                            <div class="image-upload-container mb-3">
                                <div class="image-preview-wrapper">
                                    {{-- For optimal performance, you can use WebP with fallback:
                                    <picture>
                                        <source srcset="{{ isset($model) ? $model->getFeaturedImageWebpUrl('main') : '' }}" type="image/webp">
                                        <img src="{{ isset($model) ? $model->getFeaturedImageUrl('main') : asset('images/banner-img.png') }}"
                                             class="imagePreview img-fluid" id="preview_cropped_image" alt="Featured Image">
                                    </picture>
                                    --}}

                                    <img src="{{ isset($model) ? $model->getFeaturedImageUrl('main') : asset('images/banner-img.png') }}"
                                         class="imagePreview img-fluid rounded" id="preview_cropped_image" alt="Featured Image">
                                </div>

                                <!-- Upload Controls -->
                                <div class="upload-controls mt-3">
                                    <div class="d-flex align-items-center justify-content-center gap-2">
                                        <input type="hidden" name="cropped_image" id="cropped_image">
                                        <input type="file" name="image" id="image" class="image-cropper d-none"
                                               accept="image/png,image/jpg,image/jpeg,image/webp"
                                               data-preview="cropped_image" data-width="800" data-height="800">
                                        <button type="button" class="btn btn-primary btn-sm" onclick="document.getElementById('image').click()">
                                            <i class="fa fa-upload me-1"></i> {{ __('admin.common.upload') }}
                                        </button>
                                    </div>
                                    <div class="mt-2 text-center">
                                        <small class="text-muted">
                                            <i class="fa fa-info-circle"></i>
                                            {{ __('news::news.image_upload_info') }}
                                        </small>
                                    </div>
                                </div>
                            </div>

                            @if(isset($model) && $model->hasMedia('featured_image'))
                                <div class="alert alert-info alert-sm">
                                    <i class="fa fa-info-circle"></i>
                                    {{ __('admin.common.current_image_will_be_replaced') }}
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Form buttons -->
        <div class="col-md-12">
            <div class="d-flex align-items-center justify-content-end gap-3">
                <button type="submit" class="btn btn-primary">{{ __('admin.common.submit') }}</button>
                <a href="{{ route('admin.news.index') }}" class="btn btn-secondary">{{ __('admin.common.cancel') }}</a>
            </div>
        </div>
    </div>
</form>

@include('plugins.ckeditor')
@include('plugins.select2')
@include('plugins.cropper')

{{-- Include shared image upload styles and scripts --}}
@push('css')
    <link rel="stylesheet" href="{{ asset('css/admin-image-upload.css') }}">
@endpush

@push('scripts')
    <script src="{{ asset('js/admin-image-upload.js') }}" defer></script>
@endpush

@push('scripts')
    @php
        $validator = JsValidator::formRequest('Modules\News\app\Http\Requests\NewsRequest');
        $validator->ignore('');
    @endphp
    {!! $validator !!}

    <script type="module">
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize CKEditors for both languages
            initializeCKEditor('content_en', options);

            // Set up Arabic CKEditor with RTL direction when needed
            const arOptions = Object.assign({}, options, {
                contentsLangDirection: 'rtl'
            });
            initializeCKEditor('content_ar', arOptions);

            // Hide the Arabic editor initially
            if (typeof CKEDITOR !== 'undefined') {
                if (CKEDITOR.instances.content_ar && CKEDITOR.instances.content_ar.container) {
                    CKEDITOR.instances.content_ar.container.setStyle('display', 'none');
                }
            }

            // Initialize selected tags
            let selectedTags = <?php echo json_encode($selectedTags ?? []); ?>;
            if (Array.isArray(selectedTags) && selectedTags.length > 0) {
                $('.select2-with-tags').val(selectedTags).trigger('change');
            }

            // Initialize slug generation for news
            initializeSlugGeneration('#title_en', '#slug');

            // Image validation when publish status changes
            initializeImageValidation();
        });

    function initializeImageValidation() {
        const publishCheckbox = document.getElementById('publish');
        const croppedImageInput = document.getElementById('cropped_image');
        const imageUploadContainer = $('.image-upload-container');
        const imageInput = document.getElementById('image');

        // Check if editing existing news with image
        const hasExistingImage = @json(isset($model) && $model->hasMedia('featured_image'));

        // Track if user has interacted with publish checkbox
        let publishChanged = false;

        function validateImageRequirement(showMessage = true) {
            const isPublished = publishCheckbox.checked;
            const hasImage = croppedImageInput.value || hasExistingImage;

            // Remove any existing validation messages
            $('.image-validation-message').remove();
            imageUploadContainer.removeClass('border-danger');

            if (isPublished && !hasImage) {
                if (showMessage) {
                    // Show validation message
                    const message = '<div class="alert alert-danger alert-sm image-validation-message mt-2">' +
                        '<i class="fa fa-exclamation-triangle"></i> ' +
                        '{{ __("news::news.featured_image_required_when_published") }}' +
                        '</div>';
                    imageUploadContainer.after(message);
                    imageUploadContainer.addClass('border-danger');
                }
                return false;
            }

            return true;
        }

        // Validate on publish checkbox change
        if (publishCheckbox) {
            publishCheckbox.addEventListener('change', function() {
                publishChanged = true;
                validateImageRequirement(true);
            });
        }

        // Validate when image is uploaded/cropped (only if publish was changed)
        if (croppedImageInput) {
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'value') {
                        // Only show validation if user has interacted with publish
                        validateImageRequirement(publishChanged);
                    }
                });
            });

            observer.observe(croppedImageInput, {
                attributes: true,
                attributeFilter: ['value']
            });

            // Also listen for input event
            croppedImageInput.addEventListener('input', function() {
                // Only show validation if user has interacted with publish
                validateImageRequirement(publishChanged);
            });
        }

        // Validate on form submission (always validate on submit)
        $('form').on('submit', function(e) {
            if (!validateImageRequirement(true)) {
                e.preventDefault();

                // Scroll to validation message
                $('html, body').animate({
                    scrollTop: $('.image-validation-message').offset().top - 100
                }, 500);

                return false;
            }
        });
    }
    </script>
@endpush
