@extends('admin.layouts.page')
@section('title', __('news::news.news'))

@section('breadcrumb')
    <ol class="breadcrumb-item active">{{ __('news::news.news') }}</ol>
@endsection

@permission('create-news')
    @section('action_buttons')
    <a href="{{ route('admin.news.create') }}" class="btn btn-primary">
        <em class="inic inic-add"></em> {{ __('news::news.add_news') }}
    </a>
    @endsection
@endpermission

@section('content')
    {!! $dataGridHtml !!}
@endsection

@include('plugins.sweetalert2')
@push('scripts')

    <script type="module">
        $(document).on('change','.change-status', function(){
            var id =  $(this).attr('data-id');
            var $checkbox = $(this);

            $.ajax({
                url: "{{ route('admin.news.change_status')}}",
                type: "post",
                data: {"id": id},
                datatype: 'json',
                success: function (response) {
                   toastr.success(response.message);
                   window.inic_grid_news.refresh();
                },
                error: function (xhr) {
                    // Reset checkbox to previous state
                    $checkbox.prop('checked', !$checkbox.prop('checked'));

                    // Show error message
                    var response = xhr.responseJSON;
                    if (response && response.message) {
                        toastr.error(response.message);
                    } else {
                        toastr.error('{{ __("news::news.something_wrong") }}');
                    }
                }
            });
        });

        let csrfToken = $('meta[name="csrf-token"]').attr('content');
        $(document).on('click', '.delete-grid-row', function (event) {
            event.preventDefault();
            let url = $(this).attr('data-url');
            SwalConfirm(url, function (url) {
                $.ajax({
                    url: url,
                    type: 'delete',
                    datatype: 'json',
                    headers: {
                        'X-CSRF-TOKEN': csrfToken
                    },
                    success: function (response) {
                        toastr.success(response.message);
                        window.inic_grid_news.refresh();
                    },
                    error: function (response) {
                        toastr.error(response.message);
                    }
                });
            })
        })

        $(document).on('click', '.grid-bulk-action', function(event) {
            event.preventDefault();
            var id = [];var error = 0;
            $('.check_approval').each(function(index){
                if($(this).prop('checked') == true){
                    id.push($(this).val());
                    error = 1;
                }
            });

            var url= "{{ route('admin.news.bulkAction') }}";
            var status=$(this).data('status');
            var type=$(this).data('action-type');

            if(error == 0){
                toastr.error("{{ __('news::news.please_select_one_news') }}");
            }else if(error == 1){
                SwalConfirm(url, function(url) {
                    $.ajax({
                        url: url,
                        type: 'post',
                        data: {id: id,status:status,type:type},
                        success: function(response) {
                            if(response.status == 'success'){
                                toastr.success(response.message);
                            }else if(response.status == 'warning'){
                                toastr.warning(response.message);
                            }else{
                                toastr.error(response.message);
                            }
                            window.inic_grid_news.refresh();
                        },
                        error: function(xhr) {
                            var response = xhr.responseJSON;
                            if (response && response.message) {
                                toastr.error(response.message);
                            } else {
                                toastr.error('{{ __("news::news.something_wrong") }}');
                            }
                            window.inic_grid_news.refresh();
                        }

                    });
                });
            }
        });

    </script>
@endpush
