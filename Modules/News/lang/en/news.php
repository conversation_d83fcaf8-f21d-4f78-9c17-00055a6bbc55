<?php

return [
    // News Management
    'news' => 'News',
    'add_news' => 'Add News',
    'edit_news' => 'Edit News',
    'view_news' => 'View News',
    'news_name' => 'News Name',
    'status' => 'Status',
    'action' => 'Action',

    // Form Fields
    'title' => 'Title',
    'slug' => 'Slug',
    'meta_keywords' => 'Meta Keywords',
    'meta_description' => 'Meta Description',
    'category' => 'Category',
    'author' => 'Author',
    'content' => 'Content',
    'tag' => 'Tag',
    'image' => 'Upload Media',
    'publish' => 'Publish',
    'unpublish' => 'Unpublish',
    'created_at' => 'Created On',
    'created_by' => 'Created By',
    'publish_datagridname' => 'Publish', // Datagrid Column name

    // Form Placeholders
    'title_placeholder' => 'Enter Title',
    'slug_placeholder' => 'Enter Slug',
    'meta_keywords_placeholder' => 'Enter Meta Keyword',
    'meta_description_placeholder' => 'Enter Meta Description',
    'tag_placeholder' => 'Enter Tag',

    // Status Messages
    'news_add_successfully' => 'News Added Successfully.',
    'news_update_successfully' => 'News Updated Successfully.',
    'news_delete_successfully' => 'News deleted Successfully.',
    'news_publish_successfully' => 'News Publish Successfully.',
    'news_unpublish_successfully' => 'News Unpublish Successfully.',
    'status_updated_successfully' => 'News status updated successfully.',
    'status_updated' => 'News has been :status successfully.',
    'something_wrong' => 'Something went wrong.',

    // Selection Messages
    'please_select_one_news' => 'Please select at least 1 news.',
    'image_upload_info' => 'Recommended size: 800x800px. Supported formats: JPG, PNG, WebP',

    // Image Validation Messages
    'featured_image_required_when_published' => 'A featured image is required when the news is set to published.',
    'cannot_publish_without_featured_image' => 'Cannot publish news without a featured image. Please upload an image first.',
    'cannot_publish_news_without_featured_images' => 'Cannot publish news without featured images. Please upload images first.',
    'partial_publish_completed' => ':published news published successfully. Could not publish :failed news without featured images.',

    // Status Values
    'delete' => 'Delete',

    // Validation Messages - General Required
    'category_id_required' => 'The Category field is required.',
    'category_id_exists' => 'The selected Category does not exist.',

    // Validation Messages - Title
    'title_required' => 'The Title field is required.',
    'title_array' => 'The Title field is required.',
    'title_en_required' => 'The Title (English) field is required.',
    'title_ar_required' => 'The Title (Arabic) field is required.',
    'title_en_string' => 'The Title (English) must be a string.',
    'title_ar_string' => 'The Title (Arabic) must be a string.',
    'title_en_max' => 'The Title (English) field must not exceed 255 characters.',
    'title_ar_max' => 'The Title (Arabic) field must not exceed 255 characters.',

    // Validation Messages - Content
    'content_required' => 'The Content field is required.',
    'content_array' => 'The Content field is required.',
    'content_en_required' => 'The Content (English) field is required.',
    'content_en_string' => 'The Content (English) must be a string.',
    'content_ar_string' => 'The Content (Arabic) must be a string.',

    // Validation Messages - Slug
    'slug_required' => 'The Slug field is required.',
    'slug_string' => 'The Slug must be a string.',
    'slug_max' => 'The Slug must not exceed 255 characters.',
    'slug_unique' => 'The Slug has already been taken.',
    'slug_regex' => 'The Slug format is invalid.',

    // Validation Messages - Meta Fields
    'meta_keywords_required' => 'The Meta Keywords field is required.',
    'meta_keywords_string' => 'The Meta Keywords must be a string.',
    'meta_keywords_max' => 'The Meta Keywords must not exceed 255 characters.',
    'meta_keywords_regex' => 'The Meta Keywords format is invalid.',
    'meta_description_required' => 'The Meta Description field is required.',
    'meta_description_string' => 'The Meta Description must be a string.',
    'meta_description_max' => 'The Meta Description must not exceed 255 characters.',

    // Validation Messages - Author (nullable field)
    'author_string' => 'The Author must be a string.',
    'author_regex' => 'The Author format is invalid.',

    // Validation Messages - Tag
    'tag_required' => 'The Tag field is required.',
    'tag_string' => 'The Tag must be a string.',
    'tag_max' => 'The Tag must not exceed 255 characters.',

    // Validation Messages - Image
    'image_required' => 'The Image field is required.',
    'image_mimes' => 'The Image must be a file of type: jpeg, jpg, png, webp.',
    'image_max' => 'The Image must not be larger than 10MB.',
];
