@extends('admin.layouts.page')
@section('title', __('testimonial::testimonial.view_testimonial'))

@section('breadcrumb')
    <ol class="breadcrumb-item"><a href="{{ route('admin.testimonials.index') }}">{{ __('testimonial::testimonial.testimonials') }}</a></ol>
    <ol class="breadcrumb-item active">{{ __('testimonial::testimonial.view_testimonial') }}</ol>
@endsection

@section('action_buttons')
    {!! backButton(route('admin.testimonials.index'), 'Back','Testimonial') !!}
@endsection

@section('content')
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">{{ __('testimonial::testimonial.testimonial_content') }}</h4>
                        <p class="card-title-desc">{{ __('testimonial::testimonial.view_testimonial') }}</p>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- Profile Image -->
                            <div class="col-md-3 mb-4 text-center">
                                <div class="profile-image-container">
                                    <img src="{{ $testimonial->getProfileImageUrl('preview') }}"
                                         alt="{{ $testimonial->getTranslation('author_name', 'en') }}"
                                         class="img-fluid rounded-circle"
                                         style="width: 150px; height: 150px; object-fit: cover;">
                                    @if($testimonial->hasMedia('profile_image'))
                                        <div class="mt-2">
                                            <small class="text-muted">
                                                <i class="fa fa-calendar"></i>
                                                {{ __('admin.common.uploaded') }}: {{ $testimonial->getFirstMedia('profile_image')->created_at->format('M d, Y') }}
                                            </small>
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Testimonial Details -->
                            <div class="col-md-9">
                                <div class="row">
                                    <!-- Author Name -->
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">{{ __('testimonial::testimonial.author_name') }}</label>
                                        <div class="form-control-plaintext">
                                            <strong>English:</strong> {{ $testimonial->getTranslation('author_name', 'en') ?: __('testimonial::testimonial.not_available') }}<br>
                                            <strong>Arabic:</strong> {{ $testimonial->getTranslation('author_name', 'ar', false) ?: __('testimonial::testimonial.not_available') }}
                                        </div>
                                    </div>

                                    <!-- Author Type -->
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">{{ __('testimonial::testimonial.author_type') }}</label>
                                        <div class="form-control-plaintext">
                                            <strong>English:</strong> {{ $testimonial->getTranslation('author_type', 'en') ?: __('testimonial::testimonial.not_available') }}<br>
                                            <strong>Arabic:</strong> {{ $testimonial->getTranslation('author_type', 'ar', false) ?: __('testimonial::testimonial.not_available') }}
                                        </div>
                                    </div>

                                    <!-- Display Order -->
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">{{ __('testimonial::testimonial.display_order') }}</label>
                                        <div class="form-control-plaintext">{{ $testimonial->display_order }}</div>
                                    </div>

                                    <!-- Status -->
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">{{ __('testimonial::testimonial.status') }}</label>
                                        <div class="form-control-plaintext">
                                            @if($testimonial->status)
                                                <span class="badge bg-success">{{ __('testimonial::testimonial.active') }}</span>
                                            @else
                                                <span class="badge bg-secondary">{{ __('testimonial::testimonial.inactive') }}</span>
                                            @endif
                                        </div>
                                    </div>

                                    <!-- Created At -->
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">{{ __('testimonial::testimonial.created_at') }}</label>
                                        <div class="form-control-plaintext">{{ adminDateTimeFormatShow($testimonial->created_at) }}</div>
                                    </div>

                                    <!-- Updated At -->
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label fw-bold">{{ __('testimonial::testimonial.updated_at') }}</label>
                                        <div class="form-control-plaintext">{{ adminDateTimeFormatShow($testimonial->updated_at) }}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Testimonial Content -->
                            <div class="col-md-12 mt-4">
                                <div class="card border-primary" style="border-width: 2px;">
                                    <div class="card-header bg-primary bg-opacity-10">
                                        <h5 class="mb-0"><i class="fa fa-quote-left"></i> {{ __('testimonial::testimonial.testimonial_content') }}</h5>
                                    </div>
                                    <div class="card-body">
                                        <!-- English Content -->
                                        <div class="mb-4">
                                            <h6 class="fw-bold text-primary">English</h6>
                                            <div class="testimonial-content p-3 bg-light rounded">
                                                {!! $testimonial->getTranslation('testimonial', 'en') ?: '<em class="text-muted">' . __('testimonial::testimonial.not_available') . '</em>' !!}
                                            </div>
                                        </div>

                                        <!-- Arabic Content -->
                                        <div class="mb-0">
                                            <h6 class="fw-bold text-primary">Arabic</h6>
                                            <div class="testimonial-content p-3 bg-light rounded" dir="rtl">
                                                {!! $testimonial->getTranslation('testimonial', 'ar', false) ?: '<em class="text-muted">' . __('testimonial::testimonial.not_available') . '</em>' !!}
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    .testimonial-content {
        min-height: 100px;
        line-height: 1.6;
    }

    .profile-image-container {
        position: relative;
    }

    .form-control-plaintext {
        padding: 0.375rem 0;
        margin-bottom: 0;
        font-size: 0.875rem;
        line-height: 1.5;
        color: #495057;
        background-color: transparent;
        border: solid transparent;
        border-width: 1px 0;
    }
</style>
@endpush
