@extends('admin.layouts.page')
@section('title', __('testimonial::testimonial.testimonials'))

@section('breadcrumb')
    <ol class="breadcrumb-item active">{{ __('testimonial::testimonial.testimonials') }}</ol>
@endsection

@permission('create-testimonial')
    @section('action_buttons')
    <a href="{{ route('admin.testimonials.create') }}" class="btn btn-primary">
        <em class="inic inic-add"></em> {{ __('testimonial::testimonial.add_testimonial') }}
    </a>
    @endsection
@endpermission

@section('content')
    {!! $dataGridHtml !!}
@endsection

@include('plugins.sweetalert2')
@push('scripts')
<script type="module">
    $(document).on('change','.change-status', function(){
        var id =  $(this).attr('data-id');
        $.ajax({
            url: "{{ route('admin.testimonials.change-status')}}",
            type: "post",
            data: {"id": id},
            datatype: 'json',
            success: function (response) {
                toastr.success(response.message);
                window.inic_grid_testimonials.refresh();
            },
            error: function (response) {
            toastr.error(response.message);
            }
        });
    });

    let csrfToken = $('meta[name="csrf-token"]').attr('content');
    $(document).on('click', '.delete-grid-row', function (event) {
        event.preventDefault();
        let url = $(this).attr('data-url');
        SwalConfirm(url, function (url) {
            $.ajax({
                url: url,
                type: 'delete',
                datatype: 'json',
                headers: {
                    'X-CSRF-TOKEN': csrfToken
                },
                success: function (response) {
                    toastr.success(response.message);
                    window.inic_grid_testimonials.refresh();
                },
                error: function (response) {
                    toastr.error(response.message);
                }
            });
        })
    })

    $(document).on('click', '.grid-bulk-action', function(event) {
        event.preventDefault();
        var id = [];var error = 0;
        $('.check_approval').each(function(index){
            if($(this).prop('checked') == true){
                id.push($(this).val());
                error = 1;
            }
        });

        var url= "{{ route('admin.testimonials.bulk-action') }}";
        var status=$(this).data('status');
        var type=$(this).data('action-type');

        if(error == 0){
            toastr.error("{{ __('testimonial::testimonial.no_records_selected') }}");
        }else if(error == 1){
            SwalConfirm(url, function(url) {
                $.ajax({
                    url: url,
                    type: 'post',
                    data: {id: id,status:status,type:type},
                    success: function(response) {
                        if(response.status == 'success'){
                            toastr.success(response.message);
                        }else{
                            toastr.error(response.message);
                        }
                        window.inic_grid_testimonials.refresh();
                    },
                    error: function(response) {
                        toastr.error(response.message);
                    }

                });
            });
        }
    });
</script>
@endpush
