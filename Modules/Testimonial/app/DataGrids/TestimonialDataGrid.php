<?php

namespace Modules\Testimonial\app\DataGrids;

use Illuminate\Support\Facades\Vite;
use Illuminate\Support\Facades\Auth;
use Indianic\LaravelDataGrid\LaravelDataGrid;
use Modules\Testimonial\Models\Testimonial;
use Illuminate\Support\Facades\DB;

class TestimonialDataGrid extends LaravelDataGrid
{
    public $guard = 'admin';

    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'testimonials';

    /**
     * Define how many rows you want to display on a page
     * @var int $recordsPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define row per page dropdown options
     * @var array $recordsPerPageOptions
     */
    public $recordsPerPageOptions;

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy = 'display_order';

    /**
     * Define default sorting direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection = 'ASC';

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'testimonials';

    // Translation constants
    protected const AUTHOR_NAME = 'testimonial::testimonial.author_name';
    protected const AUTHOR_TYPE = 'testimonial::testimonial.author_type';
    protected const TESTIMONIAL = 'testimonial::testimonial.testimonial';
    protected const STATUS = 'testimonial::testimonial.status';
    protected const DISPLAY_ORDER = 'testimonial::testimonial.display_order';
    protected const CREATED_AT = 'testimonial::testimonial.created_at';
    protected const ACTION = 'testimonial::testimonial.action';

    /**
     * Get Resource of Query Builder
     */
    public function resource()
    {
        return Testimonial::select(
            'testimonials.id',
            'testimonials.status',
            'testimonials.display_order',
            'testimonials.created_at as testimonial_created_at',
            DB::raw("JSON_EXTRACT(testimonials.author_name, '$.en') as author_name"),
            DB::raw("JSON_EXTRACT(testimonials.author_type, '$.en') as author_type"),
            DB::raw("JSON_EXTRACT(testimonials.testimonial, '$.en') as testimonial_text")
        )->with('media');
    }

    public function columns(): array
    {
        return [
            'checkbox' => '<input class="select_all checkbox" type="checkbox" />',
            'author_name' => __(self::AUTHOR_NAME),
            'author_type' => __(self::AUTHOR_TYPE),
            'testimonial_text' => __(self::TESTIMONIAL),
            'status' => __(self::STATUS),
            'display_order' => __(self::DISPLAY_ORDER),
            'testimonial_created_at' => __(self::CREATED_AT),
            'action' => __(self::ACTION)
        ];
    }

    public function mapDBColumns(): array
    {
        return [
            'testimonial_created_at' => 'testimonials.created_at',
            'author_name' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(testimonials.author_name, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'author_type' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(testimonials.author_type, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'testimonial_text' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(testimonials.testimonial, '$.en')) COLLATE utf8mb4_unicode_ci")
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'author_name',
            'author_type',
            'status',
            'display_order',
            'testimonial_created_at'
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'author_name' => 'string',
            'author_type' => 'string',
            'testimonial_text' => 'string',
        ];
    }

    public function gloablSearchableColumns(): array
    {
        return [
            'author_name' => 'string',
            'author_type' => 'string',
            'testimonial_text' => 'string',
        ];
    }

    // public function downloadableColumns(): array
    // {
    //     return [
    //         'author_name' => __(self::AUTHOR_NAME),
    //         'author_type' => __(self::AUTHOR_TYPE),
    //         'testimonial_text' => __(self::TESTIMONIAL),
    //         'status' => __(self::STATUS),
    //         'display_order' => __(self::DISPLAY_ORDER),
    //         'testimonial_created_at' => __(self::CREATED_AT)
    //     ];
    // }

    public function getBulkAction(): array
    {
        return [
            'activate' => [
                'class' => 'success',
                'title' => __('testimonial::testimonial.activate'),
                'module' => 'Testimonial',
                'type' => 'activate'
            ],
            'deactivate' => [
                'class' => 'warning',
                'title' => __('testimonial::testimonial.deactivate'),
                'module' => 'Testimonial',
                'type' => 'deactivate'
            ],
            'delete' => [
                'class' => 'danger',
                'title' => __('testimonial::testimonial.delete'),
                'module' => 'Testimonial',
                'type' => 'delete'
            ],
        ];
    }

    public function getColumnAuthorName($data): string
    {
        $testimonial = Testimonial::find($data['id']);

        $url = $testimonial->getProfileImageUrl('thumbnail');

        $name = $data['author_name'] ?? '';
        $name = trim($name, '"');

        return '<div class="d-flex align-items-center">
                    <img class="me-2 rounded-circle" src="' . $url . '" alt="' . $name . '" width="40" height="40">
                    <a href="javascript:;" class="text-black">' . $name . '</a>
                </div>';
    }

    public function getColumnAuthorType($data): string
    {
        return $data['author_type'] ? trim($data['author_type'], '"') : '-';
    }

    public function getColumnTestimonialText($data): string
    {
        $text = $data['testimonial_text'] ?? '';
        $text = trim($text, '"');
        return strlen($text) > 50 ? substr($text, 0, 50) . '...' : $text;
    }

    public function getColumnStatus(array $data): string
    {
        $isActive = (bool) $data['status'];
        return '<input type="checkbox" class="my-switch change-status" data-id = "' . $data['id'] . '"
        id="switchCheckbox' . $data['id'] . '" ' . ($isActive ? 'checked' : '') . '
        data-bs-toggle="tooltip" title="' . ($isActive ? 'Active' : 'Inactive') . '">';
    }

    public function getColumnDisplayOrder($data): string
    {
        return '<span class="badge bg-primary">' . $data['display_order'] . '</span>';
    }

    public function getColumnTestimonialCreatedAt($data): string
    {
        return adminDateTimeFormatShow($data['testimonial_created_at']);
    }

    public function getColumnAction(array $data): string
    {
        $return = '';
        $user = Auth::user();

        if ($user->hasPermission('update-testimonial')) {
            $return .= '<a class="me-3" href="' . route('admin.testimonials.edit', encrypt($data['id'])) . '">
            <span class="inic inic-edit fs-18" data-bs-toggle="tooltip" title="Edit"></span></a>';
        }

        if ($user->hasPermission('read-testimonial')) {
            $return .= '<a class="me-3" href="' . route('admin.testimonials.show', encrypt($data['id'])) . '">
            <span class="inic inic-eye fs-18" data-bs-toggle="tooltip" title="View"></span></a>';
        }

        if ($user->hasPermission('delete-testimonial')) {
            $return .= '<a class="delete-grid-row" href="javascript:void(0)" data-grid="inic_grid_' . $this->uniqueID . '"
            data-url="' . route('admin.testimonials.destroy', encrypt($data['id'])) . '">
            <span class="inic inic-bin fs-18" data-bs-toggle="tooltip" title="Delete"></span></a>';
        }

        return $return;
    }

    public function getColumnCheckbox(array $data): string
    {
        return '<input type="checkbox" value="' . $data['id'] . '" class="check_approval" />';
    }
}
