<?php

namespace Modules\CalendarEvent\app\Http\Request;

use Illuminate\Foundation\Http\FormRequest;

class EventRequest extends FormRequest
{
    protected const SLUG_UNIQUE_VALIDATION = 'unique:events,slug';
    protected const STRING = 'string';

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $maxSize = 'max:255';
        $rules = [
            // Translatable fields
            'title' => ['required', 'array'],
            'title.en' => ['required', self::STRING, $maxSize],
            'title.ar' => ['nullable', self::STRING, $maxSize],

            'description' => ['required', 'array'],
            'description.en' => ['required', self::STRING],
            'description.ar' => ['nullable', self::STRING],

            'location' => ['nullable', 'array'],
            'location.en' => ['nullable', self::STRING, $maxSize],
            'location.ar' => ['nullable', self::STRING, $maxSize],

            'venue' => ['nullable', 'array'],
            'venue.en' => ['nullable', self::STRING, $maxSize],
            'venue.ar' => ['nullable', self::STRING, $maxSize],

            'registration_instructions' => ['nullable', 'array'],
            'registration_instructions.en' => ['nullable', self::STRING],
            'registration_instructions.ar' => ['nullable', self::STRING],

            // Regular fields
            'start_date' => ['required', 'date', 'after:now'],
            'end_date' => ['nullable', 'date', 'after:start_date'],
            'type' => ['required', 'in:all,online,in-person'],
            'status' => ['required', 'in:draft,published,cancelled,completed'],
            // 'is_registration_required' => ['boolean'],
            'registration_end_date' => ['nullable', 'date', 'after:now', 'before:start_date'],
            'meeting_link' => ['nullable', 'url', 'required_if:type,online'],
        ];

        $routeName = explode('.', request()->route()->getName());
        $route = array_pop($routeName);
        $slugRules = ['required', 'regex:/^[^\s]+(\s*[^\s]+)*$/'];

        switch ($route) {
            case 'create':
            case 'store':
                $rules = array_merge($rules, [
                    'slug' => array_merge($slugRules, [self::SLUG_UNIQUE_VALIDATION]),
                ]);
                break;
            case 'edit':
            case 'update':
                $event = request()->route()->parameter('calendar_event');
                $rules = array_merge($rules, [
                    'slug' => array_merge($slugRules, [self::SLUG_UNIQUE_VALIDATION . ',' . $event->id . ',id']),
                ]);
                break;
            default:
                return $rules;
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            // Title validation messages
            'title.array' => __('calendarevent::event.title_array'),
            'title.en.required' => __('calendarevent::event.title_en_required'),
            'title.en.string' => __('calendarevent::event.title_en_string'),
            'title.en.max' => __('calendarevent::event.title_en_max'),
            'title.ar.string' => __('calendarevent::event.title_ar_string'),
            'title.ar.max' => __('calendarevent::event.title_ar_max'),

            // Description validation messages
            'description.array' => __('calendarevent::event.description_array'),
            'description.en.required' => __('calendarevent::event.description_en_required'),
            'description.ar.required' => __('calendarevent::event.description_ar_required'),
            'description.en.string' => __('calendarevent::event.description_en_string'),
            'description.ar.string' => __('calendarevent::event.description_ar_string'),

            // Location validation messages
            'location.array' => __('calendarevent::event.location_array'),
            'location.en.string' => __('calendarevent::event.location_en_string'),
            'location.en.max' => __('calendarevent::event.location_en_max'),
            'location.ar.string' => __('calendarevent::event.location_ar_string'),
            'location.ar.max' => __('calendarevent::event.location_ar_max'),

            // Venue validation messages
            'venue.array' => __('calendarevent::event.venue_array'),
            'venue.en.string' => __('calendarevent::event.venue_en_string'),
            'venue.en.max' => __('calendarevent::event.venue_en_max'),
            'venue.ar.string' => __('calendarevent::event.venue_ar_string'),
            'venue.ar.max' => __('calendarevent::event.venue_ar_max'),

            // Registration instructions validation messages
            'registration_instructions.array' => __('calendarevent::event.registration_instructions_array'),
            'registration_instructions.en.string' => __('calendarevent::event.registration_instructions_en_string'),
            'registration_instructions.ar.string' => __('calendarevent::event.registration_instructions_ar_string'),

            // Date validation messages
            'start_date.required' => __('calendarevent::event.start_date_required'),
            'start_date.date' => __('calendarevent::event.start_date_date'),
            'start_date.after' => __('calendarevent::event.start_date_after_now'),
            'end_date.date' => __('calendarevent::event.end_date_date'),
            'end_date.after' => __('calendarevent::event.end_date_after_start'),
            'registration_end_date.date' => __('calendarevent::event.registration_end_date_date'),
            'registration_end_date.after' => __('calendarevent::event.registration_end_date_after_now'),
            'registration_end_date.before' => __('calendarevent::event.registration_end_before_start'),

            // Other validation messages
            'type.required' => __('calendarevent::event.type_required'),
            'type.in' => __('calendarevent::event.type_invalid'),
            'status.required' => __('calendarevent::event.status_required'),
            'status.in' => __('calendarevent::event.status_invalid'),
            'meeting_link.required_if' => __('calendarevent::event.meeting_link_required_for_online'),
            'meeting_link.url' => __('calendarevent::event.meeting_link_url'),
            'slug.required' => __('calendarevent::event.slug_required'),
            'slug.unique' => __('calendarevent::event.slug_unique'),
            'slug.regex' => __('calendarevent::event.slug_regex'),
        ];
    }
}
