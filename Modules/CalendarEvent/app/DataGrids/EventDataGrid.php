<?php

namespace Modules\CalendarEvent\app\DataGrids;

use Modules\CalendarEvent\app\Models\Event;
use Indianic\LaravelDataGrid\LaravelDataGrid;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class EventDataGrid extends LaravelDataGrid
{
    public const TITLE = "calendarevent::event.title";
    public const TYPE = "calendarevent::event.type";
    public const STATUS = "calendarevent::event.status";
    public const START_DATE = "calendarevent::event.start_date";
    public const END_DATE = "calendarevent::event.end_date";
    public const LOCATION = "calendarevent::event.location";
    public const CREATED_AT = "calendarevent::event.created_at";
    public const ACTION = "calendarevent::event.action";

    public $guard = 'admin';

    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'events';

    /**
     * Define how many rows you want to display on a page
     * @var int $recordsPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define row per page dropdown options
     * @var array $recordsPerPageOptions
     */
    public $recordsPerPageOptions;

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy = 'id';

    /**
     * Define default sorting direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection = 'DESC';

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'events';

    /**
     * Define primary key column name
     * @var string $primaryKey
     */
    public $primaryKey = 'id';

    public function resource()
    {
        return Event::select(
            'events.id',
            'events.slug',
            'events.type',
            'events.status',
            'events.start_date',
            'events.end_date',
            'events.is_registration_required',
            'events.created_at as event_created_at',
            DB::raw("JSON_EXTRACT(events.title, '$.en') as title"),
            DB::raw("JSON_EXTRACT(events.location, '$.en') as location")
        )->with('media');
    }

    public function columns(): array
    {
        return [
            // 'checkbox' => '<input class="select_all checkbox" type="checkbox" />',
            'title' => __(self::TITLE),
            'type' => __(self::TYPE),
            'location' => __(self::LOCATION),
            'start_date' => __(self::START_DATE),
            'end_date' => __(self::END_DATE),
            'status' => __(self::STATUS),
            'event_created_at' => __(self::CREATED_AT),
            'action' => __(self::ACTION)
        ];
    }

    public function mapDBColumns(): array
    {
        return [
            'title' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(events.title, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'type' => 'events.type',
            'location' => DB::raw("JSON_UNQUOTE(JSON_EXTRACT(events.location, '$.en')) COLLATE utf8mb4_unicode_ci"),
            'status' => 'events.status',
            'start_date' => 'events.start_date',
            'end_date' => 'events.end_date',
            'event_created_at' => 'events.created_at'
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'title',
            'type',
            'location',
            'start_date',
            'end_date',
            'status',
            'event_created_at'
        ];
    }

    // public function downloadableColumns(): array
    // {
    //     return [
    //         'title' => __(self::TITLE),
    //         'type' => __(self::TYPE),
    //         'location' => __(self::LOCATION),
    //         'start_date' => __(self::START_DATE),
    //         'end_date' => __(self::END_DATE),
    //         'status' => __(self::STATUS),
    //         'event_created_at' => __(self::CREATED_AT)
    //     ];
    // }

    public function gloablSearchableColumns(): array
    {
        return [
            'title' => 'string',
            'type' => 'string',
            'location' => 'string',
            'status' => 'string',
            'start_date' => 'string',
            'end_date' => 'string',
            'event_created_at' => 'string'
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'title' => 'string',
            'type' => 'string',
            'location' => 'string',
            'status' => 'string',
            'start_date' => 'string',
            'end_date' => 'string',
            'event_created_at' => 'string'
        ];
    }

    // public function getBulkAction(): array
    // {
    //     return [
    //         'Publish' => [
    //             'class' => 'success',
    //             'title' => __('calendarevent::event.publish'),
    //             'module' => 'CalendarEvent',
    //             'type' => 'publish'
    //         ],
    //         'Unpublish' => [
    //             'class' => 'warning',
    //             'title' => __('calendarevent::event.unpublish'),
    //             'module' => 'CalendarEvent',
    //             'type' => 'unpublish'
    //         ],
    //         'Cancel' => [
    //             'class' => 'secondary',
    //             'title' => __('calendarevent::event.cancel'),
    //             'module' => 'CalendarEvent',
    //             'type' => 'cancel'
    //         ],
    //         'Complete' => [
    //             'class' => 'info',
    //             'title' => __('calendarevent::event.complete'),
    //             'module' => 'CalendarEvent',
    //             'type' => 'complete'
    //         ],
    //         'Delete' => [
    //             'class' => 'danger',
    //             'title' => __('calendarevent::event.delete'),
    //             'module' => 'CalendarEvent',
    //             'type' => 'delete'
    //         ],
    //     ];
    // }

    public function getColumnTitle(array $data): string
    {
        $event = Event::find($data['id']);

        $url = $event->getFeaturedImageUrl('thumbnail');

        $title = $data['title'] ?? '';
        $title = trim($title, '"');

        return '<div class="d-flex align-items-center">
                    <img class="me-2 rounded" src="' . $url . '" alt="' . $title . '" width="40" height="40" style="object-fit: cover;">
                    <div>
                        <a href="javascript:;" class="text-black fw-bold">' . $title . '</a>
                        <br><small class="text-muted">' . $data['slug'] . '</small>
                    </div>
                </div>';
    }

    public function getColumnType(array $data): string
    {
        $typeLabels = [
            'all' => __('calendarevent::event.type_all'),
            'online' => __('calendarevent::event.type_online'),
            'in-person' => __('calendarevent::event.type_in_person')
        ];

        return $typeLabels[$data['type']] ?? $data['type'];
    }

    public function getColumnLocation(array $data): string
    {
        $location = $data['location'] ?? '';
        $location = trim($location, '"');

        return $location ?: '-';
    }

    public function getColumnStartDate(array $data): string
    {
        return adminDateTimeFormatShow($data['start_date']);
    }

    public function getColumnEndDate(array $data): string
    {
        return $data['end_date'] ? adminDateTimeFormatShow($data['end_date']) : '-';
    }

    public function getColumnStatus(array $data): string
    {
        $statusClasses = [
            'draft' => 'badge bg-secondary',
            'published' => 'badge bg-success',
            'cancelled' => 'badge bg-danger',
            'completed' => 'badge bg-info'
        ];

        $statusLabels = [
            'draft' => __('calendarevent::event.status_draft'),
            'published' => __('calendarevent::event.status_published'),
            'cancelled' => __('calendarevent::event.status_cancelled'),
            'completed' => __('calendarevent::event.status_completed')
        ];

        $class = $statusClasses[$data['status']] ?? 'badge bg-secondary';
        $label = $statusLabels[$data['status']] ?? $data['status'];

        return '<span class="badge '.$class.'">'.$label.'</span>';
    }

    public function getColumnEventCreatedAt(array $data): string
    {
        return adminDateTimeFormatShow($data['event_created_at']);
    }

    public function getColumnAction(array $data): string
    {
        $return = '';
        $user = Auth::user();

        if ($user->hasPermission('update-calendar-event')) {
            $return .= '<a class="me-3" href="' . route('admin.calendar-events.edit', encrypt($data['id'])) . '">
            <span class="inic inic-edit fs-18" data-bs-toggle="tooltip" title="Edit"></span></a>';
        }

        if ($user->hasPermission('read-calendar-event')) {
            $return .= '<a class="me-3" href="' . route('admin.calendar-events.show', encrypt($data['id'])) . '">
            <span class="inic inic-eye fs-18" data-bs-toggle="tooltip" title="View"></span></a>';
        }

        if ($user->hasPermission('delete-calendar-event')) {
            $return .= '<a class="delete-grid-row" href="javascript:void(0)" data-grid="inic_grid_' . $this->uniqueID . '"
            data-url="' . route('admin.calendar-events.destroy', encrypt($data['id'])) . '">
            <span class="inic inic-bin fs-18" data-bs-toggle="tooltip" title="Delete"></span></a>';
        }

        return $return;
    }

    // public function getColumnCheckbox(array $data): string
    // {
    //     return '<input type="checkbox" value="' . $data['id'] . '" class="check_approval" />';
    // }

    public function getDownloadColumnTitle(array $data): string
    {
        $title = $data['title'] ?? '';
        return trim($title, '"');
    }

    public function getDownloadColumnLocation(array $data): string
    {
        $location = $data['location'] ?? '';
        return trim($location, '"');
    }
}
