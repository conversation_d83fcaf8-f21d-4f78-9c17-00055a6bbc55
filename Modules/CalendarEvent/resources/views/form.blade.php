<form action="{{ $action }}" method="POST" enctype="multipart/form-data">
    @csrf
    @if (isset($event))
    @method('PUT')
    @endif

    @include('calendarevent::partials.language-switcher')

    <!-- Translatable Fields Card -->
    <div class="card border-primary">
        <div class="card-header bg-primary bg-opacity-10">
            <h5 class="card-title mb-0">
                <i class="fa fa-language me-2"></i>{{ __('admin.common.translatable_fields') }}
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- Title -->
                <div class="col-md-6 mb-3">
                    <label for="title_en" class="form-label">{{ __('calendarevent::event.title') }} <span class="text-danger">*</span></label>
                    <div class="form-group translatable-field" data-language="en">
                        <input type="text" id="title_en" name="title[en]" value="{{ old('title.en', isset($event) ? $event->getTranslation('title', 'en', false) : '') }}" class="form-control @error('title.en') is-invalid @enderror" placeholder="{{ __('calendarevent::event.title_placeholder') }}">
                        @error('title.en')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group translatable-field" data-language="ar" style="display: none;">
                        <input type="text" id="title_ar" name="title[ar]" dir="rtl" value="{{ old('title.ar', isset($event) ? $event->getTranslation('title', 'ar', false) : '') }}" class="form-control @error('title.ar') is-invalid @enderror" placeholder="{{ __('calendarevent::event.title_placeholder') }}">
                        @error('title.ar')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Location -->
                <div class="col-md-6 mb-3">
                    <label for="location_en" class="form-label">{{ __('calendarevent::event.location') }}</label>
                    <div class="form-group translatable-field" data-language="en">
                        <input type="text" id="location_en" name="location[en]" value="{{ old('location.en', isset($event) ? $event->getTranslation('location', 'en', false) : '') }}" class="form-control @error('location.en') is-invalid @enderror" placeholder="{{ __('calendarevent::event.location_placeholder') }}">
                        @error('location.en')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group translatable-field" data-language="ar" style="display: none;">
                        <input type="text" id="location_ar" name="location[ar]" dir="rtl" value="{{ old('location.ar', isset($event) ? $event->getTranslation('location', 'ar', false) : '') }}" class="form-control @error('location.ar') is-invalid @enderror" placeholder="{{ __('calendarevent::event.location_placeholder') }}">
                        @error('location.ar')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Venue -->
                <div class="col-md-6 mb-3">
                    <label for="venue_en" class="form-label">{{ __('calendarevent::event.venue') }}</label>
                    <div class="form-group translatable-field" data-language="en">
                        <input type="text" id="venue_en" name="venue[en]" value="{{ old('venue.en', isset($event) ? $event->getTranslation('venue', 'en', false) : '') }}" class="form-control @error('venue.en') is-invalid @enderror" placeholder="{{ __('calendarevent::event.venue_placeholder') }}">
                        @error('venue.en')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group translatable-field" data-language="ar" style="display: none;">
                        <input type="text" id="venue_ar" name="venue[ar]" dir="rtl" value="{{ old('venue.ar', isset($event) ? $event->getTranslation('venue', 'ar', false) : '') }}" class="form-control @error('venue.ar') is-invalid @enderror" placeholder="{{ __('calendarevent::event.venue_placeholder') }}">
                        @error('venue.ar')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Description -->
                <div class="col-md-12 mb-3">
                    <label for="description_en" class="form-label">{{ __('calendarevent::event.description') }}</label>
                    <div class="form-group translatable-field" data-language="en">
                        <textarea id="description_en" name="description[en]" class="form-control ck-editor @error('description.en') is-invalid @enderror" placeholder="{{ __('calendarevent::event.description_placeholder') }}">{{ old('description.en', isset($event) ? $event->getTranslation('description', 'en', false) : '') }}</textarea>
                        @error('description.en')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group translatable-field" data-language="ar" style="display: none;">
                        <textarea id="description_ar" name="description[ar]" dir="rtl" class="form-control ck-editor @error('description.ar') is-invalid @enderror" placeholder="{{ __('calendarevent::event.description_placeholder') }}">{{ old('description.ar', isset($event) ? $event->getTranslation('description', 'ar', false) : '') }}</textarea>
                        @error('description.ar')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Registration Instructions -->
                <div class="col-md-12 mb-3" id="registration_instructions_field" style="display: none;">
                    <label for="registration_instructions_en" class="form-label">{{ __('calendarevent::event.registration_instructions') }}</label>
                    <div class="form-group translatable-field" data-language="en">
                        <textarea id="registration_instructions_en" name="registration_instructions[en]" class="form-control ck-editor @error('registration_instructions.en') is-invalid @enderror" placeholder="{{ __('calendarevent::event.registration_instructions_placeholder') }}">{{ old('registration_instructions.en', isset($event) ? $event->getTranslation('registration_instructions', 'en', false) : '') }}</textarea>
                        @error('registration_instructions.en')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group translatable-field" data-language="ar" style="display: none;">
                        <textarea id="registration_instructions_ar" name="registration_instructions[ar]" dir="rtl" class="form-control ck-editor @error('registration_instructions.ar') is-invalid @enderror" placeholder="{{ __('calendarevent::event.registration_instructions_placeholder') }}">{{ old('registration_instructions.ar', isset($event) ? $event->getTranslation('registration_instructions', 'ar', false) : '') }}</textarea>
                        @error('registration_instructions.ar')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- General Settings Card -->
    <div class="card mt-3">
        <div class="card-header bg-light">
            <h5 class="card-title mb-0">
                <i class="fa fa-cog me-2"></i>{{ __('admin.common.general_settings') }}
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- Slug -->
                <div class="col-md-6 mb-3">
                    <label for="slug" class="form-label">{{ __('calendarevent::event.slug') }} <span class="text-danger">*</span></label>
                    <input type="text" id="slug" name="slug" value="{{ old('slug', $event->slug ?? '') }}" class="form-control @error('slug') is-invalid @enderror" placeholder="{{ __('calendarevent::event.slug_placeholder') }}">
                    @error('slug')
                    <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Type -->
                <div class="col-md-6 mb-3">
                    <label for="type" class="form-label">{{ __('calendarevent::event.type') }} <span class="text-danger">*</span></label>
                    <select id="type" name="type" class="form-select @error('type') is-invalid @enderror">
                        <option value="">Select Type</option>
                        <option value="all" {{ old('type', $event->type ?? '') == 'all' ? 'selected' : '' }}>{{ __('calendarevent::event.type_all') }}</option>
                        <option value="online" {{ old('type', $event->type ?? '') == 'online' ? 'selected' : '' }}>{{ __('calendarevent::event.type_online') }}</option>
                        <option value="in-person" {{ old('type', $event->type ?? '') == 'in-person' ? 'selected' : '' }}>{{ __('calendarevent::event.type_in_person') }}</option>
                    </select>
                    @error('type')
                    <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Start Date -->
                <div class="col-md-6 mb-3">
                    <label for="start_date" class="form-label">{{ __('calendarevent::event.start_date') }} <span class="text-danger">*</span></label>
                    <input type="datetime-local" id="start_date" name="start_date" value="{{ old('start_date', isset($event) && $event->start_date ? $event->start_date->format('Y-m-d\TH:i') : '') }}" min="{{ now()->format('Y-m-d\TH:i') }}" class="form-control @error('start_date') is-invalid @enderror">
                    @error('start_date')
                    <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <!-- End Date -->
                <div class="col-md-6 mb-3">
                    <label for="end_date" class="form-label">{{ __('calendarevent::event.end_date') }}</label>
                    <input type="datetime-local" id="end_date" name="end_date" value="{{ old('end_date', isset($event) && $event->end_date ? $event->end_date->format('Y-m-d\TH:i') : '') }}" class="form-control @error('end_date') is-invalid @enderror">
                    @error('end_date')
                    <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Status -->
                <div class="col-md-6 mb-3">
                    <label for="status" class="form-label">{{ __('calendarevent::event.status') }} <span class="text-danger">*</span></label>
                    <select id="status" name="status" class="form-select @error('status') is-invalid @enderror">
                        <option value="">Select Status</option>
                        <option value="draft" {{ old('status', $event->status ?? '') == 'draft' ? 'selected' : '' }}>{{ __('calendarevent::event.status_draft') }}</option>
                        <option value="published" {{ old('status', $event->status ?? '') == 'published' ? 'selected' : '' }}>{{ __('calendarevent::event.status_published') }}</option>
                        <option value="cancelled" {{ old('status', $event->status ?? '') == 'cancelled' ? 'selected' : '' }}>{{ __('calendarevent::event.status_cancelled') }}</option>
                        <option value="completed" {{ old('status', $event->status ?? '') == 'completed' ? 'selected' : '' }}>{{ __('calendarevent::event.status_completed') }}</option>
                    </select>
                    @error('status')
                    <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Meeting Link -->
                <div class="col-md-6 mb-3" id="meeting_link_field" style="display: none;">
                    <label for="meeting_link" class="form-label">{{ __('calendarevent::event.meeting_link') }} <span class="text-danger">*</span></label>
                    <input type="url" id="meeting_link" name="meeting_link" value="{{ old('meeting_link', $event->meeting_link ?? '') }}" class="form-control @error('meeting_link') is-invalid @enderror" placeholder="{{ __('calendarevent::event.meeting_link_placeholder') }}">
                    @error('meeting_link')
                    <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Registration Required -->
                <div class="col-md-6 mb-3">
                    <label for="is_registration_required" class="form-label">{{ __('calendarevent::event.is_registration_required') }}</label> <br />
                    <input type="checkbox" id="is_registration_required" name="is_registration_required" class="my-switch" {{ old('is_registration_required', $event->is_registration_required ?? false) ? 'checked' : '' }}>
                </div>

                <!-- Registration End Date -->
                <div class="col-md-6 mb-3" id="registration_end_date_field" style="display: none;">
                    <label for="registration_end_date" class="form-label">{{ __('calendarevent::event.registration_end_date') }}</label>
                    <input type="datetime-local" id="registration_end_date" name="registration_end_date" value="{{ old('registration_end_date', isset($event) && $event->registration_end_date ? $event->registration_end_date->format('Y-m-d\TH:i') : '') }}" min="{{ now()->format('Y-m-d\TH:i') }}" class="form-control @error('registration_end_date') is-invalid @enderror">
                    @error('registration_end_date')
                    <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>
        </div>
    </div>

    <!-- Featured Image Card -->
    <div class="card mt-3">
        <div class="card-header bg-light">
            <h5 class="card-title mb-0">
                <i class="fa fa-image me-2"></i>{{ __('calendarevent::event.featured_image') }}
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-12">
                    <!-- Image Preview Container -->
                    <div class="image-upload-container mb-3">
                        <div class="image-preview-wrapper">
                            {{-- For optimal performance, you can use WebP with fallback:
                            <picture>
                                <source srcset="{{ isset($event) ? $event->getFeaturedImageWebpUrl('preview') : '' }}" type="image/webp">
                                <img src="{{ isset($event) ? $event->getFeaturedImageUrl('preview') : asset('images/banner-img.png') }}"
                                     class="imagePreview img-fluid" id="preview_cropped_image" alt="Event Image">
                            </picture>
                            --}}

                            <img src="{{ isset($event) ? $event->getFeaturedImageUrl('preview') : asset('images/banner-img.png') }}"
                                 class="imagePreview img-fluid rounded" id="preview_cropped_image" alt="Event Image">
                        </div>

                        <!-- Upload Controls -->
                        <div class="upload-controls mt-3">
                            <div class="d-flex align-items-center justify-content-center gap-2">
                                <input type="hidden" name="cropped_image" id="cropped_image">
                                <input type="file" name="featured_image" id="featured_image" class="image-cropper d-none"
                                       accept="image/png,image/jpg,image/jpeg,image/webp"
                                       data-preview="cropped_image" data-width="856" data-height="500">
                                <button type="button" class="btn btn-primary btn-sm" onclick="document.getElementById('featured_image').click()">
                                    <i class="fa fa-upload me-1"></i> {{ __('admin.common.upload') }}
                                </button>
                            </div>
                            <div class="mt-2 text-center">
                                <small class="text-muted">
                                    <i class="fa fa-info-circle"></i>
                                    {{ __('calendarevent::event.image_upload_info') }}
                                </small>
                            </div>
                        </div>
                    </div>

                    @error('featured_image')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                    @enderror
                    @if(isset($event) && $event->hasMedia('featured_image'))
                        <div class="alert alert-info alert-sm">
                            <i class="fa fa-info-circle"></i>
                            {{ __('admin.common.current_image_will_be_replaced') }}
                        </div>
                    @endif
                </div>
            </div>

        </div>
    </div>

    <!-- Form Actions -->
    <div class="d-flex align-items-center justify-content-end gap-3 mt-3">
        <button type="submit" class="btn btn-primary">{{ __('admin.common.submit') }}</button>
        <a href="{{ route('admin.calendar-events.index') }}" class="btn btn-secondary">{{ __('admin.common.cancel') }}</a>
    </div>
</form>

@include('plugins.ckeditor')
@include('plugins.select2')
@include('plugins.cropper')

{{-- Include shared image upload styles and scripts --}}
@push('css')
    <link rel="stylesheet" href="{{ asset('css/admin-image-upload.css') }}">
@endpush

@push('scripts')
    <script src="{{ asset('js/admin-image-upload.js') }}" defer></script>
@endpush

@push('scripts')
@php
$validator = JsValidator::formRequest('Modules\CalendarEvent\app\Http\Request\EventRequest');
$validator->ignore('');
@endphp
{!! $validator !!}
<script type="module">
    document.addEventListener('DOMContentLoaded', () => {
        // Initialize CKEditors for both languages
        initializeCKEditor('description_en', options);
        initializeCKEditor('registration_instructions_en', options);

        // Set up Arabic CKEditor with RTL direction when needed
        const arOptions = { ...options, contentsLangDirection: 'rtl' };
        initializeCKEditor('description_ar', arOptions);
        initializeCKEditor('registration_instructions_ar', arOptions);

        // Hide the Arabic editors initially
        if (typeof CKEDITOR !== 'undefined') {
            if (CKEDITOR.instances.description_ar && CKEDITOR.instances.description_ar.container) {
                CKEDITOR.instances.description_ar.container.setStyle('display', 'none');
            }
            if (CKEDITOR.instances.registration_instructions_ar && CKEDITOR.instances.registration_instructions_ar.container) {
                CKEDITOR.instances.registration_instructions_ar.container.setStyle('display', 'none');
            }
        }

        // Handle event type change
        const typeSelect = document.getElementById('type');
        const meetingLinkField = document.getElementById('meeting_link_field');

        const toggleMeetingLinkField = () => {
            if (typeSelect.value === 'online') {
                meetingLinkField.style.display = 'block';
            } else {
                meetingLinkField.style.display = 'none';
            }
        };

        typeSelect.addEventListener('change', toggleMeetingLinkField);
        toggleMeetingLinkField(); // Initialize on page load

        // Handle registration required change
        const registrationCheckbox = document.getElementById('is_registration_required');
        const registrationEndDateField = document.getElementById('registration_end_date_field');
        const registrationInstructionsField = document.getElementById('registration_instructions_field');

        const toggleRegistrationFields = () => {
            if (registrationCheckbox.checked) {
                registrationEndDateField.style.display = 'block';
                registrationInstructionsField.style.display = 'block';
            } else {
                registrationEndDateField.style.display = 'none';
                registrationInstructionsField.style.display = 'none';
            }
        };

        registrationCheckbox.addEventListener('change', toggleRegistrationFields);
        toggleRegistrationFields(); // Initialize on page load

        // Auto-generate slug from title
        const titleEnInput = document.getElementById('title_en');
        const slugInput = document.getElementById('slug');

        titleEnInput.addEventListener('input', () => {
            if (!slugInput.value || slugInput.dataset.manual !== 'true') {
                const slug = titleEnInput.value
                    .toLowerCase()
                    .replace(/[^a-z0-9\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .replace(/-+/g, '-')
                    .trim('-');
                slugInput.value = slug;
            }
        });

        slugInput.addEventListener('input', () => {
            slugInput.dataset.manual = 'true';
        });
    });
</script>
@endpush
