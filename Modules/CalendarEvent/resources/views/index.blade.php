@extends('admin.layouts.page')
@section('title', __('calendarevent::event.events'))

@section('breadcrumb')
    <ol class="breadcrumb-item active">{{ __('calendarevent::event.events') }}</ol>
@endsection

@permission('create-calendar-event')
    @section('action_buttons')
    <a href="{{ route('admin.calendar-events.create') }}" class="btn btn-primary">
        <em class="inic inic-add"></em> {{ __('calendarevent::event.add_event') }}
    </a>
    @endsection
@endpermission

@section('content')
    {!! $dataGrid !!}
@endsection

@include('plugins.sweetalert2')
@push('scripts')

    <script type="module">
        let csrfToken = $('meta[name="csrf-token"]').attr('content');
        $(document).on('click', '.delete-grid-row', function (event) {
            event.preventDefault();
            let url = $(this).attr('data-url');
            SwalConfirm(url, function (url) {
                $.ajax({
                    url: url,
                    type: 'delete',
                    datatype: 'json',
                    headers: {
                        'X-CSRF-TOKEN': csrfToken
                    },
                    success: function (response) {
                        toastr.success(response.message);
                        window.inic_grid_calendar_events.refresh();
                    },
                    error: function (response) {
                        toastr.error(response.message);
                    }
                });
            })
        })

        $(document).on('click', '.grid-bulk-action', function(event) {
            event.preventDefault();
            var id = [];var error = 0;
            $('.check_approval').each(function(index){
                if($(this).prop('checked') == true){
                    id.push($(this).val());
                    error = 1;
                }
            });

            var url= "{{ route('admin.calendar-events.bulkAction') }}";
            var status=$(this).data('status');
            var type=$(this).data('action-type');

            if(error == 0){
                toastr.error("{{ __('calendarevent::event.please_select_one_event') }}");
            }else if(error == 1){
                SwalConfirm(url, function(url) {
                    $.ajax({
                        url: url,
                        type: 'post',
                        data: {id: id,status:status,type:type},
                        success: function(response) {
                            if(response.status == 'success'){
                                toastr.success(response.message);
                            }else{
                                toastr.error(response.message);
                            }
                            window.inic_grid_calendar_events.refresh();
                        },
                        error: function(response) {
                            toastr.error(response.message);
                        }

                    });
                });
            }
        });

    </script>
@endpush
