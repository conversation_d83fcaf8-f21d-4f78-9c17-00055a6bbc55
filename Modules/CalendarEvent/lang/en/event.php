<?php

return [
    // General
    'events' => 'Calendar Events',
    'event' => 'Event',
    'add_event' => 'Add Event',
    'edit_event' => 'Edit Event',
    'view_event' => 'View Event',
    'event_details' => 'Event Details',

    // Form Fields
    'title' => 'Title',
    'description' => 'Description',
    'start_date' => 'Start Date',
    'end_date' => 'End Date',
    'location' => 'Location',
    'venue' => 'Venue',
    'type' => 'Type',
    'featured_image' => 'Featured Image',
    'is_registration_required' => 'Registration Required',
    'registration_end_date' => 'Registration End Date',
    'registration_instructions' => 'Registration Instructions',
    'meeting_link' => 'Meeting Link',
    'status' => 'Status',
    'slug' => 'Slug',
    'action' => 'Action',
    'created_at' => 'Created On',
    'updated_at' => 'Updated On',

    // Event Types
    'type_all' => 'All',
    'type_online' => 'Online',
    'type_in_person' => 'In Person',

    // Event Status
    'status_draft' => 'Draft',
    'status_published' => 'Published',
    'status_cancelled' => 'Cancelled',
    'status_completed' => 'Completed',

    // Actions
    'publish' => 'Publish',
    'unpublish' => 'Unpublish',
    'cancel' => 'Cancel',
    'complete' => 'Complete',
    'delete' => 'Delete',

    // Common
    'yes' => 'Yes',
    'no' => 'No',

    // Placeholders
    'title_placeholder' => 'Enter event title',
    'description_placeholder' => 'Enter event description',
    'location_placeholder' => 'Enter event location',
    'venue_placeholder' => 'Enter event venue',
    'slug_placeholder' => 'Enter event slug',
    'meeting_link_placeholder' => 'Enter meeting link for online events',
    'registration_instructions_placeholder' => 'Enter registration instructions',

    // Success Messages
    'event_created_successfully' => 'Event created successfully.',
    'event_updated_successfully' => 'Event updated successfully.',
    'event_deleted_successfully' => 'Event deleted successfully.',
    'events_deleted_successfully' => 'Events deleted successfully.',
    'status_updated_successfully' => 'Event status updated successfully.',
    'events_cancelled_successfully' => 'Events cancelled successfully.',
    'events_completed_successfully' => 'Events completed successfully.',

    // Error Messages
    'error_creating_event' => 'Error creating event. Please try again.',
    'error_updating_event' => 'Error updating event. Please try again.',
    'error_deleting_event' => 'Error deleting event. Please try again.',
    'error_loading_events' => 'Error loading events.',
    'error_loading_event' => 'Error loading event.',
    'error_bulk_action' => 'Error performing bulk action.',
    'something_wrong' => 'Something went wrong.',

    // Validation Messages - Required
    'title_array' => 'The title field must be an array.',
    'title_en_required' => 'The title (English) field is required.',
    'title_ar_required' => 'The title (Arabic) field is required.',
    'start_date_required' => 'The start date field is required.',
    'type_required' => 'The event type field is required.',
    'status_required' => 'The status field is required.',
    'slug_required' => 'The slug field is required.',

    // Validation Messages - Format
    'title_en_string' => 'The title (English) must be a string.',
    'title_ar_string' => 'The title (Arabic) must be a string.',
    'title_en_max' => 'The title (English) may not be greater than 255 characters.',
    'title_ar_max' => 'The title (Arabic) may not be greater than 255 characters.',
    'description_array' => 'The description field must be an array.',
    'description_en_required' => 'The description (English) field is required.',
    'description_ar_required' => 'The description (Arabic) field is required.',
    'description_en_string' => 'The description (English) must be a string.',
    'description_ar_string' => 'The description (Arabic) must be a string.',
    'location_array' => 'The location field must be an array.',
    'location_en_string' => 'The location (English) must be a string.',
    'location_ar_string' => 'The location (Arabic) must be a string.',
    'location_en_max' => 'The location (English) may not be greater than 255 characters.',
    'location_ar_max' => 'The location (Arabic) may not be greater than 255 characters.',
    'venue_array' => 'The venue field must be an array.',
    'venue_en_string' => 'The venue (English) must be a string.',
    'venue_ar_string' => 'The venue (Arabic) must be a string.',
    'venue_en_max' => 'The venue (English) may not be greater than 255 characters.',
    'venue_ar_max' => 'The venue (Arabic) may not be greater than 255 characters.',
    'registration_instructions_array' => 'The registration instructions field must be an array.',
    'registration_instructions_en_string' => 'The registration instructions (English) must be a string.',
    'registration_instructions_ar_string' => 'The registration instructions (Arabic) must be a string.',

    // Validation Messages - Other
    'start_date_date' => 'The start date must be a valid date.',
    'start_date_after_now' => 'The start date must be in the future.',
    'end_date_date' => 'The end date must be a valid date.',
    'end_date_after_start' => 'The end date must be after the start date.',
    'type_invalid' => 'The selected event type is invalid.',
    'status_invalid' => 'The selected status is invalid.',
    'registration_end_date_date' => 'The registration end date must be a valid date.',
    'registration_end_date_after_now' => 'The registration end date must be in the future.',
    'registration_end_before_start' => 'The registration end date must be before the start date.',
    'meeting_link_required_for_online' => 'Meeting link is required for online events.',
    'meeting_link_url' => 'The meeting link must be a valid URL.',
    'meeting_link_invalid_url' => 'The meeting link must be a valid URL.',
    'slug_unique' => 'The slug has already been taken.',
    'slug_regex' => 'The slug format is invalid.',
    'featured_image_mimes' => 'The featured image must be a file of type: jpeg, jpg, png.',
    'featured_image_max' => 'The featured image may not be greater than 10MB.',
    'featured_image_invalid' => 'The featured image must be a file of type: jpeg, jpg, png.',
    'featured_image_too_large' => 'The featured image may not be greater than 10MB.',

    // Bulk Actions
    'invalid_bulk_action' => 'Invalid bulk action parameters.',
    'please_select_one_event' => 'Please select at least one event.',

    // Additional Messages
    'event_not_found' => 'Event not found.',
    'error_updating_status' => 'Error updating event status.',
    'join_meeting' => 'Join Meeting',
    'image_upload_info' => 'Recommended size: 856x500px or higher. Supported formats: JPG, PNG, WebP',
];
