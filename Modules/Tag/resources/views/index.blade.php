@extends('admin.layouts.page')

@section('title', __('tag::tag.tag'))

@section('breadcrumb')
    <ol class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">{{ __('admin.common.home') }}</a></ol>
    <ol class="breadcrumb-item active">{{ __('tag::tag.tag') }}</ol>
@endsection

@section('content')
    {!! $dataGridHtml !!}
@endsection

@push('scripts')
    <script type="module">
        $(document).on('click', '.delete-grid-row', function (event) {
            event.preventDefault();
            let url = $(this).attr('data-url');
            SwalConfirm(url, function (url) {
                $.ajax({
                    url: url,
                    type: 'delete',
                    datatype: 'json',
                    success: function (response) {
                        toastr.success(response.message);
                        window.inic_grid_tags.refresh();
                    },
                    error: function (response) {
                        toastr.error(response.message);
                    }
                });
            })
        })

        $(document).on('click', '#bulkDelete', function(event) {
            event.preventDefault();
            var id = [];var error = 0;
            $('.check_approval').each(function(index){
                if($(this).prop('checked') == true){
                    error = 1;
                    id.push($(this).val());
                }
            });
            var url= "{{ route('admin.tags.bulkDelete') }}"
            if(error == 0){
                toastr.error("{{ __('tag::tag.select_one_tag') }}");
            }else if(error == 1){
                SwalConfirm(url, function(url) {
                    $.ajax({
                        url: url,
                        type: 'post',
                        data: {id: id},
                        success: function(response) {
                            toastr.success(response.message);
                            window.inic_grid_tags.refresh();
                        },
                        error: function(response) {
                            toastr.error(response.message);
                        }
                    });
                })
            }
        });
    </script>
@endpush
