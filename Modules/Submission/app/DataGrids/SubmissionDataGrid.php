<?php

namespace Modules\Submission\DataGrids;

use Illuminate\Support\Facades\Auth;
use Modules\Submission\Models\Submission;
use Indianic\LaravelDataGrid\LaravelDataGrid;

class SubmissionDataGrid extends LaravelDataGrid
{
    public $guard = 'admin';

    /**
     * Define unique table id
     * @var mixed $uniqueID
     */
    public $uniqueID = 'submissions';

    /**
     * Define how many rows you want to display on a page
     * @var int $recordsPerPage
     */
    public $recordsPerPage = 10;

    /**
     * Define row per page dropdown options
     * @var array $recordsPerPageOptions
     */
    public $recordsPerPageOptions;

    /**
     * Define mysql column name which you want to default on sorting
     * @var string $sortBy
     */
    public $sortBy = 'id';

    /**
     * Define default sorting direction
     * Example: ASC | DESC
     * @var string $sortByDirection
     */
    public $sortByDirection = 'DESC';

    /**
     * Set download file prefix or set false
     * @var mixed
     */
    protected $downloadFilePrefix = 'submissions';

    // DataGrid constants
    public const AUTHOR_NAME = 'submission::submission.author_name';
    public const EMAIL = 'submission::submission.email';
    public const PHONE = 'submission::submission.phone';
    public const BOOK_TITLE = 'submission::submission.book_title';
    public const GENRE = 'submission::submission.genre';
    public const STATUS = 'submission::submission.status';
    public const CREATED_AT = 'submission::submission.created_at';
    public const ACTION = 'submission::submission.action';

    public function resource()
    {
        return Submission::select(
            'submissions.id',
            'submissions.author_name',
            'submissions.email',
            'submissions.phone',
            'submissions.book_title',
            'submissions.genre',
            'submissions.manuscript_file',
            'submissions.status',
            'submissions.created_at as submission_created_at'
        );
    }

    public function columns(): array
    {
        return [
            'checkbox' => '<input class="select_all checkbox" type="checkbox" />',
            'author_name' => __(self::AUTHOR_NAME),
            'email' => __(self::EMAIL),
            'book_title' => __(self::BOOK_TITLE),
            'genre' => __(self::GENRE),
            'status' => __(self::STATUS),
            'submission_created_at' => __(self::CREATED_AT),
            'action' => __(self::ACTION)
        ];
    }

    public function mapDBColumns(): array
    {
        return [
            'submission_created_at' => 'submissions.created_at',
            'author_name' => 'submissions.author_name',
            'email' => 'submissions.email',
            'book_title' => 'submissions.book_title',
            'genre' => 'submissions.genre',
            'status' => 'submissions.status'
        ];
    }

    public function sortableColumns(): array
    {
        return [
            'author_name',
            'email',
            'book_title',
            'genre',
            'status',
            'submission_created_at'
        ];
    }

    public function searchableColumns(): array
    {
        return [
            'author_name' => 'string',
            'email' => 'string',
            'book_title' => 'string',
            'genre' => 'string',
            'status' => 'string',
            'submission_created_at' => 'string'
        ];
    }

    public function gloablSearchableColumns(): array
    {
        return [
            'author_name' => 'string',
            'email' => 'string',
            'book_title' => 'string',
            'genre' => 'string',
            'status' => 'string',
            'submission_created_at' => 'string'
        ];
    }

    // public function filters(): array
    // {
    //     return [
    //         'status' => [
    //             'title' => __(self::STATUS),
    //             'width' => '250',
    //             'type' => 'dropdown',
    //             'options' => [
    //                 'pending' => __('submission::submission.pending'),
    //                 'reviewed' => __('submission::submission.reviewed'),
    //                 'accepted' => __('submission::submission.accepted'),
    //                 'rejected' => __('submission::submission.rejected')
    //             ]
    //         ],
    //         'submission_created_at' => [
    //             'title' => __(self::CREATED_AT),
    //             'width' => '250',
    //             'type' => 'daterange'
    //         ],
    //     ];
    // }

    public function getBulkAction(): array
    {
        return [
            // 'pending' => [
            //     'class' => 'warning',
            //     'title' => __('submission::submission.mark_pending'),
            //     'module' => 'Submission',
            //     'type' => 'pending'
            // ],
            // 'reviewed' => [
            //     'class' => 'info',
            //     'title' => __('submission::submission.mark_reviewed'),
            //     'module' => 'Submission',
            //     'type' => 'reviewed'
            // ],
            // 'accepted' => [
            //     'class' => 'success',
            //     'title' => __('submission::submission.mark_accepted'),
            //     'module' => 'Submission',
            //     'type' => 'accepted'
            // ],
            // 'rejected' => [
            //     'class' => 'danger',
            //     'title' => __('submission::submission.mark_rejected'),
            //     'module' => 'Submission',
            //     'type' => 'rejected'
            // ],
            'delete' => [
                'class' => 'danger',
                'title' => __('submission::submission.delete'),
                'module' => 'Submission',
                'type' => 'delete'
            ],
        ];
    }

    public function getColumnAuthorName(array $data): string
    {
        return '<div class="d-flex align-items-center">
                    <div>
                        <span class="fw-bold">' . $data['author_name'] . '</span>
                    </div>
                </div>';
    }

    public function getColumnEmail(array $data): string
    {
        return '<a href="mailto:' . $data['email'] . '" class="text-primary">' . $data['email'] . '</a>';
    }

    public function getColumnBookTitle(array $data): string
    {
        return '<div>
                    <span class="fw-bold">' . $data['book_title'] . '</span>
                    ' . ($data['genre'] ? '<br><small class="text-muted">' . $data['genre'] . '</small>' : '') . '
                </div>';
    }

    public function getColumnGenre(array $data): string
    {
        return $data['genre'] ? '<span class="badge bg-secondary">' . $data['genre'] . '</span>' : '-';
    }

    public function getColumnStatus(array $data): string
    {
        $badgeClass = match($data['status']) {
            'pending' => 'warning',
            'reviewed' => 'info',
            'accepted' => 'success',
            'rejected' => 'danger',
            default => 'secondary'
        };

        return '<span class="badge bg-' . $badgeClass . '">' . ucfirst($data['status']) . '</span>';
    }

    public function getColumnSubmissionCreatedAt(array $data)
    {
        return adminDateTimeFormatShow($data['submission_created_at']);
    }

    public function getColumnAction(array $data): string
    {
        $return = '';
        $user = Auth::user();

        if ($user->hasPermission('read-submission')) {
            $return .= '<a class="me-3" href="' . route('admin.submissions.show', encrypt($data['id'])) . '">
            <span class="inic inic-eye fs-18" data-bs-toggle="tooltip" title="View"></span></a>';
        }

        if ($user->hasPermission('delete-submission')) {
            $return .= '<a class="delete-grid-row" href="javascript:void(0)" data-grid="inic_grid_' . $this->uniqueID . '"
            data-url="' . route('admin.submissions.destroy', encrypt($data['id'])) . '">
            <span class="inic inic-bin fs-18" data-bs-toggle="tooltip" title="Delete"></span></a>';
        }

        return $return;
    }

    public function getColumnCheckbox(array $data): string
    {
        return '<input type="checkbox" value="' . $data['id'] . '" class="check_approval" />';
    }
}
