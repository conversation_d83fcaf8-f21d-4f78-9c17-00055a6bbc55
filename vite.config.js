import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    build: {
        chunkSizeWarningLimit: 1000,
      },
    plugins: [
        laravel({
            input: [

                'resources/admin/sass/app.scss',
                'resources/admin/js/jquery.js',
                'resources/admin/js/app.js',
                'resources/admin/css/app.css',

                'resources/front/sass/app.scss',
                'resources/front/js/app.js',
                'resources/front/css/app.css',
                'resources/front/css/style.css',

                'public/vendor/laravel-data-grid/js/laravel-data-grid.js',
                'public/vendor/jsvalidation/js/jsvalidation.js',

                //User phone number validation
                'node_modules/intl-tel-input/build/js/utils.js',


            ],
            refresh: true,
        }),
    ],
});
